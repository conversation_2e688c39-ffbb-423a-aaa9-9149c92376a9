# --------------------------------------------------------------------------------
# Company Name: Kavia AI
# Author: <PERSON><PERSON><PERSON>
# Creation Date: Year (2024)
#
# Confidential Information of Kavia AI
# NOTICE: All information contained herein is, and remains the property of Kavia AI.
# The intellectual and technical concepts contained herein are proprietary to Kavia AI
# and may be covered by U.S. and Foreign Patents, patents in process, and are protected by trade secret or copyright law.
# Dissemination of this information or reproduction of this material is strictly forbidden unless prior written permission is obtained
# from Kavia AI. Access to this file is granted on the condition that it will not be used for any purpose other than as specifically authorized
# in writing by Kavia AI, and subject to the terms of any agreement governing such access and use. Access to this file may also require
# a signed Non-Disclosure Agreement.
#
# --------------------------------------------------------------------------------


import asyncio
import time
from app.models.user_model import ProjectMember
from py2neo import Graph
from typing import Any, Dict, List
import json
from app.utils.cache_utils import async_ttl_cache
from app.utils.project_utils import camel_to_snake
import logging
from app.core.Settings import settings
from typing import List
from app.connection.tenant_middleware import get_tenant_id
from app.classes.S3Handler import S3Handler
from app.utils.datetime_utils import generate_timestamp


logger = logging.getLogger(__name__)




class NodeDB:
    def __init__(self, uri, user, password, database=settings.KAVIA_ROOT_TENANT_ID):
        self.uri = uri
        self.user = user
        self.password = password
        self.database = database.lower().replace('-','').replace('_','')

        # First connect to system database to check/create tenant database
        self.system_graph = Graph(uri, auth=(user, password), name="system")
        self._create_database_if_not_exists()

        # Now connect to the specified database
        self.graph = Graph(uri, auth=(user, password), name=self.database)
        self.update_logger = logging.getLogger(__name__)  
        

    def _create_database_if_not_exists(self):
        try:
            # Check if database exists
            result = self.system_graph.run("SHOW DATABASES").data()
            existing_dbs = [record["name"] for record in result]
            
            if self.database not in existing_dbs:
                # Create new database if it doesn't exist
                create_result = self.system_graph.run(f"CREATE DATABASE {self.database}")
                if not create_result:
                    raise Exception(f"Failed to create database: {self.database}")
                print(f"Created new database: {self.database}")
                
                # Add a delay to ensure database is ready before continuing
                time.sleep(2)
                
                # Enable CDC with full transaction log enrichment
                self.system_graph.run(f'ALTER DATABASE {self.database} SET OPTION txLogEnrichment "FULL"')
                print(f"Enabled CDC with full transaction log enrichment for database: {self.database}")
                
                # Connect to the newly created database
                temp_graph = Graph(self.uri, auth=(self.user, self.password), name=self.database)
                
                # Create a dummy node to skip id_0
                temp_graph.run("CREATE (:DummyNode {_dummy: true})")
                print("Created dummy node to skip id_0")
                
                # Verify database exists before proceeding
                verify_result = self.system_graph.run("SHOW DATABASES").data()
                verify_dbs = [record["name"] for record in verify_result]
                if self.database not in verify_dbs:
                    raise Exception(f"Database {self.database} still not found after creation attempt")
        except Exception as e:
            error_msg = f"Error checking/creating database: {str(e)}"
            print(error_msg)
            # Re-raise the exception to prevent continuing with an invalid database
            raise Exception(error_msg)
        
    async def async_run(self, query, **params):
        retries = 3  # Number of retries
        base_delay = 1  # Base delay in seconds
        for attempt in range(retries):
            try:
                result = await asyncio.to_thread(self.graph.run, query, **params)
                
                return result  # Return the result on successful execution
            except Exception as e:
                print(f"Error running query on attempt {attempt+1}: {e}")
                
                # Handle database not found error
                if "DatabaseNotFound" in str(e):
                    print(f"Database {self.database} not found. Attempting to create it.")
                    try:
                        # Reconnect to system database
                        self.system_graph = Graph(self.uri, auth=(self.user, self.password), name="system")
                        # Try to create the database
                        self._create_database_if_not_exists()
                        # Reconnect to the newly created database
                        self.graph = Graph(self.uri, auth=(self.user, self.password), name=self.database)
                    except Exception as create_error:
                        print(f"Failed to create database: {create_error}")
                        # If we can't create the database, no point in retrying
                        raise e
                elif "Cannot connect" in str(e):  # Connection issues
                    print("Re-initializing graph connection")
                    self.graph = Graph(self.uri, auth=(self.user, self.password), name=self.database)
                    
                if attempt < retries - 1:
                    sleep_time = base_delay * (2 ** attempt)  # Exponential backoff
                    print(f"Retrying in {sleep_time} seconds...")
                    await asyncio.sleep(sleep_time)
                else:
                    print("All retry attempts failed.")
                    raise e  # Optionally re-raise the last exception after all retries fail

        return None  # You can decide what to return if all retries fail, if anything at all

    def run_query(self, query, **params):
        retries = 3
        base_delay = 1
        for attempt in range(retries):
            try:
                result = self.graph.run(query, **params)
                return result
            except Exception as e:
                print(f"Error running query on attempt {attempt+1}: {e}")
                if "Cannot connect" in str(e):
                    print("Re-initializing graph connection")
                    self.graph = Graph(self.uri, auth=(self.user, self.password))
                if attempt < retries - 1:
                    sleep_time = base_delay * (2 ** attempt)
                    print(f"Retrying in {sleep_time} seconds...")
                    time.sleep(sleep_time)
                else:
                    print("All retry attempts failed.")
                    raise e
        return None

    async def get_all_relationships(self, root_node_id, relationship_type, node_type=None, levels=1):
        """
        Retrieves all relationships of a given type starting from a root node, up to the specified number of levels.

        :param root_node_id: The ID of the root node.
        :param relationship_type: The type of the relationship to retrieve.
        :param node_type: The type of the nodes to retrieve (optional).
        :param levels: The maximum number of levels to traverse.
        :return: A list of all related nodes and their relationships.
        """
        if node_type:
            node_type_string = f":{node_type}"
        else:
            node_type_string = ""

        # Using parameterization for root_node_id and constructing the query safely for levels and relationship_type
        query = f"""
            MATCH (p) WHERE ID(p) = $root_node_id
            MATCH (p)-[:HAS_CHILD*1..{levels}]->(a1{node_type_string})-[r:{relationship_type}]-(a2{node_type_string})
            RETURN ID(a1) AS StartNodeID, type(r) AS type, properties(r) as properties, ID(a2) AS EndNodeID
        """
        try:
            query_result = await self.async_run(query, root_node_id=root_node_id)
            return query_result.data()
        except Exception as e:
            print(f"Failed to execute query: {e}")
            return []
        
    async def get_relationships_involving_node(self, node_id, relationship_type):
        """
        Retrieves all relationships of a given type where the specified node is either a source or a target.

        :param node_id: The ID of the node involved in the relationships.
        :param relationship_type: The type of the relationship to retrieve.
        :return: A list of relationships involving the specified node.
        """

        # Parameterized query to find all relationships where node_id is either source or target
        query = f"""
            MATCH (a1)-[r:{relationship_type}]->(a2)
            WHERE ID(a1) = $node_id OR ID(a2) = $node_id
            RETURN ID(a1) AS source, type(r) AS type, properties(r) as properties, ID(a2) AS target
        """
        try:
            query_result = await self.async_run(query, node_id=node_id)
            return query_result.data()
        except Exception as e:
            print(f"Failed to execute query: {e}")
            return []


    async def get_relationship(self, source, target, relationship_type):
        query = f"""
        MATCH (n)-[r:{relationship_type}]->(m)
        WHERE ID(n) = $source AND ID(m) = $target
        RETURN ID(n) AS source, ID(m) AS target, type(r) AS type, properties(r) AS properties
        """

        query_result = await self.async_run(query, source=source, target=target)

        result = query_result.data()
        if result and len(result) > 0:
            return result[0]
        else:
            return None
    
    async def create_unique_child_node(self, parent_id, child_type, child_properties):
        query = """
        MATCH (p)
        WHERE ID(p) = $parent_id
        OPTIONAL MATCH (p)-[:HAS_CHILD]->(existing)
        WHERE labels(existing) = [$child_type]
        WITH p, existing
        WHERE existing IS NULL
        CREATE (p)-[:HAS_CHILD]->(c:$child_type $child_properties)
        RETURN ID(c) as id, properties(c) as properties
        """
        params = {
            "parent_id": parent_id,
            "child_type": child_type,
            "child_properties": child_properties
        }
        query_result = await self.async_run(query, **params)
        result = query_result.data()
        return result[0] if result else None


    async def relationship_exists(self, start_node_id, end_node_id, relationship_type):
        """
        Checks if a relationship of a given type exists between two nodes.

        :param start_node_id: The ID of the starting node.
        :param end_node_id: The ID of the ending node.
        :param relationship_type: The type of the relationship to check for.
        :return: True if the relationship exists, False otherwise.
        """
        query = f"""
        MATCH (startNode)-[r:{relationship_type}]->(endNode)
        WHERE ID(startNode) = $start_node_id AND ID(endNode) = $end_node_id
        RETURN COUNT(r) > 0 AS relationshipExists
        """
        query_result = await self.async_run(query, start_node_id=start_node_id, end_node_id=end_node_id)
        result = query_result.data()

        # Assuming the query always returns a result, and the 'relationshipExists' key exists in the result.
        return result[0]['relationshipExists']
 
    # create a db node with api_data as a dictionary for attributes
    async def create_node(self, node_types, properties, parent_id=None):
        labels = ":".join(filter(None, node_types))  # Prepare the labels string
        if 'repository_details' in properties:
            repo_details = properties['repository_details'].copy()
            properties['repository_details'] = json.dumps(properties['repository_details'])
            
        # Add is_active = True for Project nodes
        if "Project" in node_types:
            properties["is_active"] = True
        
        # Dynamically construct the SET part of the query to set properties as attributes
        set_clauses = ", ".join([f"n.{key} = ${key}" for key in properties.keys()])
        query = f"CREATE (n:{labels}) SET {set_clauses} RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_result = await self.async_run(query, **properties)

        result = query_result.data()[0]            
            
        #TODO: HOLD THE LANGTRACE PROJECT CREATION FOR NOW
        # langtrace_enabled = os.getenv("LANGTRACE_ENABLED", "False").lower() == "true"
        
        # if "Project" in node_types and langtrace_enabled:  
        #     await fast_api_task(result['id'] ,properties)
        
        
        # Create the relationship if a parent_id is provided
        if parent_id is not None:
            await self.create_relationship(parent_id, result['id'], "HAS_CHILD")

        return result
    
    async def add_label_to_node(self, node_id, new_label):
        # This query fetches the existing labels of the node and adds the new label if it is not already there
        query = f"MATCH (n) WHERE ID(n) = $node_id RETURN LABELS(n) AS existing_labels"
        query_result = await self.async_run(query, node_id=node_id)
        labels = query_result.data()[0]['existing_labels']

        # Check if the new label is already part of the node's labels
        if new_label not in labels:
            updated_labels = ":".join(labels + [new_label])
            # Update the node with the new label added
            update_query = f"MATCH (n) WHERE ID(n) = $node_id SET n:{updated_labels} RETURN LABELS(n) AS new_labels"
            update_result = await self.async_run(update_query, node_id=node_id)
            labels = update_result.data()[0]['new_labels']

        return labels  # Return the updated label list for the node


    # create multiple nodes at once (optimize later to use a single query)
    async def create_nodes_old(self, node_types, propertieslist, parent_id=None):
        # Create a list of coroutine objects for each call to create_node
        tasks = [self.create_node(node_types, properties, parent_id) for properties in propertieslist]
        
        # Run all the create_node coroutine tasks concurrently and wait for all to complete
        results = await asyncio.gather(*tasks)
        
        return results
    
    async def get_project_id(self, node_id):
        query = """
        MATCH (p:Project)-[:HAS_CHILD*]->(n)
        WHERE ID(n) = $node_id
        RETURN ID(p) as project_id
        """
        result = await self.async_run(query, node_id=node_id)
        return result.data()[0]['project_id'] if result else None

    async def create_relationship(self, source_id, target_id, relationship_type, properties=None):
        properties = properties or {}
        query = f"""
        MATCH (source), (target)
        WHERE ID(source) = $source_id AND ID(target) = $target_id
        CREATE (source)-[r:{relationship_type} $properties]->(target)
        RETURN type(r) AS type
        """
        await self.async_run(query, source_id=source_id, target_id=target_id, properties=properties)

    def get_db_labels_for_type(self,type=None):
        """
        Returns a list of labels to be used in the database for a given Type.
        If the Type is not in the mapping, it returns the Type itself as a single-item list.

        Args:
        type_name (str): The Type of the node

        Returns:
        list: A list of labels to be used in the database
        """
        label_mappings = {
            "Project": ["Project"],
            "RequirementRoot": ["RequirementRoot", "Requirement"],
            "Epic": ["Epic", "Requirement"],
            "UserStory": ["UserStory", "Requirement"],
            "Task": ["Task", "Requirement"],
            "Requirement": ["Requirement"],
            "ArchitecturalRequirement": ["ArchitecturalRequirement", "Requirement"],
            "ArchitectureRoot": ["ArchitectureRoot", "Architecture"],
            "Architecture": ["Architecture"],
            "ArchitectureLeaf": ["ArchitectureLeaf", "Architecture"],
            "Component": ["Component", "Architecture"],
            "SubComponent": ["SubComponent", "Architecture"],
            "Design": ["Design"],
            "Interface": ["Interface"],
            "Method": ["Method", "InterfaceElement"],
            "HttpRoute": ["HttpRoute", "InterfaceElement"],
            "DataContract": ["DataContract", "InterfaceElement"],
            "CommunicationProtocol": ["CommunicationProtocol", "InterfaceElement"],
            "DesignSpecification": ["DesignSpecification", "Design"],
            "Algorithm": ["Algorithm", "DesignElement"],
            "StateLogic": ["StateLogic", "DesignElement"],
            "WorkItemRoot": ["WorkItemRoot", "WorkItem"],
            "WorkItem": ["WorkItem"],
            "Dependency": ["Dependency"],
            "ClassDiagram": ["ClassDiagram", "Diagram"],
            "SequenceDiagram": ["SequenceDiagram", "Diagram"],
            "StateMachineDiagram": ["StateMachineDiagram", "Diagram"],
            "UnitTest": ["UnitTest", "Test"],
            "IntegrationTest": ["IntegrationTest", "Test"],
            "PerformanceTest": ["PerformanceTest", "Test"],
            "RobustnessTest": ["RobustnessTest", "Test"],
            "APIDoc": ["APIDoc", "Documentation"],
            "User": ["User"],
            "FunctionalRequirement": ["FunctionalRequirement"],
            "NonFunctionalRequirement": ["NonFunctionalRequirement"],
            "FunctionalTestCase": ["FunctionalTestCase", "Test"],
            "NonFunctionalTestCase": ["NonFunctionalTestCase", "Test"],
            "StressTest": ["StressTest", "Test"],
            "StabilityTest": ["StabilityTest", "Test"],
            "InteroperabilityTest": ["InteroperabilityTest", "Test"]
        }

        return label_mappings.get(type, [type])

    async def create_node_with_type(self, node_types, properties, parent_id=None):
        # Translate node types to database labels
        labels = self.get_db_labels_for_type(node_types)
        labels_string = ":".join(filter(None, labels))

        # Dynamically construct the SET part of the query to set properties as attributes
        set_clauses = ", ".join([f"n.{key} = ${key}" for key in properties.keys()])
        query = f"CREATE (n:{labels_string}) SET {set_clauses} RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_result = await self.async_run(query, **properties)

        result = query_result.data()[0]

        # Create the relationship if a parent_id is provided
        if parent_id is not None:
            await self.create_relationship(parent_id, result['id'], "HAS_CHILD")

        return result
    
    async def create_nodes(self, node_types, properties_list, parent_id=None):
        """
        Creates multiple nodes at once.
        """
        if not properties_list:
            return []

        try:
            nodes_to_create = []
            for props in properties_list:
                # Get labels based on Type property first
                if isinstance(props,dict):
                    type_value = props.get('Type')
                else:
                    type_value = None

                if type_value:
                    # Use only the labels from the mapping for this specific Type
                    labels = self.get_db_labels_for_type(type_value)
                else:
                    # If no Type specified, use the passed in node_types
                    labels = node_types if isinstance(node_types, list) else [node_types]

                # Keep labels as an array instead of joining them
                nodes_to_create.append({
                    'labels': list(set(labels)),  # Keep as array, just deduplicate
                    'properties': props
                })

            query = """
            UNWIND $nodes as node_data
            CALL apoc.create.node(node_data.labels, node_data.properties) YIELD node
            """

            if parent_id is not None:
                query += """
                WITH node
                MATCH (parent) WHERE ID(parent) = $parent_id
                CREATE (parent)-[:HAS_CHILD]->(node)
                """

            query += """
            RETURN ID(node) as id, labels(node) as labels, properties(node) as properties
            """

            result = await self.async_run(query, nodes=nodes_to_create, parent_id=parent_id)
            return result.data()

        except Exception as e:
            self.update_logger.error(f"Error in create_nodes: {str(e)}")
            raise
    #     # Execute the query with the parameters
    #     query_result = await self.async_run(query, **params)
    #     results = query_result.data()
    #     return results
    
    async def create_nodes_with_types_old(self, properties_list, parent_id=None):
        if not properties_list:
            return []  # No properties to process

        # Prepare the Cypher query using UNWIND
        if parent_id is not None:
            query = f"""
            MATCH (p) WHERE ID(p) = $parent_id
            UNWIND $properties_list AS props
            CREATE (n:{'{props.Type}'})
            REMOVE n.Type
            SET n += props
            CREATE (p)-[r:HAS_CHILD]->(n)
            RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties, ID(r) AS rel_id
            """
            params = {'properties_list': properties_list, 'parent_id': parent_id}
        else:
            query = f"""
            UNWIND $properties_list AS props
            CREATE (n:{'{props.Type}'})
            REMOVE n.Type
            SET n += props
            RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
            """
            params = {'properties_list': properties_list}

        # Execute the query
        query_result = await self.async_run(query, **params)
        results = query_result.data()
        return results
    

    async def create_nodes_with_types(self, properties_list, parent_id=None):
        if not properties_list:
            return []  # No properties to process

        params = {'properties_list': properties_list}
        if parent_id is not None:
            query = f"""
            UNWIND $properties_list AS props
            MATCH (p) WHERE ID(p) = $parent_id
            CALL apoc.create.node([props.Type], props) YIELD node AS n
            REMOVE n.Type
            CREATE (p)-[r:HAS_CHILD]->(n)
            RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties, ID(r) AS rel_id
            """
            params['parent_id'] = parent_id  # Add parent_id to the parameters
        else:
            query = f"""
            UNWIND $properties_list AS props
            CALL apoc.create.node([props.Type], props) YIELD node AS n
            REMOVE n.Type
            RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
            """
            params['parent_id'] = None  # Explicitly set parent_id to None

        # Execute the query
        results = await self.async_run(query, **params)
        return results.data()


    
    # create a relationship between two nodes using ids and relationship_type
    async def create_relationship(self, start_node_id, end_node_id, relationship_type, properties=None):
        properties = properties if isinstance(properties, dict) else {}
        
        # Dynamically construct the SET part of the query for properties
        set_clauses = ", ".join([f"r.{key} = ${key}" for key in properties.keys()])
        query = f"""
        MATCH (n), (m)
        WHERE ID(n) = $start_node_id AND ID(m) = $end_node_id
        CREATE (n)-[r:{relationship_type}]->(m)
        {f"SET {set_clauses}" if set_clauses else ""}
        RETURN type(r) AS relationship_type, properties(r) AS properties
        """

        query_result = await self.async_run(query, start_node_id=start_node_id, end_node_id=end_node_id, **properties)
        result = query_result.data()
        
        if not result:
            return None
        
        return result[0]

    async def update_interfaces_with_relationship(self, source_id, target_id, properties):
        query = """
        MATCH (source)-[r:INTERFACES_WITH]->(target)
        WHERE ID(source) = $source_id AND ID(target) = $target_id
        SET r += $properties
        RETURN r
        """
        params = {
            "source_id": source_id,
            "target_id": target_id,
            "properties": properties
        }
        result = await self.async_run(query, **params)
        return result.data()

    
    async def update_relationship_properties(self, source, target, relationship_type, updated_properties):

        query = f"""
        MATCH (n)-[r:{relationship_type}]->(m)
        WHERE ID(n) = $source AND ID(m) = $target
        SET r += $updated_properties
        RETURN type(r) AS relationship_type, properties(r) AS properties
        """

        query_result = await self.async_run(query, source=source, target=target, updated_properties=updated_properties)
        result = query_result.data()

        if not result:
            return None

        return result[0]
    
    async def get_shared_interfaces(self, node_id):
        """Get all interfaces where the node is either source or target and their configuration states."""
        query = """
        MATCH (n)-[r:INTERFACES_WITH]-(other:Architecture)
        WHERE ID(n) = $node_id 
        AND other.Type IN ['Component', 'SubComponent']
        RETURN ID(other) as other_id, 
            properties(other) as other_properties,
            LABELS(other) as other_labels,
            CASE WHEN ID(startNode(r)) = $node_id THEN 'source' ELSE 'target' END as role,
            properties(r) as interface_properties
        """
        result = await self.async_run(query, node_id=node_id)
        return result.data()

    async def create_interface_node_for_ancestor(self, source_id, target_id, interface_properties):
        """Create an interface node under the closest shared ancestor of two components."""
        # First find shared ancestor
        shared_ancestor = await self.get_shared_ancestor(source_id, target_id)
        if not shared_ancestor:
            return None
        
        # Create interface node
        interface_node = await self.create_node(
            ["Interface"], 
            interface_properties,
            shared_ancestor['id']
        )
        
        # Update the INTERFACES_WITH relationship
        await self.update_relationship_properties(
            source_id, 
            target_id, 
            "INTERFACES_WITH", 
            {"interface_node_id": interface_node['id']}
        )
        
        return interface_node

    async def update_interface_configuration_state(self, source_id, target_id, state):
        """Update the configuration state of an interface relationship."""
        return await self.update_relationship_properties(
            source_id,
            target_id,
            "INTERFACES_WITH",
            {"design_details_state": state}
        )

    async def get_shared_ancestor(self, node_id1, node_id2):
        # Function to find the ancestors of a given node
        async def find_ancestors(node_id):
            query = """
            MATCH (ancestor)-[:HAS_CHILD*]->(n)
            WHERE ID(n) = $node_id
            RETURN collect(ID(ancestor)) AS ancestors
            """
            query_result = await self.async_run(query, node_id=node_id)
            result = query_result.data()[0]
            return set(result['ancestors'])

        # Find ancestors for both nodes
        ancestors1 = await find_ancestors(node_id1)
        ancestors2 = await find_ancestors(node_id2)

        # Find common ancestors
        common_ancestors = ancestors1.intersection(ancestors2)

        if not common_ancestors:
            return None  # No common ancestor found

        # Function to find the closest shared ancestor to node_id1
        async def find_closest_ancestor(common_ancestors):
            query = """
            UNWIND $common_ancestors AS ancestor_id
            MATCH (ancestor) WHERE ID(ancestor) = ancestor_id
            OPTIONAL MATCH path=(ancestor)-[:HAS_CHILD*]->(n1) WHERE ID(n1) = $node_id1
            RETURN ID(ancestor) AS id, LABELS(ancestor) AS labels, properties(ancestor) AS properties, 
                CASE WHEN path IS NOT NULL THEN length(path) ELSE 0 END AS depth
            ORDER BY depth ASC
            LIMIT 1
            """
            query_result = await self.async_run(query, common_ancestors=list(common_ancestors), node_id1=node_id1)
            result = query_result.data()[0]
            return result

        # Get the closest shared ancestor
        closest_ancestor = await find_closest_ancestor(common_ancestors)

        return closest_ancestor







    async def update_dependencies(self, node_id, dependencies):
        query = """
        MATCH (n)
        WHERE ID(n) = $node_id
        UNWIND $dependencies AS dependency_id
        MATCH (m)
        WHERE ID(m) = dependency_id
        MERGE (n)-[r:HAS_DEPENDENCY]->(m)
        """
        await self.async_run(query, node_id=node_id, dependencies=dependencies)
        
    @async_ttl_cache(ttl=0.01)
    async def get_node_properties_by_id(self, node_id, node_type=None):
        match_label = "(n)"
        if node_type:
            match_label = f"(n:{node_type})"
        
        query = f"MATCH {match_label} WHERE ID(n) = $node_id RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_result = await self.async_run(query, node_id=node_id)

        result = query_result.data()
        if result and len(result) > 0:
            return result[0]
        return None
        
    @async_ttl_cache(ttl=0.01)
    async def get_node_by_id(self, node_id, node_type = None):
        match_label = "(n)"
        if node_type:
            match_label = f"(n:{node_type})"
            
        query = f"""
        MATCH {match_label} 
        WHERE ID(n) = $node_id 
        RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
        """
        
        if node_type == "Project":
            query += ", n.is_active AS is_active"
        
        query_result = await self.async_run(query, node_id=node_id)

        result = query_result.data()
        if result and len(result) > 0:
            result = result[0]
            if node_type == "Project":
                if result.get('is_active') == True:
                    return result
                else:
                    return None  # Return None if the project is not active
            return result
        return None
    
    async def get_node_by_label_id(self, node_id, node_type):
        query = f"MATCH (n:{node_type}) WHERE ID(n) = $node_id RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_result = await self.async_run(query, node_id=node_id)
        result = query_result.data()
        if result and len(result) > 0:
            return result[0]
        return []
    
    async def get_node_based_on_data_model(self, node_id: int, node_type: str, data_model: dict, exclude_relationships: bool = False  ,skip_ui_metadata: bool = False):
        ui_metadata_look_up = {}
        node_info = data_model['model'][node_type]
        required_attributes = node_info.get('attributes', {})
        relationships = node_info.get('relationships', {})
        ui_metadata_look_up[node_type] = node_info.get('ui_metadata', {})
        # Query to fetch node attributes
        node_query = f"""
        MATCH (n:{node_type})
        WHERE ID(n) = $node_id
        {' AND (n.is_active = true OR NOT "Project" IN labels(n))' if node_type == 'Project' else ''}
        RETURN n
        """
        node_result = await self.async_run(node_query, node_id=node_id)
        node_data = node_result.data()

        if not node_data:
            return {}

        node = node_data[0]['n']
        
        try:
            labels = node.labels
            for label in labels:
                if label not in ui_metadata_look_up:
                    ui_metadata_look_up[label] = node_info.get('ui_metadata', {})
        except:
            pass

        node_properties = dict(node)

        # Filter properties based on required_attributes
        filtered_properties = {k: v for k, v in node_properties.items() if k in required_attributes}
        filtered_properties['id'] = node_id
        # Fetch relationships
        relationship_data = {}
        
        if exclude_relationships and skip_ui_metadata:
            return {
                'node': filtered_properties
            }
        
        if exclude_relationships:
            return {
                'node': filtered_properties,
                'ui_metadata': ui_metadata_look_up
            }
            
        for rel_name, rel_info in relationships.items():
            neo4j_rel_name = camel_to_snake(rel_name)
            allowed_types = rel_info.get('types', [])
            
            type_condition = " OR ".join([f"'{t}' IN labels(related)" for t in allowed_types])
            rel_query = f"""
            MATCH (n:{node_type})-[r:{neo4j_rel_name}]->(related)
            WHERE ID(n) = $node_id AND ({type_condition})
            RETURN ID(related) as id, labels(related) as labels, properties(related) as properties, type(r) as relationship_type
            """
            rel_result = await self.async_run(rel_query, node_id=node_id)
            rel_data = rel_result.data()

            relationship_data[rel_name] = []
            for related_node in rel_data:
                related_node_properties = related_node['properties']
                
                # Filter related node properties based on its type in the data model
                if related_node_properties.get("Type") in data_model['model']:

                    try:
                        for label in related_node['labels']:
                            ui_metadata_look_up[label] = data_model['model'][label].get("ui_metadata")
                    except:
                        pass

                    if related_node_properties.get("Type") not in ui_metadata_look_up:
                        ui_metadata_look_up[related_node_properties.get("Type")] = data_model['model'][related_node_properties.get("Type")].get('ui_metadata', {})
                        
                    related_node_attributes = data_model['model'][related_node_properties.get("Type")].get('attributes', {})
                    filtered_related_properties = {k: v for k, v in related_node_properties.items() if k in related_node_attributes}
                    filtered_related_properties['id'] = related_node['id']
                else:
                    filtered_related_properties = related_node_properties

                relationship_data[rel_name].append({
                    'node': filtered_related_properties,
                    'relationship_type': related_node['relationship_type']
                })
                
        if skip_ui_metadata:
            return {
                'node': filtered_properties,
                'relationships': relationship_data
            }

        return {
            'node': filtered_properties,
            'relationships': relationship_data,
            'ui_metadata': ui_metadata_look_up
        }
        
    async def get_edge_by_id(self, edge_id, edge_type):
        query = f"MATCH ()-[r:{edge_type}]->() WHERE ID(r) = {edge_id} RETURN ID(r) AS id, properties(r) AS properties"
        query_result = await self.async_run(query)
        result = query_result.data()
        if result and len(result) > 0:
            return result[0]
        return None
    
    async def get_nodes_in_path(self, end_node_id, start_node_type):
        query = f"""
        MATCH path=(n:{start_node_type})-[:HAS_CHILD*]->(m)
        WHERE ID(m) = $end_node_id
        UNWIND nodes(path) AS node
        RETURN ID(node) AS id, LABELS(node) AS labels,  properties(node) AS properties
        """
        query_result = await self.async_run(query, end_node_id=end_node_id)
        result = query_result.data()
        return result
        
    async def get_nodes_by_ids_and_label(self, id_list, node_type=None):
        # Convert Python list to a list that can be passed to Cypher query
        match_label = "(n)"
        if node_type:
            match_label = f"(n:{node_type})"

        query = f"MATCH {match_label} WHERE ID(n) IN $id_list RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_results = await self.async_run(query, id_list=id_list)
        results = query_results.data()
        # No need to check if results is not empty, as an empty result is valid when no nodes match the IDs
        return results

    async def get_nodes_by_ids(self, id_list):
        # Convert Python list to a list that can be passed to Cypher query
        query = f"MATCH (n) WHERE ID(n) IN $id_list RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_results = await self.async_run(query, id_list=id_list)
        results = query_results.data()
        # No need to check if results is not empty, as an empty result is valid when no nodes match the IDs
        return results

    async def get_node_by_property(self, node_type, property_name, property_value):
        query = f"MATCH (n:{node_type}) WHERE n.{property_name} = $property_value RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_result = await self.async_run(query, property_value=property_value)
        result = query_result.data()
        if result and len(result) > 0:
            return result[0]
    
    async def get_nodes_by_property(self, node_type, property_name, property_value):
        query = f"MATCH (n:{node_type}) WHERE n.{property_name} = $property_value RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_result = await self.async_run(query, property_value=property_value)
        result = query_result.data()
        return result
    
    async def get_node_by_label(self, node_type: str):
        query = f"""
            MATCH (n:{node_type})
            WHERE '{node_type}' IN LABELS(n)
            RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        if result:
            return result[0]  # Return the first matching node
        return None  # Return None if no matching node is found
    
    async def get_nodes_by_label(self, node_type):
        query = f"""
            MATCH (n:{node_type})
            RETURN ID(n) AS id, LABELS(n) AS labels,  properties(n) AS properties
        """
        query_result = await self.async_run(query)  # This will be a list of dictionaries with keys 'id', 'labels', and 'properties'
        
        result = query_result.data()
        return result
    
    async def get_nodes_connected_by_relationship(self, start_node_id, relationship_type, end_node_type):
        query = f"MATCH (n)-[r:{relationship_type}]-(m:{end_node_type}) WHERE ID(n) = {start_node_id} RETURN ID(m) AS id, LABELS(m) AS labels, properties(m) AS properties"
        query_result = await self.async_run(query)
        result = query_result.data()
        return result

    
    async def get_connected_nodes(self, start_node_id, end_node_type):
        query = f"MATCH (n)-[r]-(m:{end_node_type}) WHERE ID(n) = $start_node_id RETURN ID(m) AS id, LABELS(m) AS labels, properties(m) AS properties"
        query_result = await self.async_run(query, start_node_id=start_node_id)
        result = query_result.data()
        return result
    
    async def get_nodes_in_reverse_direction(self, end_node_id, relationship_type, start_node_type):
        query = f"MATCH (n:{start_node_type})-[r:{relationship_type}]-(m) WHERE ID(m) = {end_node_id} RETURN ID(m) AS id, LABELS(m) AS labels, properties(m) AS properties"
        query_result = await self.async_run(query)
        result = query_result.data()
        return result
    
    async def get_nodes_connected_by_multiple_hops(self, start_node_id, relationship_type, end_node_type, max_hops):
        query = (f"MATCH (n)-[:{relationship_type}*0..{max_hops}]->(m:{end_node_type}) "
                f"WHERE ID(n) = {start_node_id} "
                f"RETURN ID(m) AS id, LABELS(m) AS labels, properties(m) AS properties")
        query_result = await self.async_run(query)
        result = query_result.data()
        return result
    
    async def get_root_node(self, node_id):
        query = f"MATCH (n)-[:HAS_CHILD*]->(m) WHERE ID(m) = {node_id} RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_result = await self.async_run(query)
        result = query_result.data()
        if result and len(result) > 0:
            return result[-1]
        
    async def get_parent_node(self, child_node_id):
        query = f"MATCH (n)-[:HAS_CHILD]->(m) WHERE ID(m) = $child_node_id RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        query_result = await self.async_run(query, child_node_id=child_node_id)
        result = query_result.data()
        if result and len(result) > 0:
            return result[0]

    async def get_node_by_title(self, node_type, title):
        query = f"""
        MATCH (n:{node_type}) 
        WHERE n.Title = $title
        RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
        """
        query_result = await self.async_run(query, title=title)
        result = query_result.data()
        if result and len(result) > 0:
            return result[0]
        return None
    
    async def get_child_nodes(self, parent_node_id, child_node_type=None):
        if child_node_type:
            query = f"MATCH (n)-[:HAS_CHILD]->(m:{child_node_type}) WHERE ID(n) = $parent_node_id RETURN ID(m) AS id, LABELS(m) AS labels, properties(m) AS properties"
        else:
            query = f"MATCH (n)-[:HAS_CHILD]->(m) WHERE ID(n) = $parent_node_id RETURN ID(m) AS id, LABELS(m) AS labels, properties(m) AS properties"
        query_result = await self.async_run(query, parent_node_id=parent_node_id)
        result = query_result.data()
        return result

    
    async def get_sibling_nodes(self, node_id, node_type=None):
        query = """
            MATCH (parent)-[:HAS_CHILD]->(givenNode)
            WHERE ID(givenNode) = $node_id
            MATCH (parent)-[:HAS_CHILD]->(sibling)
            WHERE ID(sibling) <> $node_id
        """
        
        # Optionally filter by the sibling node type if provided.
        if node_type:
            query += f"AND '{node_type}' in labels(sibling)"
            
        query += "RETURN ID(sibling) AS id, LABELS(sibling) AS labels, properties(sibling) AS properties"
        
        query_result = await self.async_run(query, node_id=node_id)
        result = query_result.data()
        return result


    
    async def get_node_tree(self, node_id, node_type):
        query = (
            "MATCH path=(root:{node_type})-[:HAS_CHILD*0..]->(child:{node_type}) "
            "WHERE ID(root) = $node_id "
            "WITH collect(path) AS paths "
            "CALL apoc.convert.toTree(paths) YIELD value "
            "RETURN value"
        ).format(node_type=node_type)  # Dynamically insert the node_type into the query string

        query_result = await self.async_run(query, node_id=node_id)
        result = query_result.data()
        return result[0]['value']
    

    async def delete_node(self, node_id):

        # self.pc_index.delete([str(node_id)])  # Delete node from PC index

        query = f"MATCH (n) WHERE ID(n) = $node_id DETACH DELETE n"
        await self.async_run(query, node_id=node_id)
    
        return True
    
    async def delete_node_by_filter(self, node_type, property_name, property_value):
        query = f"MATCH (n:{node_type}) WHERE n.{property_name} = $property_value DETACH DELETE n"
        await self.async_run(query, property_value=property_value)

    async def delete_nodes_by_label(self, labels):
        # Convert labels to a list if it's a single label
        if isinstance(labels, str):
            labels = [labels]
        
        # Build the label condition for the Cypher query
        label_condition = " OR ".join([f"'{label}' IN LABELS(n)" for label in labels])
        
        # Check if nodes with the specified labels exist
        query = f"MATCH (n) WHERE {label_condition} RETURN COUNT(n) AS count"
        result = await self.async_run(query)
        count = result.data()[0]['count']
        
        if count == 0:
            return "No available nodes to delete."
        
        # Delete nodes from the database
        query = f"MATCH (n) WHERE {label_condition} DETACH DELETE n"
        await self.async_run(query)
        
        return "Nodes deleted successfully."

    async def get_nodes_in_path(self, end_node_id, start_node_type):
        query = f"""
        MATCH path=(n:{start_node_type})-[:HAS_CHILD*]->(m)
        WHERE ID(m) = $end_node_id
        UNWIND nodes(path) AS node
        RETURN ID(node) AS id, LABELS(node) AS labels, properties(node) AS properties
        """
        query_result = await self.async_run(query, end_node_id=end_node_id)
        result = query_result.data()
        return result


    async def get_associated_item(self, node_id, type_1, type_2, relationship_type):
        query = f'''
            MATCH (p:{type_1})-[:{relationship_type}]->(prod:{type_2})
            WHERE ID(p) = {node_id}
            RETURN ID(prod) AS ProductID, prod.Name AS Name, prod.Description AS Description
        '''
        print(query)
        query_result = await self.async_run(query)
        result = query_result.data()
        return result

    def get_likely_associated_products(self,):
        
        # return empty list if no likely associated products are found
        return []


    async def delete_node_recursively_by_relationship(self, start_node_id, relationship_type):
        # Query to find all nodes (and the start node) that will be deleted.
        find_nodes_query = f"""
        MATCH (n)-[r:{relationship_type}*]->(m) 
        WHERE ID(n) = {start_node_id} 
        RETURN DISTINCT ID(m) AS NodeID 
        UNION
        MATCH (n)
        WHERE ID(n) = {start_node_id}
        RETURN ID(n) AS NodeID
        """
        
        # Execute the query to find nodes
        result = await self.async_run(find_nodes_query)
        
        # Collecting all node IDs that will be deleted
        deleted_node_ids = [record["NodeID"] for record in result]

        if deleted_node_ids:
            # Convert list of IDs into a string to use in Cypher query
            ids_string = ",".join(map(str, deleted_node_ids))

            # Delete nodes by their IDs directly
            delete_nodes_query = f"MATCH (n) WHERE ID(n) IN [{ids_string}] DETACH DELETE n"
            await self.async_run(delete_nodes_query)

        return deleted_node_ids
    
    async def delete_associates_and_deactivate_project(self, project_id, relationships):
        # Query to find all nodes connected by specified relationships
        find_nodes_query = f"""
        MATCH (n:Project) WHERE ID(n) = {project_id}
        OPTIONAL MATCH (n)-[r:{' | '.join(relationships)}*]->(m)
        WHERE NOT m:Project
        RETURN DISTINCT ID(n) AS ProjectID, COLLECT(DISTINCT ID(m)) AS AssociatedNodeIDs
        """
        # Execute the query to find nodes
        result = await self.async_run(find_nodes_query)
        data = result.data()

        if not data:
            return []  # No nodes found

        # Collecting all node IDs that will be deleted
        node_ids_to_delete = set(data[0]['AssociatedNodeIDs'])

        # Convert set of IDs into a list to use in Cypher query
        ids_list = list(node_ids_to_delete)

        if ids_list:
            # Delete associated nodes by their IDs
            ids_string = ",".join(map(str, ids_list))
            delete_nodes_query = f"""
            MATCH (n) WHERE ID(n) IN [{ids_string}]
            DETACH DELETE n
            """
            await self.async_run(delete_nodes_query)

        # Update the project node to set is_active to False
        update_project_query = f"""
        MATCH (p:Project) WHERE ID(p) = {project_id}
        SET p.is_active = False
        """
        await self.async_run(update_project_query)

        return ids_list

    async def delete_node_and_associates(self, start_node_id, relationships) -> list:
        # Query to find all nodes connected by specified relationships
        find_nodes_query = f"""
        MATCH (n) WHERE ID(n) = {start_node_id}
        OPTIONAL MATCH (n)-[r:{' | '.join(relationships)}*]->(m)
        RETURN DISTINCT ID(n) AS NodeID, COLLECT(ID(m)) AS AssociatedNodeIDs
        """
        # Execute the query to find nodes
        result = await self.async_run(find_nodes_query)
        data = result.data()

        if not data:
            return []  # No nodes found

        # Collecting all node IDs that will be deleted
        node_ids_to_delete = set(data[0]['AssociatedNodeIDs'])
        node_ids_to_delete.add(data[0]['NodeID'])  # Add the start node ID

        # Convert set of IDs into a list to use in Cypher query
        ids_list = list(node_ids_to_delete)

        if ids_list:
            # Delete nodes by their IDs directly
            ids_string = ",".join(map(str, ids_list))
            delete_nodes_query = f"MATCH (n) WHERE ID(n) IN [{ids_string}] DETACH DELETE n"
            await self.async_run(delete_nodes_query)

        return ids_list


    async def delete_code_query_node(self, start_node_id) -> list:
        # Query to update the code_query node's IsDeleted property
        find_nodes_query = f"""
        MATCH (n:code_query)
        WHERE ID(n) = $start_node_id
        SET n.IsDeleted = true
        RETURN ID(n) AS NodeID
        """
        
        # Execute the query to update the node
        result = await self.async_run(find_nodes_query, start_node_id=start_node_id)
        data = result.data()

        if not data:
            return []  # No nodes found

        # Return list containing the updated node ID
        return [data[0]['NodeID']]


    async def update_dependencies(self, node_id, dependencies):
        query = """
        MATCH (n)
        WHERE ID(n) = $node_id
        UNWIND $dependencies AS dependency_id
        MATCH (m)
        WHERE ID(m) = dependency_id
        MERGE (n)-[r:HAS_DEPENDENCY]->(m)
        """
        await self.async_run(query, node_id=node_id, dependencies=dependencies)

    async def get_descendant_nodes(self, parent_node_id, descendant_node_types=None, property_names=None, max_depth=None):
        node_type_condition = ""
        if descendant_node_types:
            # Convert the list to a string format suitable for Cypher query
            types_as_str = "[" + ", ".join(f"'{type_}'" for type_ in descendant_node_types) + "]"
 #           node_type_condition = f"AND ALL(label IN LABELS(m) WHERE label IN {types_as_str})"
            node_type_condition = f"AND ANY(label IN {types_as_str} WHERE label IN LABELS(m))"

        if property_names:
            property_selection = ", ".join(f"m.{prop} AS {prop}" for prop in property_names)
        else:
            property_selection = "properties(m) AS properties"

        query = f"""
        MATCH (n)-[:HAS_CHILD*1..{max_depth}]->(m)
        WHERE ID(n) = $parent_node_id {node_type_condition}
        RETURN ID(m) AS id, LABELS(m) AS labels, {property_selection}
        """
        query_result = await self.async_run(query, parent_node_id=parent_node_id)
        result = query_result.data()
        return result
    
# Update node by id. The list of attributes to update may be just partial and not all attributes
    async def update_node_by_id(self, node_id, properties, node_type=None):
        set_clauses = ", ".join([f"n.{key} = ${key}" for key in properties.keys()])
        query = f"MATCH (n) WHERE ID(n) = $node_id SET {set_clauses} RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties "
        # Prepare parameters for the query
        params = {'node_id': node_id}
        params.update(properties)  # This adds each key-value pair in `properties` to the `params` dict
        
        print('query--->', query)
        print('params--->>', params)
        
        query_result = await self.async_run(query, **params)
        result = query_result.data()
        if result and len(result) > 0:
            return result[0]
        else:
            return None
        
    # async def update_node_by_id(self, node_id, properties, node_type=None):
    #     # Define keys to exclude
    #     excluded_keys = {'new_child_nodes', 'new_relationships', 'reason_for_this_call'}
        
    #     # Filter out the excluded keys from properties
    #     filtered_properties = {k: v for k, v in properties.items() if k not in excluded_keys}
        
    #     # Create SET clauses only for filtered properties
    #     set_clauses = ", ".join([f"n.{key} = ${key}" for key in filtered_properties.keys()])
        
    #     query = f"MATCH (n) WHERE ID(n) = $node_id SET {set_clauses} RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"
        
    #     # Prepare parameters for the query
    #     params = {'node_id': node_id}
        
    #     # Get filtered keys for logging if needed
    #     keys_list = list(filtered_properties.keys())
    #     self.update_logger.info(f"validation logs->keys {keys_list}")
        
    #     # Update params with filtered properties
    #     params.update(filtered_properties)
    #     self.update_logger.info(f"filtered_properties logs->keys {filtered_properties}")
    #     query_result = await self.async_run(query, **params)
    #     result = query_result.data()
        
    #     if result and len(result) > 0:
    #         return result[0]
    #     else:
    #         return None
    
    async def update_property_by_id(self, node_id, property_name, property_value, session_id=None):
        if session_id is not None and property_name == "session_name":
            # If session_id is provided and we're updating session_name
            query = """
            MATCH (n) WHERE ID(n) = $node_id
            SET n.{property_name} = $property_value, n.session_id = $session_id
            RETURN ID(n) AS id
            """.format(property_name=property_name)
            query_result = await self.async_run(
                query, 
                node_id=node_id, 
                property_value=property_value,
                session_id=session_id
            )
        else:
            # Original behavior when session_id is not provided
            query = f"MATCH (n) WHERE ID(n) = $node_id SET n.{property_name} = $property_value RETURN ID(n) AS id"
            query_result = await self.async_run(query, node_id=node_id, property_value=property_value)
        
        result = query_result.data()
        return result[0] if result else None

    
    # update multiple nodes at the same time.
    async def update_nodes_by_id(self, nodelist):

        for node in nodelist:
            node['id'] = int(node['id'])

        if not nodelist:
            return []  # Return empty list if no nodes to update

        # Start building the Cypher query
        query = "UNWIND $nodelist as nodeToUpdate MATCH (n) WHERE ID(n) = nodeToUpdate.id "

        # Dynamically build the SET part of the query based on all possible keys
        all_keys = set()
        for node in nodelist:
            all_keys.update(node.keys())
        all_keys.discard('id')  # Remove 'id' from the keys to set

        set_clauses = [f"n.{key} = COALESCE(nodeToUpdate.{key}, n.{key})" for key in all_keys]
        set_clauses_str = ', '.join(set_clauses)  # Join all SET clauses

        # Complete the query with the dynamically built SET part
        query += f"SET {set_clauses_str} "
        query += "RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties"

        try:
            # Execute the query
            query_result = await self.async_run(query, nodelist=nodelist)
            result = query_result.data()
            return result
        except Exception as e:
            print(f"Error updating nodes: {e}")
            # You might want to log this error or handle it appropriately
            raise

    async def check_user_under_node(self, node_id, user_id):
        query = """
        MATCH (n)-[:HAS_USER]->(u)
        WHERE ID(n) = $node_id AND u.user_id = $user_id 
        RETURN ID(u) AS id
        """
        result = await self.async_run(query, node_id=node_id, user_id=user_id)
        return result.data()
    
# Embeddings to node added

    async def create_vector_index(self,root_node_type):
        # Only use this function if you want to create a vector index on a root node and its children
        # Ideal usecase scenario : 
            # Project and its children 
            # Product and its children
        query = f"""
        CREATE VECTOR INDEX {root_node_type}
        IF NOT EXISTS FOR 
        (n:{root_node_type})
        ON n.embedding
        OPTIONS {{
        indexConfig: {{
            `vector.dimensions`: 1536,  # Adjust based on your embedding vector size
            `vector.similarity_function`: 'cosine'
            }}
        }}
        """
        
        return await self.async_run(query)    
        
        
    async def add_embedding_to_node(self, node_id, properties ):
        return # tempoary disabled
        embedding = await asyncio.to_thread(llm_interface.generate_embedding, properties)
        query = """
        MATCH (n)
        WHERE ID(n) = $node_id
        SET n.embedding = $embedding
        RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
        """
        result = await self.async_run(query, node_id=node_id, embedding=embedding)
        
    async def find_similar_nodes(self, node_id, node_type, max_results=10):
        return [] # temporary disabled
        query = f"""
            MATCH (node:{node_type})-[:HAS_CHILD*]-(m:{node_type})
            where ID(node)={node_id}
            WITH m, vector.similarity.cosine(node.embedding, m.embedding) AS score
            RETURN ID(m) as id, m.Title as Title, score
            ORDER BY score DESCENDING
            LIMIT {max_results};
        """

        result = await self.async_run(query, node_id=node_id, node_type=node_type, max_results=max_results)
        similar_node_ids = []
        for row in result:
            similar_node_ids.append(row.get("id"))

        print("Similar nodes", similar_node_ids)
        return similar_node_ids # Return a list of similar node IDs

    async def get_discussions(self, node_id: int, status: str = None, max_results: int = 10):
        status_condition = {
            'active': "AND (m.status IS NULL OR m.status <> 'finalized')",
            'finalized': "AND m.status = 'finalized'",
            None: ""  # No additional condition for all discussions
        }.get(status, "")  # Default to empty string if status is not recognized
        
        query = f"""
        MATCH (n)-[:HAS_CHILD]->(m:Discussion)
        WHERE ID(n) = {node_id}
        AND m.discussion_type <> 'autoconfig'
        {status_condition}
        OPTIONAL MATCH (u:User)
        WHERE u.Sub = m.created_by
        RETURN 
            ID(m) AS id,
            m.Title AS title,
            m.created_at AS created_at,
            CASE 
                WHEN u IS NOT NULL THEN {{
                    id: u.Sub,
                    name: u.Name,
                    email: u.Email
                }}
                ELSE NULL
            END AS created_by,
            m.status AS status,
            m.discussion_type AS discussion_type
        ORDER BY m.created_at DESC
        LIMIT {max_results}
        """
        
        query_result = await self.async_run(query)
        return query_result.data()

    async def get_graph_nodes(self, node_id: int, node_types: list = [], max_depth: int = 4, relationships: list = ["INTERFACES_WITH", "HAS_CHILD"]):
        def format_node(node_data):
            if node_data.get("properties", {}).get("Type") == "Discussion":
                return None  # Exclude Discussion nodes
            return {
                "id": str(node_data.get("id")),
                "label": node_data.get("properties", {}).get("Title",node_data['labels'][0]),
                "type": node_data.get("properties", {}).get("Type", None),
                "title": node_data.get("properties", {}).get("Title", node_data['labels'][0]),
            }

        if node_types:
            node_types_str = '|'.join([f'{label}' for label in node_types])
            node_types_clause = f'(m:{node_types_str})'
        else:
            node_types_clause = '(m)'

        if relationships:
            rel_types_str = '|'.join([f':{rel}' for rel in relationships])
            rel_clause = f'[*1..{max_depth} {rel_types_str}]'
        else:
            rel_clause = f'[*1..{max_depth}]'

        query = f"""
        MATCH path = (n){rel_clause}-{node_types_clause}
        WHERE ID(n) = $node_id AND ALL(node IN nodes(path) WHERE node.Type <> 'Discussion')
        WITH nodes(path) AS nodes, relationships(path) AS rels
        UNWIND nodes AS node
        UNWIND rels AS rel
        RETURN DISTINCT {{
            id: ID(node),
            label: node.Title,
            properties: properties(node)
        }} AS nodeInfo,
        {{
            from: ID(startNode(rel)),
            to: ID(endNode(rel)),
            id: ID(rel),
            label: type(rel)
        }} AS edgeInfo
        """

        query_result = await self.async_run(query, node_id=node_id)

        nodes = {}
        edges = []

        for row in query_result:
            node_info, edge_info = row
            formatted_node = format_node(node_info)
            if formatted_node:
                node_id = str(node_info['id'])
                nodes[node_id] = formatted_node

            edge = {
                "from": str(edge_info['from']),
                "to": str(edge_info['to']),
                "id": str(edge_info['id']),
                "label": edge_info['label']
            }
            edges.append(edge)

        return {
            "nodes": list(nodes.values()),
            "edges": edges
        }
        
# REQUIREMENT SPECIFIC FUNCTIONS
  ################################################################################  
    async def get_top_level_nodes(self, root_node_id, node_type):
        query = """ 
            MATCH (n:Project)-[:HAS_CHILD]->(n1:{node_type})
            WHERE ID(n) = {root_node_id}

            OPTIONAL MATCH (n1)-[r:HAS_CHILD]->(m:{node_type})
            OPTIONAL MATCH (m)<-[r_assigned:ASSIGNED_TO]-(u:User)
            WITH DISTINCT m, n1, u, r_assigned, 
                EXISTS((m)-[:HAS_CHILD]->(:{node_type})) AS has_child

            RETURN 
                ID(m) AS id,
                ID(n1) AS requirement_root_id,
                LABELS(m) AS labels,
                properties(m) AS properties,
                m.Status AS status,
                m.Priority AS priority,
                m.configuration_status AS configuration_status,
                m.Title AS title,
                m.Type AS type,
                ID(u) AS assignee_id,
                u.Email AS assignee_email,
                u.Name AS assignee_name,
                r_assigned.assigned_at AS assigned_at,
                has_child
            """
        query = query.format(node_type=node_type, root_node_id=root_node_id)
        result = await self.async_run(query)
        return result.data()
    
    async def get_child_requirements(self, parent_id, node_type):
        query = f"""
        MATCH (n)-[:HAS_CHILD]->(m:{node_type})
        WHERE ID(n) = {parent_id}

        OPTIONAL MATCH (m)<-[r:ASSIGNED_TO]-(u:User)
        WITH DISTINCT m, u, r,
            EXISTS((m)-[:HAS_CHILD]->(:{node_type})) AS has_child

        RETURN 
            ID(m) AS id,
            LABELS(m) AS labels,
            properties(m) AS properties,
            m.Status AS status,
            m.Priority AS priority,
            m.configuration_status AS configuration_status,
            m.Title AS title,
            m.Type AS type,
            ID(u) AS assignee_id,
            u.Email AS assignee_email,
            u.Name AS assignee_name,
            r.assigned_at AS assigned_at,
            has_child
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result
    
    async def get_requirement_by_id(self, node_id, project_id):
        query = f"""
        MATCH (p:Project)-[:HAS_CHILD*]->(n:Requirement)
        WHERE ID(n) = {node_id} AND ID(p) = {project_id}
        OPTIONAL MATCH (n)<-[r:ASSIGNED_TO]-(u:User)
        OPTIONAL MATCH path = (root:RequirementRoot)-[:HAS_CHILD*]->(n)
        WITH n, u, r, [node IN nodes(path) | {{id: ID(node), title: node.Title, labels: labels(node)}}] AS parents
        RETURN 
            ID(n) AS id, 
            LABELS(n) AS labels, 
            properties(n) AS properties, 
            n.Status AS status, 
            n.configuration_status AS configuration_status, 
            n.Title AS title, 
            n.Type AS type,
            u.Username AS assignee_id, 
            u.Email AS assignee_email, 
            u.Name AS assignee_name, 
            r.assigned_at AS assigned_at,
            parents
        """
        result = await self.async_run(query)
        result = result.data()
        if result:
            result = result[0]
            return result
        else:
            return {}
    
    async def get_total_requirements(self, project_id):
        query = f"""
        MATCH (p:Project)-[:HAS_CHILD*]->(n:Requirement)
        WHERE ID(p) = {project_id} AND not n:RequirementRoot
        RETURN COUNT(n) AS count
        """
        result = await self.async_run(query)
        result = result.data()
        if result:
            return result[0]['count']
        else:
            return 0
        
    async def get_requirements_count(self, project_id):
        query = """
        MATCH (p:Project)-[:HAS_CHILD*]->(n:Requirement)
        WHERE ID(p) = $project_id AND NOT n:RequirementRoot AND NOT n:ArchitecturalRequirement
        WITH CASE 
                WHEN n.Status IS NULL THEN 'To Do' 
                ELSE n.Status 
            END AS status, count(n) AS count
        RETURN collect({status: status, count: count}) AS results
        """
        parameters = {"project_id": project_id}
        result = await self.async_run(query, **parameters)
        result = result.data()
        if result:
            return result[0]['results']
        else:
            return []
        
    async def get_requirements_count_by_status(self, project_id, status):
        if status == "To Do":
            query = """
            MATCH (p:Project)-[:HAS_CHILD*]->(n:Requirement)
            WHERE ID(p) = $project_id AND NOT n:RequirementRoot 
            AND (n.Status IS NULL OR n.Status = $status)
            RETURN COUNT(n) AS requirements_count
            """
        else:
            query = """
            MATCH (p:Project)-[:HAS_CHILD*]->(n:Requirement)
            WHERE ID(p) = $project_id AND NOT n:RequirementRoot AND n.Status = $status
            RETURN COUNT(n) AS requirements_count
            """


        parameters={"project_id": project_id, "status": status}
        result = await self.async_run(query, **parameters)
        result = result.data()
        # Check if results are available
        if result:
            return result[0]['requirements_count']
        else:
            return 0  # No requirements with this status
        
    async def get_requirements(self, project_id: int):
        # Retrieve the RequirementRoot node for the project
        requirement_root = await self.get_child_nodes(project_id, "RequirementRoot")
        if not requirement_root:
            return []  # No requirements if RequirementRoot doesn't exist
        
        requirement_root_id = requirement_root[0]['id']

        # Fetch all requirements under the RequirementRoot at any depth
        query = """MATCH (project:Project)-[:HAS_CHILD]->(root:RequirementRoot)
        WHERE ID(root) = $requirement_root_id
        // Optional Epic
        OPTIONAL MATCH (root)-[:HAS_CHILD]->(epic:Epic)
        // Optional UserStory (child of Epic if Epic exists)
        OPTIONAL MATCH (epic)-[:HAS_CHILD]->(userStory:UserStory)
        // Optional test relationship
        OPTIONAL MATCH (userStory)<-[:VERIFIES]-(test:Test)

        // Optional Task under UserStory
        OPTIONAL MATCH (userStory)-[:HAS_CHILD]->(task:Task)
        WITH project, root, epic, userStory, test, task

        RETURN 
            // Project columns
            project.Title AS ProjectName,
            root.Title AS RequirementRootName,
            
            // Epic columns
            ID(epic) AS EpicId,
            epic.Title AS Epic,
            epic.Priority AS EpicPriority,
            epic.Description AS EpicDescription,
            
            // UserStory columns
            ID(userStory) AS UserStoryId,
            userStory.Title AS UserStory,
            userStory.Priority AS UserStoryPriority,
            userStory.Description AS UserStoryDescription,
            
            // Test columns
            test.Title AS TestName,
            test.Priority AS TestPriority,
            test.Category AS TestCategory,
            test.Description AS TestDescription,
            test.CanBeAutomated AS TestAutomated,
            test.Type AS TestType,
            test.TestLevel AS TestLevel,
            
            // Test details columns
            test.AcceptanceCriteria AS AcceptanceCriteria,
            test.ExpectedResults AS ExpectedResults,
            test.Steps AS Steps,
            test.PreConditions AS PreConditions,
            test.Tags AS Tags,
            // Task columns
            ID(task) AS TaskId,
            task.Title AS TaskTitle,
            task.Description AS TaskDescription,
            task.StoryPoints AS TaskStoryPoints

        ORDER BY ID(epic) ASC, ID(userStory) ASC, ID(task) ASC
        """

        query_result = await self.async_run(query, requirement_root_id=requirement_root_id)
        requirements = query_result.data()
        return requirements
    

        

    async def import_requirements(self, project_id: int, requirements_data: list):
        import pandas as pd
        """
        Import requirements data with comprehensive duplicate prevention.
        """
        def parse_boolean(value) -> bool:
            if pd.isna(value) or value is None:
                return False
            if isinstance(value, bool):
                return value
            if isinstance(value, str):
                return value.lower() in ['true', 'yes', '1', 'y']
            return bool(value)

        def clean_string(value) -> str:
            if pd.isna(value) or value is None:
                return ''
            return str(value).strip()

        def normalize_key(text: str) -> str:
            """Create normalized key for comparison"""
            return text.lower().strip()

        try:
            # Ensure RequirementRoot exists
            requirement_root = await self.get_child_nodes(project_id, "RequirementRoot")
            if not requirement_root:
                root_node = await self.create_node(
                    ["RequirementRoot"],
                    {
                        "Title": "Requirements",
                        "Type": "RequirementRoot"
                    }, 
                    project_id
                )
                requirement_root_id = root_node["id"] if isinstance(root_node, dict) else root_node
            else:
                requirement_root_id = requirement_root[0]['id']
            
            # Pre-load ALL existing nodes to prevent duplicates
            print("Loading existing nodes...")
            existing_epics = await self.get_child_nodes(requirement_root_id, "Epic")
            epic_lookup = {normalize_key(epic["properties"]["Title"]): epic["id"] 
                        for epic in existing_epics if epic.get("properties", {}).get("Title")}
            
            # Load all user stories across all epics
            user_story_lookup = {}
            for epic in existing_epics:
                stories = await self.get_child_nodes(epic["id"], "UserStory")
                for story in stories:
                    if story.get("properties", {}).get("Title"):
                        key = f"{normalize_key(epic['properties']['Title'])}_{normalize_key(story['properties']['Title'])}"
                        user_story_lookup[key] = story["id"]
            
            # Load all tests across all user stories
            test_lookup = {}
            for story_id in user_story_lookup.values():
                tests = await self.get_child_nodes(story_id, "Test")
                for test in tests:
                    if test.get("properties", {}).get("Title"):
                        test_key = f"{story_id}_{normalize_key(test['properties']['Title'])}"
                        test_lookup[test_key] = test["id"]
            
            print(f"Loaded {len(epic_lookup)} epics, {len(user_story_lookup)} user stories, {len(test_lookup)} tests")
            
            imported_count = 0
            skipped_count = 0
            duplicate_count = 0
            
            print(f"Processing {len(requirements_data)} rows...")
            
            for i, row in enumerate(requirements_data):
                try:
                    # Clean values using the updated column names
                    epic_title = clean_string(row.get('Epic'))
                    user_story_title = clean_string(row.get('UserStory'))
                    test_title = clean_string(row.get('TestCase'))
                    
                    print(f"Row {i+1}: Epic='{epic_title}', UserStory='{user_story_title}', TestCase='{test_title}'")
                    
                    # Must have at least Epic, UserStory AND TestCase to proceed
                    if not epic_title or not user_story_title:
                        print(f"  -> Skipping: Missing required fields")
                        skipped_count += 1
                        continue
                    
                    # Normalize keys for lookup
                    epic_key = normalize_key(epic_title)
                    story_key = f"{epic_key}_{normalize_key(user_story_title)}"
                    
                    # Handle Epic
                    if epic_key not in epic_lookup:
                        epic_properties = {
                            "Title": epic_title,
                            "Type": "Epic",
                            "Priority": clean_string(row.get('EpicPriority', '')),
                            "Description": clean_string(row.get('EpicDescription', '')),
                        }
                        
                        # DEBUG: Print Epic data
                        print(f"\n=== DEBUG EPIC DATA ROW {i+1} ===")
                        print(f"Epic properties: {epic_properties}")
                        print(f"Epic labels: ['Requirement', 'Epic']")
                        print(f"Parent RequirementRoot ID: {requirement_root_id}")
                        print("=== END DEBUG EPIC DATA ===\n")
                        
                        epic_node = await self.create_node(
                            ["Requirement", "Epic"],  # Add both labels
                            epic_properties,
                            requirement_root_id
                        )
                        epic_id = epic_node["id"] if isinstance(epic_node, dict) else epic_node
                        epic_lookup[epic_key] = epic_id
                        print(f"  -> Created Epic: {epic_title}")
                    else:
                        epic_id = epic_lookup[epic_key]
                        print(f"  -> Using existing Epic: {epic_title}")
                    
                    # Handle UserStory
                    if story_key not in user_story_lookup:
                        user_story_properties = {
                            "Title": user_story_title,
                            "Type": "UserStory",
                            "Priority": clean_string(row.get('UserStoryPriority', '')),
                            "Description": clean_string(row.get('UserStoryDescription', '')),
                        }
                        
                        user_story_node = await self.create_node(
                            ["Requirement", "UserStory"],  # Add both labels
                            user_story_properties,
                            epic_id
                        )
                        user_story_id = user_story_node["id"] if isinstance(user_story_node, dict) else user_story_node
                        user_story_lookup[story_key] = user_story_id
                        print(f"  -> Created UserStory: {user_story_title}")
                    else:
                        user_story_id = user_story_lookup[story_key]
                        print(f"  -> Using existing UserStory: {user_story_title}")
                    
                    # Handle Test with comprehensive duplicate check
                    test_key = f"{user_story_id}_{normalize_key(test_title)}"
                    
                    if test_key in test_lookup:
                        print(f"  -> Duplicate Test found: {test_title}")
                        duplicate_count += 1
                        continue
                    
                    # Create Test with updated column mappings
                    test_properties = {
                        "Title": test_title,
                        "Type": clean_string(row.get('TestType', '')),
                        "Priority": clean_string(row.get('TestPriority', '')),
                        "Category": clean_string(row.get('TestCategory', '')),
                        "Description": clean_string(row.get('TestDescription', '')),
                        "CanBeAutomated": parse_boolean(row.get('TestAutomated', '')),
                        "AcceptanceCriteria": clean_string(row.get('AcceptanceCriteria', '')),
                        "ExpectedResults": clean_string(row.get('ExpectedResults', '')),
                        "Steps": clean_string(row.get('Steps', '')),
                        "PreConditions": clean_string(row.get('PreConditions', '')),
                        "Tags": clean_string(row.get('Tags', '')),
                        # Additional missing columns
                        "TestLevel": clean_string(row.get('TestLevel', '')),
                        "TestName": clean_string(row.get('TestName', test_title)),  # Fallback to TestCase if TestName not provided
                    }
                    
                    test_node = await self.create_node(
                        ["Test", clean_string(row.get('TestType', 'Test'))],
                        test_properties,
                        user_story_id
                    )
                    
                    test_id = test_node["id"] if isinstance(test_node, dict) else test_node
                    test_lookup[test_key] = test_id
                    
                    # Create VERIFIES relationship
                    query = """
                    MATCH (test), (userStory)
                    WHERE ID(test) = $test_id AND ID(userStory) = $user_story_id
                    MERGE (test)-[:VERIFIES]->(userStory)
                    """
                    await self.async_run(query, test_id=test_id, user_story_id=user_story_id)
                    
                    print(f"  -> Created Test: {test_title}")
                    imported_count += 1
                    
                except Exception as row_error:
                    print(f"  -> Error processing row {i+1}: {row_error}")
                    import traceback
                    traceback.print_exc()
                    skipped_count += 1
                    continue
            
            print(f"Import complete: {imported_count} imported, {skipped_count} skipped, {duplicate_count} duplicates prevented")
            
            return {
                "imported_count": imported_count,
                "skipped_count": skipped_count,
                "duplicate_count": duplicate_count
            }
            
        except Exception as e:
            print(f"Database import failed: {str(e)}")
            import traceback
            traceback.print_exc()
            raise Exception(f"Database import failed: {str(e)}")


 #################################################################################

#User Management Functions:
    async def upsert_user_db(self, user_id, user_attributes):
        query = """
        MERGE (u:User {Username: $user_id})
        ON CREATE SET u = $user_attributes
        ON MATCH SET u += $user_attributes
        RETURN ID(u) AS id, LABELS(u) AS labels, properties(u) AS properties
        """
        query_result = await self.async_run(query, user_id=user_id, user_attributes=user_attributes)
        result = query_result.data()
        return result[0] if result else None
    
    async def delete_user(self, user_id):
        query = f"MATCH (u:User) WHERE u.Username = '{user_id}' DETACH DELETE u return u.Username as username"
        result = await self.async_run(query)
        return result.data()

    async def connect_user_to_node(self, user_id, node_id, relationship_type):
        query = f"""
        MATCH (u:User), (n)
        WHERE u.Username = '{user_id}' AND ID(n) = {node_id}
        MERGE (u)-[r:{relationship_type}]->(n)
        RETURN ID(u) AS user_id, ID(n) AS node_id
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result[0] if result else None

    async def disconnect_user_from_node(self, user_id, node_id, relationship_type):
        query = f"""
        MATCH (u:User)-[r:{relationship_type}]->(n)
        WHERE u.Username = '{user_id}' AND ID(n) = {node_id}
        DELETE r
        RETURN ID(u) AS user_id, ID(n) AS node_id
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result[0] if result else None
     
    async def get_users_connected_to_node(self, node_id, relationship_type):
        query = f"""
        MATCH (u:User)-[:{relationship_type}]->(n)
        WHERE ID(n) = {node_id}
        RETURN ID(u) AS user_id, u.Username AS username, u.Email AS email
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result
    
    async def add_user_to_discussion(self, user_id, discussion_id):
        query = """
        MATCH (u:User), (d:Discussion)
        WHERE u.Username = $user_id AND ID(d) = $discussion_id
        MERGE (u)-[r:INVOLVED_IN]->(d)
        RETURN ID(u) AS user_id, ID(d) AS discussion_id
        """
        query_result = await self.async_run(query, user_id=user_id, discussion_id=discussion_id)
        result = query_result.data()
        return result[0] if result else None
    
    async def get_user_by_id(self,user_id):
        query = """
        MATCH (u:User) 
        WHERE u.Username = $user_id
        RETURN ID(u) AS id, LABELS(u) AS labels, properties(u) AS properties
        """
        query_result = await self.async_run(query, user_id=user_id)
        result = query_result.data()
        print(query_result)
        if result:
            return result[0]
        else:
            return {}

    async def get_participants_in_discussion(self, discussion_id):
        query = f"""
        MATCH (u:User)-[:INVOLVED_IN]->(d:Discussion)
        WHERE ID(d) = {discussion_id}
        RETURN ID(u) AS user_id, u.Username AS username, u.Email AS email
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result
    
    async def get_participants_in_discussion_to_add(self, discussion_id):
        query = f"""
        MATCH (u:User)
        WHERE NOT EXISTS((u)-[:INVOLVED_IN]->(:Discussion {{id: {discussion_id}}}))
        RETURN DISTINCT ID(u) AS user_id, u.Username AS username, u.Email AS email
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result
    
    async def is_user_in_discussion(self, user_id, discussion_id):
        query = f"""
        MATCH (u:User)-[:INVOLVED_IN]->(d:Discussion)
        WHERE u.Username = '{user_id}' AND ID(d) = {discussion_id}
        RETURN COUNT(u) > 0 AS userInDiscussion
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result[0]['userInDiscussion']
    
    async def is_user_in_project(self, user_id, project_id):
        query = f"""
        MATCH (u:User)-[:WORKS_ON]->(p:Project|Product)
        WHERE u.Username = '{user_id}'  AND ID(p) = {project_id}
        RETURN COUNT(u) > 0 AS userInProject
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result[0]['userInProject']
    
    async def get_clone_url_by_projectid(self,project_id):
        query = f"""
        MATCH (p:Project)
        WHERE ID(p) = {project_id}
        return Properties(p).cloneUrlHttp AS Url , Properties(p).repositoryName AS Repo_Name
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result[0]['Url'],result[0]['Repo_Name']


    async def connect_user_to_project(self, project_id: int, member: ProjectMember):
        """Updates or creates the WORKS_ON relationship between a user and a project."""
        query = f"""
        MATCH (p:Project|Product), (u:User)
        WHERE ID(p) = {project_id} AND u.Username = '{member.user_id}'
        MERGE (u)-[r:WORKS_ON]->(p)
        SET r.role = '{member.role}'
        SET r.responsibilities = '{json.dumps(member.responsibilities)}'
        RETURN ID(p) as project_id, p.Title as project_name, ID(u) as user_id
        """

        result = await self.async_run(query)
        return result.data()

    async def disconnect_user_from_project(self, project_id: int, user_id: str):
        """Deletes the WORKS_ON relationship between a user and a project."""
        query = f"""
        MATCH (p:Project|Product), (u:User)
        WHERE ID(p) = {project_id} AND u.Username = '{user_id}'
        OPTIONAL MATCH (u)-[r:WORKS_ON]->(p)
        DELETE r
        """
        result = await self.async_run(query)

    async def get_projects(self):
        query = """
        MATCH (p:Project)
        WHERE p.is_active = True
        OPTIONAL MATCH (u:User) WHERE u.Username = p.created_by
        RETURN ID(p) AS id, p.Title AS Title, p.created_at AS created_at, p.created_by AS created_by, u.Name AS creator_name, u.Email AS creator_email, u.Picture AS creator_picture
        ORDER BY p.created_at DESC
        """
        result = await self.async_run(query)
        if result:
            return result.data()
        else:
            return []
    async def get_projects_with_pagination(self, skip: int = 0, limit: int = 10, search: str = None, creator_filter: str = None,email_filter: str = None):
        """
        Get projects with pagination, search, and filtering capabilities
        
        Args:
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return
            search: Search term for project titles
            creator_filter: Filter by creator name
        
        Returns:
            Dictionary containing projects data and pagination info
        """
        # Build WHERE clause conditions
        where_conditions = ["p.is_active = true"]
        params = {"skip": skip, "limit": limit}
        
        # Add search condition
        if search and search.strip():
            where_conditions.append("toLower(p.Title) CONTAINS toLower($search)")
            params["search"] = search.strip()
        
        # Add creator filter condition
        if creator_filter and creator_filter.strip():
            where_conditions.append("u.Name = $creator_filter")
            params["creator_filter"] = creator_filter.strip()
        if email_filter and email_filter.strip():
            where_conditions.append("u.Email = $email_filter")
            params["email_filter"] = email_filter.strip()
            
        where_clause = " AND ".join(where_conditions)
        
        # Count query for total records
        count_query = f"""
        MATCH (p:Project)
        OPTIONAL MATCH (u:User) 
        WHERE u.Username = p.created_by
        WITH p, u
        WHERE {where_clause}
        RETURN COUNT(p) as total
        """
        
        # Main query with pagination
        main_query = f"""
        MATCH (p:Project)
        OPTIONAL MATCH (u:User) 
        WHERE u.Username = p.created_by
        WITH p, u
        WHERE {where_clause}
        RETURN ID(p) AS id, p.Title AS Title, p.created_at AS created_at, 
            p.created_by AS created_by, u.Name AS creator_name, 
            u.Email AS creator_email, u.Picture AS creator_picture
        ORDER BY p.created_at DESC
        SKIP $skip
        LIMIT $limit
        """
        
        try:
            # Execute count query
            count_result = await self.async_run(count_query, **params)
            count_data = count_result.data() if count_result else []
            total_count = count_data[0]['total'] if count_data and len(count_data) > 0 else 0
            
            # Execute main query
            result = await self.async_run(main_query, **params)
            projects = result.data() if result else []
            
            # Calculate pagination metadata
            total_pages = (total_count + limit - 1) // limit  # Ceiling division
            current_page = (skip // limit) + 1
            has_next = skip + limit < total_count
            has_previous = skip > 0
            
            return {
                "projects": projects,
                "pagination": {
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "current_page": current_page,
                    "page_size": limit,
                    "has_next": has_next,
                    "has_previous": has_previous,
                    "start_index": skip + 1 if projects else 0,
                    "end_index": min(skip + len(projects), total_count)
                }
            }
            
        except Exception as e:
            print(f"Error in get_projects with pagination: {str(e)}")
            return {
                "projects": [],
                "pagination": {
                    "total_count": 0,
                    "total_pages": 0,
                    "current_page": 1,
                    "page_size": limit,
                    "has_next": False,
                    "has_previous": False,
                    "start_index": 0,
                    "end_index": 0
                }
            }
    
    async def get_project_members(self, project_id: int):
        """Fetches users involved in a project, along with their roles and responsibilities."""

        query = f"""
        MATCH (u:User)-[r:WORKS_ON]->(p:Project|Product)
        WHERE ID(p) = {project_id}
        RETURN u.Username AS user_id, u.Name as name, u.Email as email, r.role AS role, r.responsibilities AS responsibilities
        """

        result = await self.async_run(query)
        return result.data()  # Directly return the query result data
    
    async def get_project_non_members(self, project_id: int):
        """Fetches users can be add in a project, along with their roles and responsibilities."""

        query = f"""
        MATCH (u:User)-[r:WORKS_ON]->(p)
        WHERE (p:Project OR p:Product) AND ID(p) <> {project_id}
        RETURN u.Username AS user_id, u.Name AS name, u.Email AS email, r.role AS role, r.responsibilities AS responsibilities
        """
        result = await self.async_run(query)
        return result.data()  # Directly return the query result data
    
    async def assign_task(self, node_id: int, user_id: str, assigner_id: str = "admin"):
        """
        Executes the Cypher query to assign a task to a user and sets the assignment timestamp.
        """
        #check if the user is already assigned to the task
        query = f"""
        MATCH (u:User {{Username: '{user_id}'}})-[r:ASSIGNED_TO]->(t)
        WHERE ID(t) = {node_id}
        RETURN COUNT(r) AS count
        """
        if (await self.async_run(query)).data()[0]['count'] > 0:
            return "User is already assigned to the task"
        
        now = generate_timestamp()  # Get current time in ISO format
        query = f"""
        MATCH (u:User {{Username: '{user_id}'}}), (t)
        WHERE ID(t) = {node_id}
        MERGE (u)-[r:ASSIGNED_TO]->(t)
        ON CREATE SET r.assigned_at = '{now}'  // Set timestamp on creation
        ON MATCH SET r.assigned_at = '{now}'    // Update timestamp on existing relationship
        ON CREATE SET r.assigned_by = '{assigner_id}'  // Set the assigner ID
        ON MATCH SET r.assigned_by = '{assigner_id}'    // Update the assigner ID
        RETURN ID(t) as id, properties(t) as properties, labels(t) as labels
        """
        result = await self.async_run(query)
        return result.data()
    
    async def get_tasks_count(self, project_id: int):
        """Fetches users can be add in a project, along with their roles and responsibilities."""

        query = f"""
        MATCH (p:Project|Product)-[:HAS_CHILD*]->(n:Requirement)
        WHERE id(p) = {project_id}
        WITH COUNT(n) AS requirementCount
        RETURN CASE
            WHEN requirementCount > 0 THEN requirementCount - 2
            ELSE 0
        END AS project_count
        """
        result = await self.async_run(query)
        return result.data()  # Directly return the query result data
    
    async def get_assigned_tasks(self, user_id: str):
        initial_query = f"""
        MATCH (u:User {{Username: '{user_id}'}})-[a:ASSIGNED_TO]->(t)<-[:HAS_CHILD*0..]-(p:Project)
        RETURN ID(t) AS task_id, t.Title AS title, t.Description AS description, 
            t.Type as type, a.assigned_at as assigned_at, a.assigned_by as assigned_by,
            t.Priority AS priority, t.Status AS status ,ID(p) AS project_id, p.Title AS project_title
        """

        initial_result = await self.async_run(initial_query)
        tasks = initial_result.data()

        assigner_ids = [task["assigned_by"] for task in tasks]

        # Efficiently fetch assigner details for all tasks at once
        assigner_query = f"""
        UNWIND {assigner_ids} AS assigner_id
        MATCH (assigner:User {{Username: assigner_id}})
        RETURN assigner.Username AS assigned_by, assigner.Name AS assigner_name, assigner.Email AS assigner_email
        """

        assigner_result = await self.async_run(assigner_query)
        assigner_details = {d["assigned_by"]: d for d in assigner_result.data()}

        for task in tasks:
            task.update(assigner_details.get(task["assigned_by"], {}))  # Update with assigner details if found

        return tasks
    
    async def get_total_assigned_tasks(self, user_id: str):
        initial_query = f"""
        MATCH (u:User {{Username: '{user_id}'}})-[a:ASSIGNED_TO]->(t)<-[:HAS_CHILD*0..]-(p:Project|Product)
        RETURN ID(t) AS task_id, t.Title AS title, t.Description AS description, 
            t.Type as type, a.assigned_at as assigned_at, a.assigned_by as assigned_by,
            t.Priority AS priority, ID(p) AS project_id, p.Title AS project_title
        """

        initial_result = await self.async_run(initial_query)
        tasks = initial_result.data()

        assigner_ids = [task["assigned_by"] for task in tasks]

        # Efficiently fetch assigner details for all tasks at once
        assigner_query = f"""
        UNWIND {assigner_ids} AS assigner_id
        MATCH (assigner:User {{Username: assigner_id}})
        RETURN assigner.Username AS assigned_by, assigner.Name AS assigner_name, assigner.Email AS assigner_email
        """

        assigner_result = await self.async_run(assigner_query)
        assigner_details = {d["assigned_by"]: d for d in assigner_result.data()}

        for task in tasks:
            task.update(assigner_details.get(task["assigned_by"], {}))  # Update with assigner details if found

        return tasks
    
    async def get_total_assigned_tasks(self, user_id: str):
        initial_query = f"""
        MATCH (u:User {{Username: '{user_id}'}})-[a:ASSIGNED_TO]->(t)<-[:HAS_CHILD*0..]-(p:Project|Product)
        RETURN ID(t) AS task_id, t.Title AS title, t.Description AS description, 
            t.Type as type, a.assigned_at as assigned_at, a.assigned_by as assigned_by,
            t.Priority AS priority, ID(p) AS project_id, p.Title AS project_title
        """

        initial_result = await self.async_run(initial_query)
        tasks = initial_result.data()

        assigner_ids = [task["assigned_by"] for task in tasks]

        # Efficiently fetch assigner details for all tasks at once
        assigner_query = f"""
        UNWIND {assigner_ids} AS assigner_id
        MATCH (assigner:User {{Username: assigner_id}})
        RETURN assigner.Username AS assigned_by, assigner.Name AS assigner_name, assigner.Email AS assigner_email
        """

        assigner_result = await self.async_run(assigner_query)
        assigner_details = {d["assigned_by"]: d for d in assigner_result.data()}

        for task in tasks:
            task.update(assigner_details.get(task["assigned_by"], {}))  # Update with assigner details if found

        # Fetch the count of Project or Product nodes assigned to the user
        count_query = f"""
        MATCH (u:User {{Username: '{user_id}'}})-[a:ASSIGNED_TO]->(t)<-[:HAS_CHILD*0..]-(p:Project|Product)
        RETURN COUNT(DISTINCT p) AS project_product_count
        """

        count_result = await self.async_run(count_query)
        project_product_count = count_result.data()[0].get("project_product_count", 0)

        # Return only the count
        return project_product_count

    async def get_involvement_in_discussions(self, user_id: str):
        query = f"""
        MATCH (u:User {{Username: '{user_id}'}})-[:INVOLVED_IN]->(d:Discussion)<-[:HAS_CHILD]-(n)<-[:HAS_CHILD*0..]-(p:Project|Product)
        RETURN ID(d) AS discussion_id, d.Title AS title, d.Description AS description, d.updated_at AS date, ID(n) as task_id, n.Type as task_type , n.Title as task_title, ID(p) AS project_id, p.Title AS project_title
        """
        result = await self.async_run(query)
        return result.data()
    
    async def get_discussion(self, discussion_id: int):
        query = f"""
        MATCH (n)-[:HAS_CHILD]->(d:Discussion)
        WHERE ID(d) = {discussion_id} 
        OPTIONAL MATCH (d)<-[:INVOLVED_IN]-(u:User)
        RETURN ID(d) AS discussion_id, 
            properties(d) AS properties,
            d.discussion_so_far as messages,
            ID(n) as parent_node_id,
            n.Type as parent_node_type,
            collect(DISTINCT {{
                user_id: u.Username, 
                name: u.Name, 
                email: u.Email 
            }}) AS participants
        """
        result = await self.async_run(query)
        result = result.data()
        
        if result:
            result = result[0]
            
                   # Map participants to messages
            participant_map = {p["user_id"]: p for p in result["participants"]}
            filtered_messages = []
            if result.get("messages"):
                result["messages"]=json.loads(result["messages"])
                #Pop the first three messages as they are system prompts
                if len(result["messages"]) > 3 :
                    result["messages"] = result["messages"][3:]
                    for message in result["messages"]:
                        user_id = message.get("user_id")
                        if user_id:
                            message["user_details"] = participant_map.get(user_id)
                        try:
                            json.loads(message['content'])
                        except:
                            filtered_messages.append(message)
                result["messages"] = filtered_messages

                
        else:
            result = {}     
     
        return result
        
        
    
# Architectural Functions:
    async def get_root_architecture(self, project_id):
        query = f"""
        MATCH (p:Project)-[:HAS_CHILD]->(a:ArchitectureRoot)
        WHERE ID(p) = {project_id}
        OPTIONAL MATCH (a)-[:HAS_CHILD]->(a1:Architecture)

        WITH a, a1, EXISTS((a1)-[:HAS_CHILD]->(:Architecture)) AS has_child

        RETURN 
            {{
                id: ID(a), 
                properties: properties(a), 
                labels: labels(a)
            }} AS root, 
            COLLECT({{
                id: ID(a1), 
                properties: properties(a1), 
                labels: labels(a1),
                has_child: has_child
            }}) AS children
        """

        query_result = await self.async_run(query)
        result = query_result.data()
        if result:
            result = result[0]
        return result
    
    async def get_architecture(self, architecture_id, project_id):
        query = f"""
        MATCH (p:Project)-[:HAS_CHILD*]->(a:Architecture)
        WHERE ID(a) = {architecture_id} AND ID(p) = {project_id}
        OPTIONAL MATCH (a)-[:HAS_CHILD]->(child:Architecture)
        WITH a, child, EXISTS((child)-[:HAS_CHILD]->(:Architecture)) AS has_child
        OPTIONAL MATCH path = (root:ArchitectureRoot)-[:HAS_CHILD*]->(a)
        WITH a, child, has_child, root, [node IN nodes(path) | {{id: ID(node), title: node.Title, labels: labels(node)}}] AS parents
        RETURN 
            ID(a) AS id, 
            properties(a) AS properties,
            parents 
        """
        
        query_result = await self.async_run(query)
        result = query_result.data()
        
        if result:
            result = result[0]
            return result
        else:
            return []
    
    async def get_architectural_requirement(self, project_id):
        query = f"""
        MATCH (p:Project)-[:HAS_CHILD]->(a:ArchitectureRoot)-[:HAS_CHILD]->(r:ArchitecturalRequirement)
        WHERE ID(p) = {project_id}
        RETURN ID(r) AS id, properties(r) AS properties, labels(r) AS labels
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result

    async def get_child_architectures(self, parent_id):
        query = f"""
        MATCH (a:Architecture)-[:HAS_CHILD]->(a1:Architecture)
        WHERE ID(a) = {parent_id}

        WITH a1, 
            EXISTS((a1)-[:HAS_CHILD]->(:Architecture)) AS has_child

        RETURN 
            ID(a1) AS id,
            properties(a1) AS properties, 
            labels(a1) AS labels,
            has_child
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result

    async def get_architectural_element_by_property(self,parent_architecture_id, properties):
        
        
        if not properties :
            raise ValueError("Properties must be provided as a non-empty dictionary.")

        # Construct Property Filter
        property_filter = " AND ".join([f"a1.{key}='{value}'" for key, value in properties.items()])
        
        query = f"""
        MATCH (a:Architecture)-[r:HAS_CHILD|INTERFACES_WITH*0..6]-(a1:Architecture)
        WHERE ID(a) = {parent_architecture_id} AND {property_filter}
        RETURN ID(a1) AS id, properties(a1) AS properties, labels(a1) AS labels
        LIMIT 1
        """
        
        
        query_result = await self.async_run(query)
        result = query_result.data()
        return result

    async def get_architecture_details(self, project_id):
        # Get the project details along with Architecture interface details and design details
        query = f"""
        MATCH (p:Project)-[:HAS_CHILD]->(a:ArchitectureRoot)-[:HAS_CHILD|INTERFACES_WITH*0..3]-(a1:Architecture|Interface|Design)
        WHERE ID(p) = {project_id}
        WITH p, a, 
            collect(DISTINCT a1) as a1s
        RETURN ID(p) as project_id, properties(p) as properties, labels(p) as labels, 
            ID(a) as architecture_root_id, properties(a) as architecture_root_properties, labels(a) as architecture_root_labels, 
            [a1 in a1s WHERE 'Architecture' IN labels(a1) | 
                {{ 
                    architecture_id: ID(a1), 
                    architecture_properties: properties(a1), 
                    architecture_labels: labels(a1) 
                }}
            ] as architecture_details,
            [a1 in a1s WHERE 'Interface' IN labels(a1) | 
                {{ 
                    interface_id: ID(a1), 
                    interface_properties: properties(a1), 
                    interface_labels: labels(a1) 
                }}
            ] as interface_details,
            [a1 in a1s WHERE 'Design' IN labels(a1) | 
                    {{ 
                    design_id: ID(a1), 
                    design_properties: properties(a1), 
                    design_labels: labels(a1) 
                }}
            ] as design_details
        """

        query_result = await self.async_run(query)
        result = query_result.data()
        if result:
            result = result[0]
        else:
            result = {}
        return result
    
    async def get_architecture_leaf_nodes(self, project_id):
        query = f"""
        MATCH (p:Project)-[:HAS_CHILD]->(a)-[:HAS_CHILD*0..]->(a1:Architecture)
        WHERE ID(p) = {project_id} 
        MATCH (a1)<-[:HAS_CHILD]-(c:Container)
        WHERE c.Type = 'Container'
        RETURN 
            ID(a1) AS id, 
            a1.Title AS title, 
            a1.Type AS type, 
            a1.Description AS description,
            a1.branch As branch,
            {{
                id: ID(c),
                title: c.Title,
                type: c.Type
            }} as container
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        
        return result
    

    async def get_figma_file(self, project_id, file_key):
        ## From s3
        tenant_id = get_tenant_id()
        file_name = f'{tenant_id}-{project_id}-{file_key}.json'
        s3_handler = S3Handler(tenant_id)
        if s3_handler.is_file(file_name):
            data = s3_handler.get_file(file_name)
            result = json.loads(data.decode('utf-8'))
            return result
        else:
            return None
   
    async def get_work_items(self, architecture_id):
        with open('log.json','a') as f:
            f.write('------get_work_items------\n\n')
        work_item_map = {
            "component_name": "",
            "description": "",
            "framework": "",
            "interfaces": [],  # Changed to list to store all interfaces
            "interfaces": [],  # Changed to list to store all interfaces
            "algorithms": "",
            "pseudocode": "",
            "repository_name": "",
            "root_folder": "",
            "figma_components": [],
            "figma_components": [],
            "test_cases": [],  # Changed back to original property name
            # Add new diagram fields
            "architecture_diagrams":{},
            "design":""
        }
        
        # Get the architecture node
        architecture = await self.get_node_by_label_id(architecture_id, "Architecture")
        if not architecture:
            return work_item_map

        design_details = await self.get_design_details(architecture_id)
        design_components = []
        if design_details:
            design_components = await self.get_design_components(design_details[0]['id'])    
        # Set basic component info
        work_item_map["repository_name"] = architecture.get("properties", {}).get("RepositoryName")
        work_item_map["root_folder"] = architecture.get("properties", {}).get("RootFolder")
        work_item_map["component_name"] = architecture['properties']['Title']
        work_item_map["branch"] = architecture['properties'].get("branch")
        work_item_map["description"] = architecture['properties']['Description']
        work_item_map["design"] = design_details[0]['properties'] if design_details else {}
        work_item_map["framework"] = design_details[0]['properties'].get("framework") if design_details else ""

        # Get interfaces from the component itself
        component_interfaces = await self.get_interfaces_from_component(architecture_id)
        if component_interfaces and component_interfaces.get('data', {}).get('interfaces'):
            work_item_map["interfaces"].extend(component_interfaces['data']['interfaces'])

        # Get parent container and its interfaces
        parent_node = await self.get_parent_node(architecture_id)
        if isinstance(parent_node, dict):
            work_item_map['design'] = parent_node.get('properties',{}).get('Init_project_info','')
        if parent_node and 'Container' in parent_node.get('labels', []):
            container_interfaces = await self.get_interfaces_from_component(parent_node['id'])
            if container_interfaces and container_interfaces.get('data', {}).get('interfaces'):
                work_item_map["interfaces"].extend(container_interfaces['data']['interfaces'])

        # Get design details
        design_details = await self.get_design_details(architecture_id)
        if design_details :
            design_node = design_details[0]
            work_item_map["design"] = design_node.get("properties", {})
            work_item_map["framework"] = design_node.get("properties", {}).get("framework", "")

            # Get design components
            design_components = await self.get_design_components(design_node['id'])
            for component in design_components:
                type = component.get("properties", {}).get("Type")
                if not type or type == "TestCase":
                    continue
                if work_item_map.get(type):
                    work_item_map[type].append(json.dumps(component['properties']))
                else:
                    work_item_map[type] = [json.dumps(component['properties'])]

        # Log summary of work item map
        self.update_logger.info(f"Work item prepared for component: {work_item_map['component_name']}")
        self.update_logger.info(f"Number of interfaces: {len(work_item_map['interfaces'])}")

        # Get diagrams from design node
        if design_details:
            design_node = design_details[0]
            sequence_diagrams = await self.get_child_nodes(design_node['id'], "SequenceDiagram")
            state_diagrams = await self.get_child_nodes(design_node['id'], "StateDiagram")
            class_diagrams = await self.get_child_nodes(design_node['id'], "ClassDiagram")

            if sequence_diagrams:
                sequence_diagrams_list = str([json.dumps({
                    'title': diagram['properties'].get('Title', ''),
                    'description': diagram['properties'].get('Description', ''),
                    'diagram': diagram['properties'].get('Diagram', '')
                }) for diagram in sequence_diagrams])

                work_item_map["architecture_diagrams"].update({"sequence_diagram":sequence_diagrams_list})

            if state_diagrams:
                state_diagrams_list = str([json.dumps({
                    'title': diagram['properties'].get('Title', ''),
                    'description': diagram['properties'].get('Description', ''),
                    'diagram': diagram['properties'].get('Diagram', '')
                }) for diagram in state_diagrams])

                work_item_map["architecture_diagrams"].update({"state_diagram":state_diagrams_list})


            if class_diagrams:
                class_diagrams_list = str([json.dumps({
                    'title': diagram['properties'].get('Title', ''),
                    'description': diagram['properties'].get('Description', ''),
                    'diagram': diagram['properties'].get('Diagram', '')
                }) for diagram in class_diagrams])

                work_item_map["architecture_diagrams"].update({"class_diagram":class_diagrams_list})


            # Add diagram counts to logging
            self.update_logger.info(f"Number of sequence diagrams: {len(sequence_diagrams)}")
            self.update_logger.info(f"Number of state diagrams: {len(state_diagrams)}")
            self.update_logger.info(f"Number of class diagrams: {len(class_diagrams)}")

        return work_item_map

    def get_workItem(self, container_id, node_type):
        def get_interfaces(container_id):
            try:
                query = '''
                MATCH (container:Container)-[:HAS_CHILD]->(interface:Interface)
                WHERE ID(container) = $container_id
                RETURN container.Title AS containerTitle, collect(DISTINCT interface) AS interfaces
                '''
                
                result = self.run_query(query, container_id=container_id)
                
                if hasattr(result, 'data'):
                    data = result.data()
                else:
                    data = result
                
                if data and len(data) > 0:
                    container_title = data[0]['containerTitle'].replace(' ','').replace('-','_')
                    interfaces = data[0].get('interfaces', [])

                    # Concatenate all PublicAPIDetails into one string
                    concatenated_specs = ''
                    for interface in interfaces:
                        public_api_details = interface.get('PublicAPIDetails', '')
                        concatenated_specs += f"openApiSpec: {public_api_details}\n\n"

                    return {container_title : str(concatenated_specs)}

                return {'Title':''}

            except Exception as e:
                print('error get_workItem', e)
                return {'Title':''}

        if node_type == 'interface':
            return get_interfaces(container_id)
        
        return {'Title':''}

    async def get_work_items_for_testcase(self, project_id):
        
        work_item_map = {
            "component_name": "",
            "description": "",
            "framework": "",
            "repository_name": "",
            "root_folder": "",
            "test_cases": [],  # Changed back to original property name

        }
        # Get test cases from TestCaseRoot
        self.update_logger.info(f"Retrieving test cases for project_id: {project_id}")
        

        if project_id:
            test_case_root = await self.get_child_nodes(project_id, "TestCaseRoot")
            if test_case_root:
                test_case_root_id = test_case_root[0]['id']
                self.update_logger.info(f"Found TestCaseRoot with ID: {test_case_root_id}")
                
                test_cases = await self.get_child_nodes(test_case_root_id, "Test")
                if test_cases:
                    test_case_titles = [tc.get('properties', {}).get('Title', 'Untitled Test Case') for tc in test_cases]
                    self.update_logger.info(f"Retrieved {len(test_cases)} test cases:")
                    for title in test_case_titles:
                        self.update_logger.info(f"- {title}")
                    
                    work_item_map["test_cases"] = [json.dumps(tc.get('properties', {})) for tc in test_cases]
                else:
                    self.update_logger.info("No test cases found under TestCaseRoot")
            else:
                print('No TestCaseRoot found for project--->')
                self.update_logger.info("No TestCaseRoot found for project")

       
        # Log summary of work item map
        self.update_logger.info(f"Number of test cases from TestCaseRoot: {len(work_item_map['test_cases'])}")

        return work_item_map

    async def get_work_items_for_container(self, container_id):
        """
        Modified get_work_items to focus on core component and design details
        for container tasks.
        """
        work_item_map = {
            "component_name": "",
            "description": "",
            "repository_name": "",
            "root_folder": "",
            "design": {},
            "Algorithm": [],
            "Pseudocode": [],
            "interfaces": []
        }
        
        # Get architecture details
        architecture = await self.get_node_by_label_id(container_id, "Container")
        
        if not architecture:
            return work_item_map
        
        # Get basic component info
        work_item_map["component_name"] = architecture['properties'].get('Title', '')
        work_item_map["description"] = architecture['properties'].get('Description', '')
        work_item_map["repository_name"] = architecture['properties'].get('RepositoryName', '')
        work_item_map["root_folder"] = architecture['properties'].get('RootFolder', '')
        work_item_map["branch"] = architecture['properties'].get("branch", "")
        work_item_map["framework"] = architecture['properties'].get("framework", "")
        work_item_map["platform"] = architecture['properties'].get("platform", "")
        work_item_map.update(**architecture['properties'])
        
        # Get interfaces for the container itself
        container_interfaces = await self.get_interfaces_from_container(container_id)
        if container_interfaces and container_interfaces.get('data', {}).get('interfaces'):
            work_item_map["interfaces"].extend(container_interfaces['data']['interfaces'])
        
        return work_item_map
    

    def create_initial_manifest(self, welcome_page_response, manifest_path="project_manifest.yaml"):
        try:
            manifest = {}

            # Extract high-level metadata for overview section
            overview_data = welcome_page_response.get("overview", {})
            
            # Process third-party services with environment variables
            env_vars = overview_data.get("env", {})
            print(f"env_vars-----> {env_vars}")
            supabase_env = welcome_page_response.get("Supabase_env", [])
            print(f"supabase_base env ------> {supabase_env}")
            
            if supabase_env and isinstance(supabase_env, list) and len(supabase_env) > 0:
                first_env = supabase_env[0]
                env_vars = {
                    "SUPABASE_URL": first_env.get("SUPABASE_URL", ""),
                    "SUPABASE_KEY": first_env.get("SUPABASE_KEY", "")
                    # "SUPABASE_DB_URL": first_env.get("SUPABASE_DB_URL", "")
                }
                print(f"supabase_env = {env_vars}")
            
            third_party_service_details = welcome_page_response.get("third_party_services", [])
            print(f"third_party_service ---> {third_party_service_details}")
                    
            # Build overview section
            manifest["overview"] = {
                "project_name": overview_data.get("project_name", "UnnamedApp"),
                "description": overview_data.get("description", ""),
                "version": "1.0.0",
                "env": env_vars,  # Use dict for cleaner YAML output
                "third_party_services": third_party_service_details 
            }

            # Initialize containers array
            manifest["containers"] = []

            # Process each container with your enhanced structure
            for container in welcome_page_response.get("containers", []):
                container_entry = {
                    "container_name": container.get("container_name", ""),
                    "description": container.get("description", ""),
                    "interfaces": container.get("interfaces", ""),
                    "container_type": container.get("container_type", ""),
                    "dependent_containers": container.get("dependent_containers", []),
                    "workspace": container.get("workspace", ""),
                    "ports": container.get("ports", ""),
                    "framework": container.get("framework", ""),
                    "type": container.get("type", ""),
                    "buildCommand": container.get("buildCommand", ""),
                    "startCommand": container.get("startCommand", ""),
                    "lintCommand": container.get("lintCommand", ""),
                    "container_details": container.get("container_details", {}),
                    "lintConfig": container.get("lintConfig", ""),
                    "routes": container.get("routes", []),
                    "apiSpec": container.get("apiSpec", ""),
                    "auth": container.get("auth"),
                    "schema": container.get("schema", ""),
                    "migrations": container.get("migrations", ""),
                    "seed": container.get("seed", "")
                }
                manifest["containers"].append(container_entry)

            print(f"Manifest ----> {manifest}")
            return manifest

        except Exception as e:
            print('error::', e)
            return {}

    async def get_work_items_for_maintenance(self, architecture_id):
        """
        Modified get_work_items to focus on core component and design details
        for maintenance tasks.
        """
        
        work_item_map = {
            "component_name": "",
            "description": "",
            "repository_name": "",
            "root_folder": "",
            "design": {},
            "Algorithm": [],
            "Pseudocode": []
        }
        
        # Get architecture details
        architecture = await self.get_node_by_label_id(architecture_id, "Architecture")
        if not architecture:
            return work_item_map
        
        # Get basic component info
        work_item_map["component_name"] = architecture['properties'].get('Title', '')
        work_item_map["description"] = architecture['properties'].get('Description', '')
        work_item_map["repository_name"] = architecture['properties'].get('RepositoryName', '')
        work_item_map["root_folder"] = architecture['properties'].get('RootFolder', '')
        
        # Get design details
        design_details = await self.get_design_node(architecture_id)
        if design_details:
            design_node = design_details[0]
            work_item_map["design"] = design_node.get("properties", {})
            
            # Get design components
            design_components = await self.get_design_components(design_node.get("id"))
            for component in design_components:
                component_type = component.get("labels", [])[0]
                if component_type in ["Algorithm", "Pseudocode"]:
                    work_item_map[component_type].append(
                        json.dumps(component.get("properties", {}))
                    )
        
        return work_item_map

    async def create_design_node(self, architecture_id, design_properties={}):
        # Filter out None values from design_properties
        filtered_properties = {k: v for k, v in design_properties.items() if v is not None}
        
        # Create SET clause
        set_clauses = [f"d.{key} = ${key}" for key in filtered_properties.keys()]
        
        # Add default Title, Type, and Description if not provided
        if 'Title' not in filtered_properties:
            set_clauses.append("d.Title = 'Detailed design for ' + a.Title")
        if 'Type' not in filtered_properties:
            set_clauses.append("d.Type = 'Design'")
        if 'Description' not in filtered_properties:
            set_clauses.append("d.Description = 'Detailed design for ' + a.Description")
        
        set_clause = f"SET {', '.join(set_clauses)}" if set_clauses else ""

        query = f"""
        MATCH (a:Architecture)
        WHERE ID(a) = $architecture_id
        OPTIONAL MATCH (a)-[:HAS_CHILD]->(existing:Design)
        WITH a, existing
        WHERE existing IS NULL
        CREATE (a)-[:HAS_CHILD]->(d:Design)
        {set_clause}
        RETURN ID(d) as id, properties(d) as properties
        """

        params = {
            "architecture_id": architecture_id,
            **filtered_properties
        }

        query_result = await self.async_run(query, **params)
        result = query_result.data()

        if result:
            created_node = result[0]
            return created_node
        else:
            return None
    
    async def get_design_nodes(self, architecture_id):
        query = f"""
        MATCH (c:Container)-[:HAS_CHILD*0..3]->(a:Architecture)-[:HAS_CHILD]->(d:Design)
        WHERE ID(a) = {architecture_id}
        OPTIONAL MATCH (d)-[:HAS_CHILD]->(s)
        WHERE NOT s:Discussion
        RETURN ID(d) AS design_id, ID(c) as container_id ,properties(d) AS design_properties, labels(d) AS design_labels,
            COLLECT(ID(s)) AS child_ids, COLLECT(labels(s)) AS child_labels , COLLECT(properties(s)) AS child_properties
        """

        query_result = await self.async_run(query)
        results = query_result.data()

        # Process Results
        formatted_results = []
        for row in results:
            design_node = {
                'id': row['design_id'],
                'container_id': row['container_id'],
                'properties': row['design_properties'],
                'labels': row['design_labels'],
          
            }
            for i, child_id in enumerate(row['child_ids']):
                type = row['child_properties'][i].get("Type", None)
                if  type and type not in design_node:
                    design_node[type] = []

                design_node[type].append({
                    'id': child_id,
                    **row['child_properties'][i],
                    'labels': row['child_labels'][i]
                })

            formatted_results.append(design_node)

        return formatted_results
    
    
    async def get_interfaces_based_project(self, project_id):
        query = """
            MATCH (p:Project)-[:HAS_CHILD]->(sc:SystemContext) 
            WHERE ID(p) = """ + f"{project_id}" + """
            WITH sc
            MATCH (sc)-[:HAS_CHILD|CONTAINS]->(container:Container) 
            WITH container
            OPTIONAL MATCH (container)-[:HAS_CHILD]->(interface:Interface)
            OPTIONAL MATCH (container)-[:HAS_CHILD]->(component:Component)-[:HAS_CHILD]->(comp_interface:Interface)
            RETURN collect(CASE WHEN interface IS NOT NULL THEN {
            type: 'container',
            id: ID(container),
            properties: properties(container),
            child_node: {
                type: 'interface',
                id: ID(interface),
                properties: properties(interface)
            }
            } END) + collect(CASE WHEN comp_interface IS NOT NULL THEN {
            type: 'component', 
            id: ID(component),
            properties: properties(component),
            child_node: {
                type: 'interface',
                id: ID(comp_interface),
                properties: properties(comp_interface)
            }
            } END) as interfaces
                """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result
    

    async def get_interface(self, interface_id):
        query = f"""
        MATCH (i:Interface)
        WHERE ID(i) = {interface_id}
        RETURN ID(i) AS id, properties(i) AS properties
        """
        
    # async def get_interface_childs(self, interface_id):
    #     query = f"""
    #     MATCH (i:Interface)
    #     WHERE ID(i) = {interface_id}
    #     OPTIONAL MATCH (i)-[:HAS_CHILD]->(d)
    #     WHERE NOT d:Discussion AND NOT d:Interface
    #     RETURN ID(i) AS interface_id, properties(d) AS interface_properties, labels(d) AS interface_labels,
    #         COLLECT(ID(d)) AS child_ids, COLLECT(labels(d)) AS child_labels , COLLECT(properties(d)) AS child_properties
    #     """
        
    #     query_result = await self.async_run(query)
    #     results = query_result.data()
        
    #     formatted_results = {}
    #     for row in results:
            
    #         for i, child_id in enumerate(row['child_ids']):
    #             type = row['child_properties'][i].get("Type", row['child_labels'][i][1] if (len(row['child_labels'][i])>1) else row['child_labels'][i][0])
    #             if  type and type not in formatted_results:
    #                 formatted_results[type] = []
    #             formatted_results[type].append({
    #                 'id': child_id,
    #                 **row['child_properties'][i],
    #                 'labels': row['child_labels'][i]
    #             })
        
            
    #     return formatted_results
        
    
    async def get_interface_childs(self, interface_id):
        query = f"""
        MATCH (i:Interface)
        WHERE ID(i) = {interface_id}
        OPTIONAL MATCH (i)-[:HAS_CHILD]->(d)
        WHERE NOT d:Discussion AND NOT d:Interface
        RETURN ID(i) AS interface_id, properties(d) AS interface_properties, labels(d) AS interface_labels,
            COLLECT(ID(d)) AS child_ids, COLLECT(labels(d)) AS child_labels , COLLECT(properties(d)) AS child_properties
        """
        
        query_result = await self.async_run(query)
        results = query_result.data()
        
        formatted_results = {}
        for row in results:
            for i, child_id in enumerate(row['child_ids']):
                # Add null checks
                if not row['child_properties'][i] or not row['child_labels'][i]:
                    continue
                    
                # Get type with safer fallback logic
                type = None
                if row['child_properties'][i].get("Type"):
                    type = row['child_properties'][i].get("Type")
                elif len(row['child_labels'][i]) > 1:
                    type = row['child_labels'][i][1]
                elif len(row['child_labels'][i]) > 0:
                    type = row['child_labels'][i][0]
                else:
                    continue  # Skip if no type can be determined
                    
                if type and type not in formatted_results:
                    formatted_results[type] = []
                formatted_results[type].append({
                    'id': child_id,
                    **row['child_properties'][i],
                    'labels': row['child_labels'][i]
                })
        
        return formatted_results

    async def get_interfaces_list(self, project_id):
        query = f"""
        MATCH (p:Project)-[:HAS_CHILD]->(a:ArchitectureRoot)-[:HAS_CHILD*0..]->(i:Interface)
        WHERE ID(p) = {project_id}
        OPTIONAL MATCH (source_node)
        WHERE ID(source_node) = i.source_node_id
        OPTIONAL MATCH (target_node)
        WHERE ID(target_node) = i.target_node_id
        OPTIONAL MATCH (source_node)-[r:INTERFACES_WITH]-(target_node)
        RETURN 
            ID(i) AS id, 
            i.Title AS title,
            i.Type AS type, 
            coalesce(i.InterfaceDescription, i.Description) AS description,
            CASE WHEN source_node IS NOT NULL THEN {{
                id: ID(source_node),
                title: source_node.Title
                
            }} END AS source_node,
            CASE WHEN target_node IS NOT NULL THEN {{
                id: ID(target_node),
                title: target_node.Title
            }} END AS target_node,
            CASE WHEN r IS NOT NULL THEN properties(r) END AS interfaces_with_properties
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result
    
    
    async def create_interface_node(self, source_component_id, target_component_id, interface_properties={}):
        # Filter out None values from interface_properties
        filtered_properties = {k: v for k, v in interface_properties.items() if v is not None}
        
        # Create SET clause
        set_clauses = [f"i.{key} = ${key}" for key in filtered_properties.keys()]
        
        # Add default Title and Type if not provided
        if 'Title' not in filtered_properties:
            set_clauses.append("i.Title = 'Interface for ' + source.Title + ' and ' + target.Title")
        if 'Type' not in filtered_properties:
            set_clauses.append("i.Type = 'Interface'")
        if 'Description' not in filtered_properties:
            set_clauses.append("i.Description = 'Interface between ' + source.Title + ' and ' + target.Title")
        
        set_clause = f"SET {', '.join(set_clauses)}" if set_clauses else ""

        query = f"""
        MATCH (source:Architecture), (target:Architecture)
        WHERE ID(source) = $source_component_id AND ID(target) = $target_component_id
        AND source.Type IN ['Component', 'SubComponent'] AND target.Type IN ['Component', 'SubComponent']
        MERGE (source)-[e:INTERFACES_WITH]->(target)
        CREATE (source)-[:HAS_CHILD]->(i:Interface)
        {set_clause}
        SET i.source_node_id = ID(source),
            i.target_node_id = ID(target),
            e.interface_node_id = ID(i)
        RETURN i, e, ID(source) AS source_id, ID(target) AS target_id
        """

        params = {
            "source_component_id": source_component_id,
            "target_component_id": target_component_id,
            **filtered_properties
        }

        query_result = await self.async_run(query, **params)
        result = query_result.data()

        if result:
            created_node = result[0]['i']
            updated_edge = result[0]['e']
            return {
                "node": created_node,
                "edge": updated_edge,
            }
        else:
            return None
    
    async def get_interfaces_with_relationship_list(self, project_id: int) -> list:
        query = f"""
        MATCH (p:Project)-[:HAS_CHILD]->(ar:ArchitectureRoot)
        WHERE ID(p) = {project_id}
        MATCH (ar:ArchitectureRoot)-[r1:HAS_CHILD|INTERFACES_WITH]-(a:Architecture)-[r:INTERFACES_WITH*0..5]-(a1:Architecture)
        WHERE ALL(rel IN r WHERE rel.Type = 'interfacesWith')
        UNWIND r AS individual_r
        RETURN DISTINCT 
            ID(individual_r) AS relationship, 
            properties(individual_r) AS properties,
            ID(startNode(individual_r)) AS source_id,
            startNode(individual_r).Title AS source_title,
            ID(endNode(individual_r)) AS target_id,
            endNode(individual_r).Title AS target_title
        """
        
        query_result = await self.async_run(query)
        result = query_result.data()
        if result:
            for interface in result:
                interface['relationship'] = str(interface['relationship'])
            return result
        else:
            return []


    async def interfaces_with(self, architecture_id):
        query = f"""
        MATCH (a:Architecture)-[r:INTERFACES_WITH]-(a1:Architecture)
        WHERE ID(a) = {architecture_id}
        OPTIONAL MATCH (interface_node)
        WHERE ID(interface_node) = r.interface_node_id
        RETURN ID(a1) as connecting_architecture_id,
                a1.Title as connecting_architecture_title,
                CASE WHEN interface_node IS NOT NULL THEN {{
                    id: ID(interface_node),
                    title: interface_node.Title,
                    target_node_id: interface_node.target_node_id,
                    source_node_id: interface_node.source_node_id,
                    interface_type: CASE 
                        WHEN ID(a1) = interface_node.target_node_id THEN 'target'
                        WHEN ID(a1) = interface_node.source_node_id THEN 'source'
                        ELSE NULL 
                    END
                }} END AS interface_node,
                ID(r) AS relationship_id,
                properties(r) AS interfaces_with_properties     
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        if result:
            for interface in result:
                interface['relationship_id'] = str(interface['relationship_id'])
            return result
        else:
            return []

    async def get_interface_information(self, node_id):
        interfaces = await self.get_relationships_involving_node(node_id, "INTERFACES_WITH")
        for interface in interfaces:
            if interface['target'] == node_id:
                interface['source_node'] = await self.get_node_by_id(interface['source'])
            if interface['source'] == node_id:
                interface['target_node'] = await self.get_node_by_id(interface['target'])
            
            interface_node_id = interface['properties'].get('interface_node_id')
            if interface_node_id:
                interface['interface_node'] = await self.get_node_by_id(interface_node_id)
        
        return interfaces

           
    async def get_interfaces(self, architecture_id):
        query = f"""
        MATCH (a:Architecture)-[:HAS_CHILD]->(i:Interface)
        WHERE ID(a) = {architecture_id}
        RETURN ID(i) AS interface_id, i.incoming_interfaces AS incoming_interfaces
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        if result:
            result = result[0]
            result['incoming_interfaces'] = json.loads(result['incoming_interfaces'])
        return result
    
    async def get_design_details(self, architecture_id):
        query = f"""
        MATCH (a:Architecture)-[:HAS_CHILD]->(d:Design)
        WHERE ID(a) = {architecture_id}
        RETURN ID(d) AS id, properties(d) AS properties, labels(d) AS labels
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result
    
    async def get_design_components(self, design_id):
        query = f"""
        MATCH (d:Design)-[:HAS_CHILD]->(c)
        WHERE ID(d) = {design_id} AND NOT c:Discussion
        RETURN ID(c) AS id, properties(c) AS properties, labels(c) AS labels
        """
        query_result = await self.async_run(query)
        result = query_result.data()
        return result
    

    async def get_architecture_documentation(self, project_id: int):
        query =  """
        MATCH (p:Project)-[:HAS_CHILD]->(n:ArchitectureRoot)-[:HAS_CHILD*]->(d:Design)-[:HAS_CHILD]->(doc: APIDoc)
        WHERE ID(p) = $root_id
        RETURN d.Title AS design_name, COLLECT({content: doc.`api-doc-in-text`, title: doc.Title}) AS api_docs
        """
        params = {"root_id": project_id}
        
        try:
            query_result = await self.async_run(query, **params)
            result = query_result.data()
            return result
        except Exception as e:
            print(f"Error fetching documentation: {str(e)}")
            raise
    
    async def get_sad_documentation(self, project_id: int):
        query =  """
        MATCH (p:Project)-[:HAS_CHILD]->(n:ArchitectureRoot)-[:HAS_CHILD*]->(d:Design)-[:HAS_CHILD]->(doc:SADDoc)
        WHERE ID(p) = $root_id
        RETURN d.Title AS design_name, COLLECT({content: doc.`Content`, title: doc.Title}) AS sad_docs
        """
        params = {"root_id": project_id}
        
        try:
            query_result = await self.async_run(query, **params)
            result = query_result.data()
            return result
        except Exception as e:
            print(f"Error fetching documentation: {str(e)}")
            raise
    
    async def get_documentation(self, project_id: int, documentation_type: str = "SAD", interface_id: int = None):
        def process_documentation(results):
            if not results:
                return []
            
            processed_docs = []
            current_doc = None
            
            # Process first record to set up document
            first_record = results[0]
            
            # Add nodeId to doc
            doc_with_id = first_record['doc']
            doc_with_id['id'] = first_record['doc_id']  
            
            current_doc = {
                'doc': doc_with_id,
                'sections': []
            }
            processed_docs.append(current_doc)
            
            # Process all records for sections
            for record in results:
                if record['sections']:
                    section = record['sections']
                    section['id'] = record['section_id']  
                    current_doc['sections'].append(section)

            return processed_docs

        if documentation_type.casefold() == "prd":
            base_path = """
            MATCH (project:Project)
            WHERE ID(project) = $project_id
            MATCH (project)-[:HAS_CHILD]->(doc:DocumentationRoot)
            """
        elif documentation_type.casefold() == "api":
            if not interface_id:
                raise ValueError("interface_id is required for API documentation")
            base_path = """
            MATCH (project:Project)
            WHERE ID(project) = $project_id
            MATCH (project)-[:HAS_CHILD]->(context:SystemContext)
            MATCH (context)-[:HAS_CHILD]->(container:Container)
            MATCH (container)-[:HAS_CHILD*0..3]->(interface:Interface)
            WHERE ID(interface) = $interface_id
            MATCH (interface)-[:HAS_CHILD]->(doc:DocumentationRoot)
            """
        else:  # SAD
            base_path = """
            MATCH (project:Project)
            WHERE ID(project) = $project_id
            MATCH (project)-[:HAS_CHILD]->(arch:Architecture)
            MATCH (arch)-[:HAS_CHILD]->(doc:DocumentationRoot)
            """
        
        query = f"""
        {base_path}
        OPTIONAL MATCH (doc)-[:HAS_CHILD]->(sections:Sub_Section)
        RETURN 
            doc, 
            ID(doc) as doc_id, 
            sections, 
            ID(sections) as section_id
        ORDER BY sections.Order
        """
        
        params = {
            "project_id": project_id,
            "interface_id": interface_id
        }
        
        try:
            query_result = await self.async_run(query, **params)
            result = query_result.data()
            return process_documentation(result)
        except Exception as e:
            print(f"Error fetching documentation: {str(e)}")
            raise

    async def create_documentation_root(
        self, 
        project_id: int, 
        documentation_type: str = "SAD",
        interface_id: int = None
    ):
        """Create a DocumentationRoot node for a specific documentation type"""
        try:
            timestamp = generate_timestamp()
            
            if documentation_type.casefold() == "prd":
                query = """
                MATCH (project:Project)
                WHERE ID(project) = $project_id
                MERGE (project)-[:HAS_CHILD]->(doc:DocumentationRoot:Documentation {
                    Title: "Product Requirements Documentation",
                    Description: "Product requirements documentation for the project",
                    DocumentationType: "PRD",
                    Version: "1.0",
                    LastUpdated: datetime($timestamp)
                })
                RETURN doc, ID(doc) as doc_id
                """
                params = {"project_id": project_id, "timestamp": timestamp}
                
            elif documentation_type.casefold() == "api":
                if not interface_id:
                    raise ValueError("interface_id is required for API documentation")
                query = """
                MATCH (project:Project)
                WHERE ID(project) = $project_id
                MATCH (project)-[:HAS_CHILD]->(context:SystemContext)
                MATCH (context)-[:HAS_CHILD]->(container:Container)
                MATCH (container)-[:HAS_CHILD*0..3]->(interface:Interface)
                WHERE ID(interface) = $interface_id
                MERGE (interface)-[:HAS_CHILD]->(doc:DocumentationRoot:Documentation {
                    Title: "API Documentation - " + interface.Title,
                    Description: "API documentation for " + interface.Title,
                    DocumentationType: "API",
                    Version: "1.0",
                    LastUpdated: datetime($timestamp)
                })
                RETURN doc, ID(doc) as doc_id
                """
                params = {
                    "project_id": project_id,
                    "interface_id": interface_id,
                    "timestamp": timestamp
                }
                
            else:  # SAD
                query = """
                MATCH (project:Project)
                WHERE ID(project) = $project_id
                MATCH (project)-[:HAS_CHILD]->(arch:Architecture)
                MERGE (arch)-[:HAS_CHILD]->(doc:DocumentationRoot:Documentation {
                    Title: "System Architecture Documentation",
                    Description: "System architecture documentation for the project",
                    DocumentationType: "SAD",
                    Version: "1.0",
                    LastUpdated: datetime($timestamp)
                })
                RETURN doc, ID(doc) as doc_id
                """
                params = {"project_id": project_id, "timestamp": timestamp}

            result = await self.async_run(query, **params)
            doc_data = result.data()[0]
            cleaned_data = {
                **doc_data['doc'],
                "id": doc_data["doc_id"]
            }
            return cleaned_data

        except Exception as e:
            print(f"Error creating documentation root: {str(e)}")
            raise
    
    async def create_section(
        self, 
        project_id: int, 
        section_name: str, 
        documentation_type: str = "SAD",
        interface_id: int = None
    ):
        if documentation_type.casefold() == "prd":
            base_path = """
            MATCH (project:Project)
            WHERE ID(project) = $project_id
            MATCH (project)-[:HAS_CHILD]->(doc:DocumentationRoot)
            """
        elif documentation_type.casefold() == "api":
            if not interface_id:
                raise ValueError("interface_id is required for API documentation")
            base_path = """
            MATCH (project:Project)
            WHERE ID(project) = $project_id
            MATCH (project)-[:HAS_CHILD]->(context:SystemContext)
            MATCH (context)-[:HAS_CHILD]->(container:Container)
            MATCH (container)-[:HAS_CHILD*0..3]->(interface:Interface)
            WHERE ID(interface) = $interface_id
            MATCH (interface)-[:HAS_CHILD]->(doc:DocumentationRoot)
            """
        else:  # SAD
            base_path = """
            MATCH (project:Project)
            WHERE ID(project) = $project_id
            MATCH (project)-[:HAS_CHILD]->(arch:Architecture)
            MATCH (arch)-[:HAS_CHILD]->(doc:DocumentationRoot)
            """
        
        # Get max order with dynamic path
        get_max_order_query = f"""
        {base_path}
        MATCH (doc)-[:HAS_CHILD]->(section:Sub_Section)
        RETURN COALESCE(MAX(section.Order), -1) as max_order
        """
        
        # Create section with the found doc_id
        create_section_query = f"""
        {base_path}
        WITH doc
        CREATE (doc)-[:HAS_CHILD]->(section:Sub_Section {{
            Title: $section_name,
            Order: $order,
            Content: "",
            CreatedAt: datetime($timestamp),
            UpdatedAt: datetime($timestamp)
        }})
        RETURN section, ID(section) as section_id
        """
        
        try:
            params = {
                "project_id": project_id,
                "interface_id": interface_id,
                "section_name": section_name,
                "timestamp": generate_timestamp()
            }
            
            # Get max order
            order_result = await self.async_run(get_max_order_query, **params)
            max_order = order_result.data()[0]['max_order']
            new_order = max_order + 1
            
            # Create new section
            params["order"] = new_order
            section_result = await self.async_run(create_section_query, **params)
            
            section_data = section_result.data()[0]
            
            return {
                'id': section_data['section_id'],
                'Title': section_name,
                'Order': new_order,
                'Content': "",
                'CreatedAt': section_data['section'].get('CreatedAt'),
                'UpdatedAt': section_data['section'].get('UpdatedAt')
            }
                
        except Exception as e:
            print(f"Error creating section: {str(e)}")
            raise

    async def delete_section(
        self, 
        project_id: int, 
        section_id: int, 
        documentation_type: str = "SAD",
        interface_id: int = None
    ):
        if documentation_type.casefold() == "prd":
            base_path = """
            MATCH (project:Project)
            WHERE ID(project) = $project_id
            MATCH (project)-[:HAS_CHILD]->(doc:DocumentationRoot)
            """
        elif documentation_type.casefold() == "api":
            if not interface_id:
                raise ValueError("interface_id is required for API documentation")
            base_path = """
            MATCH (project:Project)
            WHERE ID(project) = $project_id
            MATCH (project)-[:HAS_CHILD]->(context:SystemContext)
            MATCH (context)-[:HAS_CHILD]->(container:Container)
            MATCH (container)-[:HAS_CHILD*0..3]->(interface:Interface)
            WHERE ID(interface) = $interface_id
            MATCH (interface)-[:HAS_CHILD]->(doc:DocumentationRoot)
            """
        else:  # SAD
            base_path = """
            MATCH (project:Project)
            WHERE ID(project) = $project_id
            MATCH (project)-[:HAS_CHILD]->(arch:Architecture)
            MATCH (arch)-[:HAS_CHILD]->(doc:DocumentationRoot)
            """

        # Verify section belongs to the project and get its order
        verify_query = f"""
        {base_path}
        MATCH (doc)-[:HAS_CHILD]->(section:Sub_Section)
        WHERE ID(section) = $section_id
        RETURN section, section.Order as current_order
        """

        # Update orders of remaining sections
        update_orders_query = f"""
        {base_path}
        MATCH (doc)-[:HAS_CHILD]->(section:Sub_Section)
        WHERE section.Order > $current_order
        SET section.Order = section.Order - 1
        RETURN section
        """

        # Delete section query
        delete_query = """
        MATCH (section:Sub_Section)
        WHERE ID(section) = $section_id
        DETACH DELETE section
        """

        try:
            params = {
                "project_id": project_id,
                "interface_id": interface_id,
                "section_id": section_id
            }

            # Verify section exists and get its order
            verify_result = await self.async_run(verify_query, **params)
            verify_data = verify_result.data()
            
            if not verify_data:
                raise ValueError(f"Section with ID {section_id} not found in project {project_id}")

            section_data = verify_data[0]
            current_order = section_data['current_order']
            
            # Store section data before deletion
            deleted_section = {
                'id': section_id,
                'Title': section_data['section'].get('Title'),
                'Order': current_order,
                'Content': section_data['section'].get('Content'),
                'CreatedAt': section_data['section'].get('CreatedAt'),
                'UpdatedAt': section_data['section'].get('UpdatedAt')
            }

            # Update orders of remaining sections
            params["current_order"] = current_order
            await self.async_run(update_orders_query, **params)

            # Delete the section
            await self.async_run(delete_query, section_id=section_id)

            return deleted_section

        except Exception as e:
            print(f"Error deleting section: {str(e)}")
            raise

    async def reorder_sections(
        self, 
        project_id: int, 
        section_orders, 
        documentation_type: str = "SAD",
        interface_id: int = None
    ):
        if documentation_type.casefold() == "prd":
            base_path = """
            MATCH (project:Project)
            WHERE ID(project) = $project_id
            MATCH (project)-[:HAS_CHILD]->(doc:DocumentationRoot)
            """
        elif documentation_type.casefold() == "api":
            if not interface_id:
                raise ValueError("interface_id is required for API documentation")
            base_path = """
            MATCH (project:Project)
            WHERE ID(project) = $project_id
            MATCH (project)-[:HAS_CHILD]->(context:SystemContext)
            MATCH (context)-[:HAS_CHILD]->(container:Container)
            MATCH (container)-[:HAS_CHILD*0..3]->(interface:Interface)
            WHERE ID(interface) = $interface_id
            MATCH (interface)-[:HAS_CHILD]->(doc:DocumentationRoot)
            """
        else:  # SAD
            base_path = """
            MATCH (project:Project)
            WHERE ID(project) = $project_id
            MATCH (project)-[:HAS_CHILD]->(arch:Architecture)
            MATCH (arch)-[:HAS_CHILD]->(doc:DocumentationRoot)
            """
        
        # Verify sections belong to the project
        verify_query = f"""
        {base_path}
        MATCH (doc)-[:HAS_CHILD]->(section:Sub_Section)
        WHERE ID(section) IN $section_ids
        RETURN count(section) as count
        """
        
        # Update orders query
        update_query = f"""
        MATCH (section:Sub_Section)
        WHERE ID(section) = $section_id
        SET section.Order = $new_order
        RETURN section, ID(section) as section_id
        """
        
        try:
            section_ids = [int(id_) for id_ in section_orders.keys()]
            params = {
                "project_id": project_id,
                "interface_id": interface_id,
                "section_ids": section_ids
            }

            # Verify all sections belong to the project
            verify_result = await self.async_run(verify_query, **params)
            verify_data = verify_result.data()[0]
            
            if verify_data['count'] != len(section_ids):
                raise ValueError("Some sections do not belong to this project")
            
            # Update each section's order
            updated_sections = []
            for section_id, order_data in section_orders.items():
                result = await self.async_run(
                    update_query,
                    section_id=int(section_id),
                    new_order=order_data['order']
                )
                section_data = result.data()[0]
                updated_sections.append({
                    'id': section_data['section_id'],
                    'Title': section_data['section'].get('Title'),
                    'Order': section_data['section'].get('Order'),
                    'Content': section_data['section'].get('Content'),
                    'CreatedAt': section_data['section'].get('CreatedAt'),
                    'UpdatedAt': section_data['section'].get('UpdatedAt')
                })
            
            return updated_sections
                
        except Exception as e:
            print(f"Error reordering sections: {str(e)}")
            raise

#End of Architectural Functions

    async def delete_conversations(self, project_id: str, conversation_id: int = None):
        """
        Deletes conversations for a given project. If conversation_id is provided,
        it deletes a specific conversation; otherwise, it deletes all conversations for the project.

        :param project_id: The ID of the project.
        :param conversation_id: Optional. The ID of a specific conversation to delete.
        """
        try:
            if conversation_id:
                # Delete a specific conversation
                query = """
                MATCH (c:Conversation {ProjectId: $project_id, ConversationId: $conversation_id})
                WHERE c.IsDeleted IS NULL OR c.IsDeleted = false
                SET c.IsDeleted = true
                RETURN c
                """
                params = {"project_id": project_id, "conversation_id": int(conversation_id)}
            else:
                # Delete all conversations for the project
                query = """
                MATCH (c:Conversation {ProjectId: $project_id})
                WHERE c.IsDeleted IS NULL OR c.IsDeleted = false
                SET c.IsDeleted = true
                RETURN c
                """
                params = {"project_id": project_id}

            result = await self.async_run(query, **params)
            affected_conversations = result.data()

            if not affected_conversations:
                print(f"No conversations found for project_id: {project_id}" + 
                    (f" and conversation_id: {conversation_id}" if conversation_id else ""))
            else:
                print(f"Successfully marked {len(affected_conversations)} conversation(s) as deleted.")

            return len(affected_conversations)

        except Exception as e:
            print(f"Error in delete_conversations: {e}")
            raise
            
    
    async def get_general_discussions_by_user(self, user_id: str, _limit: int, project_id) -> List[Dict[str, Any]]:
        """
        Fetches Conversation nodes that are children of a User node and have IsDeleted set to false.
        If project_id is provided, it filters conversations by that project.
        
        :param user_id: The ID (Username) of the user.
        :param _limit: The maximum number of conversations to return.
        :param project_id: Optional. The ID of the project to filter conversations.
        :return: A list of dictionaries containing information about Conversation nodes connected to the specified user and not marked as deleted.
        """
        if project_id:
            query = """
            MATCH (u:User {Username: $user_id})-[:HAS_CHILD]->(d:Conversation)
            WHERE d.IsDeleted = false AND d.ProjectId = $project_id
            RETURN d, ID(d) ORDER BY d.CreatedAt DESC LIMIT $limit
            """
            params = {"user_id": user_id, "project_id": project_id, "limit": _limit}
        else:
            query = """
            MATCH (u:User {Username: $user_id})-[:HAS_CHILD]->(d:Conversation)
            WHERE d.IsDeleted = false
            RETURN d, ID(d) ORDER BY d.CreatedAt DESC LIMIT $limit
            """
            params = {"user_id": user_id, "limit": _limit}
        
        try:
            query_result = await self.async_run(query, **params)
            result = query_result.data()
            return result if result and len(result) > 0 else []
        except Exception as e:
            print(f"Error in get_general_discussions_by_user: {e}")
            raise

    

    async def get_general_query_discussions_by_user(self, user_id: str, _limit: int, _skip: int, project_id) -> List[Dict[str, Any]]:
        if project_id:
            query = """
            MATCH (d:code_query)
            WHERE d.IsDeleted = false 
            AND d.ProjectId = $project_id
            WITH count(d) as total
            MATCH (d:code_query)
            WHERE d.IsDeleted = false 
            AND d.ProjectId = $project_id
            WITH d, ID(d) as node_id, total
            OPTIONAL MATCH (p:Project)
            WHERE ID(p) = d.ProjectId
            ORDER BY d.CreatedAt DESC 
            SKIP $skip
            LIMIT $limit
            RETURN d, node_id, p.Title as project_title, total
            """
            params = {"user_id": user_id, "project_id": project_id, "limit": _limit, "skip": _skip}
        else:
            query = """
            MATCH (d:code_query)
            WHERE d.Username = $user_id 
            AND d.IsDeleted = false
            WITH count(d) as total
            MATCH (d:code_query)
            WHERE d.Username = $user_id 
            AND d.IsDeleted = false
            WITH d, ID(d) as node_id, total
            OPTIONAL MATCH (p:Project)
            WHERE ID(p) = d.ProjectId
            ORDER BY d.CreatedAt DESC 
            SKIP $skip
            LIMIT $limit
            RETURN d, node_id, p.Title as project_title, total
            """
            params = {"user_id": user_id, "limit": _limit, "skip": _skip}


        try:
            query_result = await self.async_run(query, **params)
            result = query_result.data()
            print(result)
            return result if result and len(result) > 0 else []
        except Exception as e:
            print(f"Error in get_general_discussions_by_user: {e}")
            raise
    
# Indexing functions:     
    async def create_indexes(self):
        print("Creating indexes..")
        try:

            # Property Indexes
            await self.async_run("CREATE INDEX username_index IF NOT EXISTS FOR (u:User) ON (u.Username);")
            await self.async_run("CREATE INDEX user_id_index IF NOT EXISTS FOR (u:User) ON (u.user_id);")

            print("Indexes created successfully.")
        except Exception as e:
            print(f"Error creating indexes: {e}")
            
    async def delete_architecture_nodes(self, project_id: int):
        """
        Deletes all architecture nodes associated with a given project.

        :param project_id: The ID of the project.
        :return: The number of deleted nodes.
        """
        query = """
        MATCH (p:Project)-[:HAS_CHILD*]->(a:Architecture)
        WHERE ID(p) = $project_id
        WITH COLLECT(a) AS nodes_to_delete
        FOREACH (n IN nodes_to_delete | 
            DETACH DELETE n
        )
        RETURN COUNT(nodes_to_delete) AS deleted_count
        """
        
        try:
            result = await self.async_run(query, project_id=project_id)
            deleted_count = result.data()[0]['deleted_count']
            print(f"Successfully deleted {deleted_count} architecture node(s) for project {project_id}")
            return deleted_count
        except Exception as e:
            print(f"Error deleting architecture nodes: {str(e)}")
            raise
    
    async def delete_requirement_nodes(self, project_id: int):
        """
        Deletes all architecture nodes associated with a given project.

        :param project_id: The ID of the project.
        :return: The number of deleted nodes.
        """
        query = """
        MATCH (p:Project)-[:HAS_CHILD*]->(a:Requirement)
        WHERE ID(p) = $project_id
        WITH COLLECT(a) AS nodes_to_delete
        FOREACH (n IN nodes_to_delete | 
            DETACH DELETE n
        )
        RETURN COUNT(nodes_to_delete) AS deleted_count
        """
        
        try:
            result = await self.async_run(query, project_id=project_id)
            deleted_count = result.data()[0]['deleted_count']
            print(f"Successfully deleted {deleted_count} architecture node(s) for project {project_id}")
            return deleted_count
        except Exception as e:
            print(f"Error deleting architecture nodes: {str(e)}")
            raise

    async def delete_current_node(self, node_id):

        query = f"MATCH (n) WHERE ID(n) = $node_id DETACH DELETE n"
        await self.async_run(query, node_id=node_id)
    
        return True

        
    async def get_old_graph_nodes_and_edges(self, node_id: int, depth_level: int, node_types: List[str]= None, relationships: List[str] = ["INTERFACES_WITH", "HAS_CHILD"]):
        def format_node(node_data):
            properties = node_data["properties"]
            # Use Type as first preference, then Title, then the first label, then an empty string
            label = properties.get("Type") or properties.get("Title") or node_data["labels"][0] or ""
            return {
                "id": str(node_data["id"]),
                "label": label,
                "type": properties.get("Type") or node_data["labels"][0],
                "title": properties.get("Title") or node_data["labels"][0]
            }

        def format_edge(edge_data):
            return {
                "id": str(edge_data["id"]),
                "from": str(edge_data["start_node"]),
                "to": str(edge_data["end_node"]),
                "type": edge_data["type"]
            }
        node_type_filter=""
        if node_types:
            node_type_filter = " OR ".join([f"n:{node_type}" for node_type in node_types])

        where_clause = f'AND ({node_type_filter})'
        if not node_type_filter:
            where_clause = f''      
        rel_types_str = '|'.join([f'{rel}' for rel in relationships])
        rel_clause = f'[:{rel_types_str}*1..{depth_level}]'

        query = f"""
        MATCH path = (root:Project)-{rel_clause}->(n)
        WHERE ID(root) = {node_id} {where_clause}
        WITH nodes(path) AS nodes, relationships(path) AS rels
        UNWIND nodes AS node
        WITH DISTINCT node, rels
        WHERE not node:Discussion
        RETURN {{
            id: ID(node),
            labels: labels(node),
            properties: properties(node)
        }} AS nodeInfo,
        [rel IN rels WHERE startNode(rel) = node OR endNode(rel) = node | {{
            id: ID(rel),
            start_node: ID(startNode(rel)),
            end_node: ID(endNode(rel)),
            type: type(rel)
        }}] AS edgeInfos
        """

        query_result = await self.async_run(query)

        nodes = {}
        edges = set()

        for record in query_result:
            node_info = record["nodeInfo"]
            edge_infos = record["edgeInfos"]

            node_id = str(node_info["id"])
            if node_id not in nodes:
                formatted_node = format_node(node_info)
                if formatted_node:  # Only add the node if it's not None
                    nodes[node_id] = formatted_node

            for edge_info in edge_infos:
                edges.add(json.dumps(format_edge(edge_info)))

        result = {
            "nodes": list(nodes.values()),
            "edges": [json.loads(edge) for edge in edges]
        }

        return result
    
    async def get_new_graph_nodes_and_edges(self, node_id: int, depth_level: int, node_types: List[str]= None, relationships: List[str] = ["INTERFACES_WITH", "HAS_CHILD"]):
        def format_node(node_data):
            properties = node_data["properties"]
            # Use Type as first preference, then Title, then the first label, then an empty string
            label = properties.get("Type") or properties.get("Title") or node_data["labels"][0] or ""
            return {
                "id": str(node_data["id"]),
                "label": label,
                "type": properties.get("Type") or node_data["labels"][0],
                "title": properties.get("Title") or node_data["labels"][0],
                "neighbors": [],
                "links": []
            }

        def format_edge(edge_data):
            return {
                "id": str(edge_data["id"]),
                "source": str(edge_data["start_node"]),
                "target": str(edge_data["end_node"]),
                "type": edge_data["type"]
            }
        node_type_filter=""
        if node_types:
            node_type_filter = " OR ".join([f"n:{node_type}" for node_type in node_types])

        where_clause = f'AND ({node_type_filter})'
        if not node_type_filter:
            where_clause = f''      
        rel_types_str = '|'.join([f'{rel}' for rel in relationships])
        rel_clause = f'[:{rel_types_str}*1..{depth_level}]'

        query = f"""
        MATCH path = (root:Project)-{rel_clause}->(n)
        WHERE ID(root) = {node_id} {where_clause}
        WITH nodes(path) AS nodes, relationships(path) AS rels
        UNWIND nodes AS node
        WITH DISTINCT node, rels
        WHERE not node:Discussion
        RETURN {{
            id: ID(node),
            labels: labels(node),
            properties: properties(node)
        }} AS nodeInfo,
        [rel IN rels 
            WHERE (startNode(rel) = node OR endNode(rel) = node)
            AND NOT startNode(rel):Discussion
            AND NOT endNode(rel):Discussion
            | {{
            id: ID(rel),
            start_node: ID(startNode(rel)),
            end_node: ID(endNode(rel)),
            type: type(rel)
        }}] AS edgeInfos
        """
        

        query_result = await self.async_run(query)

        nodes = {}
        edges = set()

        for record in query_result:
            node_info = record["nodeInfo"]
            edge_infos = record["edgeInfos"]

            node_id = str(node_info["id"])
            if node_id not in nodes:
                formatted_node = format_node(node_info)
                if formatted_node:  # Only add the node if it's not None
                    nodes[node_id] = formatted_node

            for edge_info in edge_infos:
                edges.add(json.dumps(format_edge(edge_info)))

        result = {
            "nodes": list(nodes.values()),
            "links": [json.loads(edge) for edge in edges]
        }

        return result

    async def newgraph_search_nodes(self, node_id: int, search_text: str) -> Dict[str, List[Dict[str, Any]]]:
        print(f"Searching for node_id: {node_id}, search_text: {search_text}")

        def format_node(node_data: Dict[str, Any]) -> Dict[str, Any]:
            properties = node_data.get("properties", {})
            return {
                "id": str(node_data.get("id")),
                "label": properties.get("Type", ""),
                "type": properties.get("Type", ""),
                "title": properties.get("Title", ""),
                "neighbors": [],
                "links": []
            }

        def format_link(link_id: int, start_node_id: int, end_node_id: int, relationship_type: str) -> Dict[str, Any]:
            return {
                "id": str(link_id),
                "source": str(start_node_id),
                "target": str(end_node_id),
                "type": relationship_type
            }

        escaped_search_text = search_text.replace("'", "\\'")

        query = f"""
        MATCH (p)-[*]->(n)
        WHERE NOT (n:Discussion) AND ID(p) = {node_id} AND n.Title =~ '(?i).*{escaped_search_text}.*'
        WITH COLLECT(n) AS nodes
        UNWIND nodes AS n
        OPTIONAL MATCH (n)-[r]->(m)
        WHERE m IN nodes
        RETURN n, ID(n) as id, ID(m) as target_id, TYPE(r) as relationship_type, ID(r) as link_id
        """
        query_result = await self.async_run(query)
        print(f"Query result: {query_result}")

        formatted_nodes = []
        links = []
        node_map = {}

        # Process the nodes and relationships
        for record in query_result:
            node = record["n"]
            node_id = record["id"]
            target_id = record.get("target_id")
            relationship_type = record.get("relationship_type")
            link_id = record.get("link_id")

            if node_id not in node_map:
                node_data = {"id": node_id, "properties": dict(node)}
                formatted_node = format_node(node_data)
                formatted_nodes.append(formatted_node)
                node_map[node_id] = formatted_node

            # Process relationships and create links
            if target_id and relationship_type and link_id:
                link = format_link(link_id, node_id, target_id, relationship_type)
                links.append(link)

        print(f"Formatted nodes: {formatted_nodes}")
        print(f"Links: {links}")

        result = {
            "nodes": formatted_nodes,
            "links": links
        }

        return result
    
    async def oldgraph_search_nodes(self, node_id: int, search_text: str) -> Dict[str, List[Dict[str, Any]]]:
        print(f"Searching for node_id: {node_id}, search_text: {search_text}")

        def format_node(node_data: Dict[str, Any]) -> Dict[str, Any]:
            properties = node_data.get("properties", {})
            return {
                "id": str(node_data.get("id")),
                "label": properties.get("Type", ""),
                "type": properties.get("Type", ""),
                "title": properties.get("Title", ""),
            }

        def format_link(link_id: int, start_node_id: int, end_node_id: int, relationship_type: str) -> Dict[str, Any]:
            return {
                "id": str(link_id),
                "from": str(start_node_id),
                "to": str(end_node_id),
                "type": relationship_type
            }

        escaped_search_text = search_text.replace("'", "\\'")

        query = f"""
        MATCH (p)-[*]->(n)
        WHERE NOT (n:Discussion) AND ID(p) = {node_id} AND n.Title =~ '(?i).*{escaped_search_text}.*'
        WITH COLLECT(n) AS nodes
        UNWIND nodes AS n
        OPTIONAL MATCH (n)-[r]->(m)
        WHERE m IN nodes
        RETURN n, ID(n) as id, ID(m) as target_id, TYPE(r) as relationship_type, ID(r) as link_id
        """
        query_result = await self.async_run(query)
        print(f"Query result: {query_result}")

        formatted_nodes = []
        links = []
        node_map = {}

        # Process the nodes and relationships
        for record in query_result:
            node = record["n"]
            node_id = record["id"]
            target_id = record.get("target_id")
            relationship_type = record.get("relationship_type")
            link_id = record.get("link_id")

            if node_id not in node_map:
                node_data = {"id": node_id, "properties": dict(node)}
                formatted_node = format_node(node_data)
                formatted_nodes.append(formatted_node)
                node_map[node_id] = formatted_node

            # Process relationships and create links
            if target_id and relationship_type and link_id:
                link = format_link(link_id, node_id, target_id, relationship_type)
                links.append(link)

        print(f"Formatted nodes: {formatted_nodes}")
        print(f"Links: {links}")

        result = {
            "nodes": formatted_nodes,
            "edges": links
        }

        return result
      
    async def get_architectural_requirement_with_children(self, project_id: int) -> Dict[str, Any]:
        try:
            query = f"""
            MATCH p=(p1:Project)-[:HAS_CHILD]->(m:ArchitectureRoot)-[:HAS_CHILD]->(ar:ArchitecturalRequirement)
            WHERE ID(p1) = {project_id}
            WITH ar
            OPTIONAL MATCH (ar)-[:HAS_CHILD]->(r)
            WHERE NOT r:Discussion
            RETURN ar, COLLECT(r) AS childNodes
            """
            
            query_result = await self.async_run(query)
            results = query_result.data()

            if not results:
                return None

            # Process Results
            row = results[0]  # We expect only one result
            ar = row['ar']
            child_nodes = row['childNodes']

            formatted_result = {
                'id': ar.identity,  # Use 'identity' instead of 'id'
                'properties': dict(ar),
                'labels': list(ar.labels),
                'childNodes': [
                    {
                        'id': child.identity,  # Use 'identity' for child nodes too
                        'properties': dict(child),
                        'labels': list(child.labels)
                    } for child in child_nodes if child is not None
                ]
            }

            return formatted_result
        except Exception as e:
            logger.error(f"Database error in get_architectural_requirement_with_children: {str(e)}")
            raise
    
    async def get_absolute_root_node(self, node_id):
        """
        Find the very first root node (ultimate ancestor) for a given node.
        
        :param node_id: The ID of the node for which to find the root.
        :return: A dictionary containing the root node's id, labels, and properties,
                or None if no root is found.
        """
        query = """
        MATCH path = (root)-[:HAS_CHILD*]->(m)
        WHERE ID(m) = $node_id AND NOT ()-[:HAS_CHILD]->(root)
        RETURN ID(root) AS id, LABELS(root) AS labels, properties(root) AS properties
        LIMIT 1
        """
        
        query_result = await self.async_run(query, node_id=node_id)
        result = query_result.data()
        
        if result and len(result) > 0:
            return result[0]
        return None
    
    async def get_related_userstories(self, project_id: int) -> Dict[str, Any]:  ### This will render all the functional and architectural requirementnode with its childnode (Userstory and Epic)
        try:
            query = f"""
            MATCH p=(p1:Project)-[:HAS_CHILD]->(m:ArchitectureRoot)-[:HAS_CHILD]->(ar:ArchitecturalRequirement)
            WHERE ID(p1) = {project_id}
            WITH p, p1, m, ar
            MATCH (ar)-[:HAS_CHILD]->(r)
            WHERE NOT r:Discussion
            OPTIONAL MATCH (r)-[:RELATES_TO]->(relatedNode)
            RETURN ar, r, COLLECT(DISTINCT relatedNode) AS relatedNodes
            """
            
            query_result = await self.async_run(query)
            results = query_result.data()

            if not results:
                return None

            ar = results[0]['ar']
            child_nodes_map = {}

            for row in results:
                child = row['r']
                related_nodes = row['relatedNodes']
                
                if child.identity not in child_nodes_map:
                    child_nodes_map[child.identity] = {
                        'id': child.identity,
                        'properties': dict(child),
                        'labels': list(child.labels),
                        'relatedNodes': []
                    }
                
                child_nodes_map[child.identity]['relatedNodes'] = [
                    {
                        'id': related.identity,
                        'properties': dict(related),
                        'labels': list(related.labels)
                    } for related in related_nodes if related is not None
                ]

            formatted_result = {
                'id': ar.identity,
                'properties': dict(ar),
                'labels': list(ar.labels),
                'childNodes': list(child_nodes_map.values())
            }

            return formatted_result
        except Exception as e:
            logger.error(f"Database error in get_architectural_requirement_with_children_and_related: {str(e)}")
            raise
        
    async def get_related_nodes(self, project_id: int, child_id: int, child_type: str) -> Dict[str, Any]:
        try:
            query = f"""
            MATCH (p1:Project)-[:HAS_CHILD]->(m:ArchitectureRoot)-[:HAS_CHILD]->(ar:ArchitecturalRequirement)
            WHERE ID(p1) = {project_id}
            WITH ar
            MATCH (ar)-[:HAS_CHILD]->(child)
            WHERE ID(child) = {child_id} AND '{child_type}' IN labels(child) AND NOT 'Discussion' IN labels(child)
            OPTIONAL MATCH (child)-[:RELATES_TO]->(relatedNode)
            RETURN child, COLLECT(DISTINCT relatedNode) AS relatedNodes
            """
            
            query_result = await self.async_run(query)
            results = query_result.data()

            if not results:
                return None

            row = results[0]
            child = row['child']
            related_nodes = row['relatedNodes']

            formatted_result = {
                'id': child.identity,
                'properties': dict(child),
                'labels': list(child.labels),
                'relatedNodes': [
                    {
                        'id': related.identity,
                        'properties': dict(related),
                        'labels': list(related.labels)
                    } for related in related_nodes if related is not None
                ]
            }

            return formatted_result
        except Exception as e:
            logger.error(f"Database error in get_related_nodes: {str(e)}")
            raise
        
    
    async def get_architecture_relationships(self, project_id: int, query_type: str) -> Dict[str, Any]:
        try:
            base_query = f"""
            MATCH (p1:Project)-[:HAS_CHILD]->(m:Architecture)
            WHERE ID(p1) = {project_id}
            WITH p1, m
            """

            if query_type == 'implements':
                query = base_query + """
                MATCH (m)-[r:IMPLEMENTS]->(implemented)
                WHERE NOT implemented:Discussion
                RETURN m AS source, COLLECT({relationship: r, node: implemented}) AS implementations
                """
            elif query_type == 'components':
                query = base_query + """
                MATCH (m)-[:HAS_CHILD]->(child:Component)
                OPTIONAL MATCH (child)-[r:IMPLEMENTS]->(implemented)
                WHERE implemented <> m AND NOT implemented:Discussion
                WITH child AS source, COLLECT({relationship: r, node: implemented}) AS implementations
                WHERE size(implementations) > 0
                RETURN COLLECT({source: source, implementations: implementations}) AS components
                """
            elif query_type == 'subcomponents':
                query = base_query + """
                MATCH (m)-[:HAS_CHILD]->(comp:Component)-[:HAS_CHILD]->(sub:SubComponent)
                OPTIONAL MATCH (sub)-[r:IMPLEMENTS]->(implemented)
                WHERE implemented <> m AND NOT implemented:Discussion
                WITH sub AS source, COLLECT({relationship: r, node: implemented}) AS implementations
                WHERE size(implementations) > 0
                RETURN COLLECT({source: source, implementations: implementations}) AS subcomponents
                """
            else:
                raise ValueError("Invalid query_type")

            query_result = await self.async_run(query)
            results = query_result.data()

            if not results:
                return {'implementations': []}

            def format_node(node):
                return {
                    'id': node.identity,
                    'type': list(node.labels)[0] if node.labels else 'Unknown',
                    'properties': dict(node)
                }

            def format_relationship(rel):
                return {
                    'type': type(rel).__name__,
                    'properties': dict(rel)
                }

            if query_type == 'implements':
                source = results[0].get('source')
                implementations = results[0].get('implementations', [])
            elif query_type == 'components':
                implementations = results[0].get('components', [])
            else:  # subcomponents
                implementations = results[0].get('subcomponents', [])

            formatted_result = {
                'implementations': [
                    {
                        'source_node': format_node(item['source']),
                        'target_nodes': [
                            {
                                'id': impl['node'].identity,
                                'type': list(impl['node'].labels)[0] if impl['node'].labels else 'Unknown',
                                'properties': dict(impl['node']),
                                'relationship': format_relationship(impl['relationship'])
                            } for impl in item['implementations']
                        ]
                    } for item in (implementations if query_type != 'implements' else [{'source': source, 'implementations': implementations}])
                ]
            }

            return formatted_result
        except Exception as e:
            logger.error(f"Database error in get_architecture_relationships: {str(e)}")
            raise
        
    
    async def get_system_context_with_containers(self, project_id: int) -> Dict[str, Any]:
        try:
            # Single query to get both SystemContext and Containers
            query = f"""
            MATCH (p:Project)-[:HAS_CHILD]->(sc:SystemContext)
            WHERE ID(p)={project_id}
            WITH sc
            OPTIONAL MATCH (sc)-[:HAS_CHILD|CONTAINS]->(container:Container)
            WHERE NOT container:Discussion
            RETURN sc as systemContext, 
            collect(DISTINCT container) as containers
            """

            result = await self.async_run(query)
            results = result.data()

            if not results:
                return None

            def format_node(node):
                return {
                    'id': node.identity,
                    'type': list(node.labels)[0] if node.labels else 'Unknown',
                    'properties': dict(node)
                }

            result = results[0]
            formatted_result = {
                'data': {
                    'systemContext': format_node(result['systemContext'])
                }
            }

            # Only add containers if they exist (not empty list)
            if result['containers']:
                formatted_result['data']['containers'] = [
                    format_node(container) for container in result['containers']
                ]

            return formatted_result

        except Exception as e:
            logger.error(f"Database error in get_system_context_with_containers for project_id: {str(e)}")
            raise
        
    async def get_container_with_components(self, project_id: int, container_id: int) -> Dict[str, Any]:
        try:
          
            
            query = f"""
            MATCH (p:Project)-[:HAS_CHILD]->(sc:SystemContext)
            WHERE ID(p)={project_id}
            WITH sc
            MATCH (sc)-[:HAS_CHILD|CONTAINS]->(container:Container)
            WHERE ID(container)={container_id}
            WITH container
            OPTIONAL MATCH (container)-[:HAS_CHILD]->(component:Component)
            WHERE NOT component:Discussion
            OPTIONAL MATCH (container)-[:HAS_CHILD]->(interface:Interface)
            RETURN container as container, 
                collect(DISTINCT component) as components,
                collect(DISTINCT interface) as interfaces
            """

            result = await self.async_run(query)
            results = result.data()

            if not results:
                return None

            def format_node(node):
                return {
                    'id': node.identity,
                    'type': list(node.labels)[0] if node.labels else 'Unknown',
                    'properties': dict(node)
                }

            result = results[0]
            formatted_result = {
                'data': {
                    'container': format_node(result['container']),
                    'components': [format_node(component) for component in result['components']],
                    'interfaces': [format_node(interface) for interface in result['interfaces']]
                }
            }

            return formatted_result

        except Exception as e:
            logger.error(f"Database error in get_container_components: {str(e)}")
            raise
    
    
    async def get_component_with_children(self, project_id: int, component_id: int) -> Dict[str, Any]:
        try:
            query = f"""
            MATCH (p:Project)-[:HAS_CHILD]->(sc:SystemContext)-[:HAS_CHILD|CONTAINS]->(container:Container)
            WHERE ID(p)={project_id}
            WITH container 
            MATCH (container)-[:HAS_CHILD]->(component:Component)
            WHERE NOT component:Discussion AND ID(component)={component_id}
            WITH component
            OPTIONAL MATCH (component)-[r:CHILDNODES|HAS_CHILD]->(x)
            WHERE NOT x:Discussion AND (x:SubComponent)
            RETURN component as component,
                collect(DISTINCT x) as childNodes,
                collect(DISTINCT type(r)) as relationshipTypes,
                collect(DISTINCT labels(x)) as nodeTypes
            """

            result = await self.async_run(query)
            results = result.data()

            if not results:
                return None

            def format_node(node):
                return {
                    'id': node.identity,
                    'type': list(node.labels)[0] if node.labels else 'Unknown',
                    'properties': dict(node)
                }

            result = results[0]
            formatted_result = {
                'data': {
                    'component': format_node(result['component']),
                    'childNodes': [format_node(child) for child in result['childNodes']],
                    'relationshipTypes': result['relationshipTypes'],
                    'nodeTypes': result['nodeTypes']
                }
            }

            return formatted_result

        except Exception as e:
            logger.error(f"Database error in get_component_with_children: {str(e)}")
            raise
    
    
    async def get_design_with_children(self, project_id: int, component_id: int, design_id: int) -> Dict[str, Any]:
        try:
            query = f"""
            MATCH (p:Project)-[:HAS_CHILD]->(sc:SystemContext)-[:HAS_CHILD|CONTAINS]->(container:Container)-[:HAS_CHILD]->(component:Component)-[:HAS_CHILD]->(design:Design)
            WHERE ID(p)={project_id} AND ID(component)={component_id} AND ID(design)={design_id}
            WITH design
            OPTIONAL MATCH (design)-[r:CHILDNODES|HAS_CHILD]->(x)
            WHERE NOT x:Discussion
            RETURN design,
                collect(DISTINCT x) as childNodes,
                collect(DISTINCT type(r)) as relationshipTypes,
                collect(DISTINCT labels(x)) as nodeTypes
            """

            result = await self.async_run(query)
            results = result.data()

            if not results:
                return None

            def format_node(node):
                return {
                    'id': node.identity,
                    'type': list(node.labels)[0] if node.labels else 'Unknown',
                    'properties': dict(node)
                }

            result = results[0]
            if not result['design']:
                return None
                
            formatted_result = {
                'data': {
                    'design': format_node(result['design']),
                    'childNodes': [format_node(child) for child in result['childNodes']],
                    'relationshipTypes': result['relationshipTypes'],
                    'nodeTypes': result['nodeTypes']
                }
            }

            return formatted_result

        except Exception as e:
            logger.error(f"Database error in get_design_with_children: {str(e)}")
            raise
        
    async def get_container_with_all_components(self, project_id: int) -> Dict[str, Any]:
        try:
            query = f"""
            MATCH (p:Project)-[:HAS_CHILD]->(sc:SystemContext)-[:HAS_CHILD|CONTAINS*]->(container)-[:HAS_CHILD]->(component:Component)
            WHERE ID(p)={project_id}
            AND NOT component:Discussion
            RETURN container, collect(DISTINCT component) as components
            """

            result = await self.async_run(query)
            results = result.data()

            if not results:
                return None

            def format_node(node,container_id=None):
                properties = dict(node)
                properties['container_id'] = container_id
                return {
                    'id': node.identity,
                    'type': list(node.labels)[0] if node.labels else 'Unknown',
                    'properties': properties
                }

            result = results[0]
            formatted_result = {
                'data': [] 
            }
            for record in results:
                container = record['container']
                components = record['components']
                for component in components:
                    formatted_result['data'].append(
                        format_node(component, container.identity)
                    )

            return formatted_result

        except Exception as e:
            logger.error(f"Database error in get_container_components: {str(e)}")
            raise
        
        
    async def get_component_with_all_children(self, project_id: int) -> Dict[str, Any]:
        try:
            query = f"""
            MATCH (p:Project)-[:HAS_CHILD]->(sc:SystemContext)-[:HAS_CHILD|CONTAINS]->(container:Container)
            WHERE ID(p)={project_id}
            WITH container 
            MATCH (container)-[:HAS_CHILD]->(component:Component)
            WHERE NOT component:Discussion
            WITH component
            OPTIONAL MATCH (component)-[r:CHILDNODES|HAS_CHILD]->(x)
            WHERE NOT x:Discussion AND (x:Design OR x:Interface)
            RETURN component as component,
                collect(DISTINCT x) as childNodes,
                collect(DISTINCT type(r)) as relationshipTypes,
                collect(DISTINCT labels(x)) as nodeTypes
            """

            result = await self.async_run(query)
            results = result.data()
            

            if not results:
                return None

            def format_node(node,component_id=None):
                properties = dict(node)
                properties['component_id'] = component_id
                return {
                    'id': node.identity,
                    'type': list(node.labels)[0] if node.labels else 'Unknown',
                    'properties': properties
                }

            formatted_results = []
            for result in results:
                formatted_result = {
                    'data': {
                        'childNodes': [format_node(child, result['component'].identity) for child in result['childNodes']],
                        'relationshipTypes': result['relationshipTypes'],
                        'nodeTypes': result['nodeTypes']
                }
                
                }
                formatted_results.append(formatted_result)

            return formatted_result

        except Exception as e:
            logger.error(f"Database error in get_component_with_children: {str(e)}")
            raise
        
    
    async def get_interfaces_from_component(self, project_id: int, component_id: int) -> Dict[str, Any]:
        try:
            query = f"""
            MATCH (p:Project)-[:HAS_CHILD]->(sc:SystemContext)-[:HAS_CHILD|CONTAINS]->(container:Container)
            -[:HAS_CHILD]->(component:Component)
            WHERE ID(p)={project_id} AND ID(component)={component_id}
            WITH component
            MATCH (component)-[:HAS_CHILD]->(interface:Interface)
            WHERE NOT interface:Discussion
            RETURN collect(DISTINCT interface) as interfaces
            """

            result = await self.async_run(query)
            results = result.data()

            if not results:
                return None

            def format_node(node):
                properties = dict(node)
                return {
                    'id': node.identity,
                    'type': list(node.labels)[0] if node.labels else 'Unknown',
                    'properties': properties
                }

            formatted_result = {
                'data': {
                    'interfaces': [format_node(interface) for interface in results[0]['interfaces']]
                }
            }

            return formatted_result

        except Exception as e:
            logger.error(f"Database error in get_interfaces_from_component: {str(e)}")
            raise

    async def get_all_component_from_project(self, project_id: int) -> Dict[str, Any]:
        try:
            query = f"""
            // First get containers from project
            MATCH (p:Project)-[:HAS_CHILD]->(sc:SystemContext)-[:HAS_CHILD|CONTAINS]->(container:Container)
            WHERE ID(p)={project_id}
            WITH DISTINCT container
            
            // Get components for each container
            MATCH (container)-[:HAS_CHILD]->(component:Component)
            WHERE NOT component:Discussion
            
            // Group by container and collect components
            WITH 
                container,
                ID(container) as container_id,
                container.Title as container_name,
                COLLECT({{
                    id: ID(component),
                    name: component.name,
                    type: labels(component)[0],
                    properties: PROPERTIES(component)
                }}) as components
            ORDER BY container_name  // Moved ORDER BY here before final COLLECT
            
            // Return final result
            RETURN COLLECT({{
                container_id: container_id,
                container_name: container_name,
                components: components
            }}) as containers
            """
            
            result = await self.async_run(query)
            results = result.data()

            if not results:
                return {
                    "status": "success",
                    "containers": [],
                    "total_components": 0,
                    "message": "No components found for this project"
                }

            containers = results[0]['containers']
            total_components = sum(len(container['components']) for container in containers)

            return {
                "status": "success",
                "containers": containers,
                "total_components": total_components
            }

        except Exception as e:
            logger.error(f"Database error in get_all_component_from_project: {str(e)}")
            raise
    
    async def get_container_functional_requirements(self, project_id: int, container_id: int) -> Dict[str, Any]:
        try:
            query = f"""
            MATCH (p:Project)-[:HAS_CHILD]->(sc:SystemContext)-[:HAS_CHILD|CONTAINS*]->(container:Container)
            WHERE ID(p) = {project_id} AND ID(container) = {container_id} 
            WITH container, ID(container) as containerId, container.name as containerName
            MATCH (container)-[:IMPLEMENTS]->(fr:FunctionalRequirement) 
            WITH containerId, containerName, collect(DISTINCT {{id: ID(fr), properties: fr}}) as functional_requirements
            RETURN {{
                id: containerId,
                name: containerName,
                functional_requirements: functional_requirements
            }} as result
            """
            result = await self.async_run(query)
            data = result.data()
            return data[0]["result"] if data else None

        except Exception as e:
            logger.error(f"Database error in get_container_functional_requirements: {str(e)}")
            raise
        
    
    async def get_component_functional_requirements(self, project_id: int, component_id: int) -> Dict[str, Any]:
        try:
            query = f"""
            MATCH (p:Project)-[:HAS_CHILD]->(sc:SystemContext)-[:HAS_CHILD|CONTAINS*]->(container:Container)-[:HAS_CHILD]->(component:Component)
            WHERE ID(p) = {project_id} AND ID(component) = {component_id} 
            WITH component, ID(component) as componentId, component.name as componentName
            MATCH (component)-[:IMPLEMENTS]->(fr:FunctionalRequirement) 
            WITH componentId, componentName, collect(DISTINCT {{id: ID(fr), properties: fr}}) as functional_requirements
            RETURN {{
                id: componentId,
                name: componentName,
                functional_requirements: functional_requirements
            }} as result
            """
            result = await self.async_run(query)
            data = result.data()
            return data[0]["result"] if data else None

        except Exception as e:
            logger.error(f"Database error in get_component_functional_requirements: {str(e)}")
            raise
            
    async def get_component_deployments(self, project_id: int, container_id: int) -> Dict[str, Any]:
        try:
            query = f"""
            MATCH (n:Project)-[:HAS_CHILD]->(sc:SystemContext)-[:HAS_CHILD|CONTAINS*]->(c:Container)-[:HAS_CHILD]->(d:Deployment)
            WHERE ID(n) = {project_id} AND ID(c) = {container_id}
            WITH c, ID(c) as containerId, c.name as containerName, 
            collect(DISTINCT {{id: ID(d), properties: d}}) as deployments
            RETURN {{
                id: containerId,
                name: containerName,
                deployments: deployments
            }} as result
            """
            result = await self.async_run(query)
            data = result.data()
            return data[0]["result"] if data else None
        except Exception as e:
            logger.error(f"Database error in get_component_deployments: {str(e)}")
            raise

    async def get_requirement_counts(node_db, project_id: int) -> Dict[str, int]:
        query = """
        MATCH (p:Project)-[:HAS_CHILD|CONTAINS]->(rr:RequirementRoot)
        WHERE ID(p) = $project_id
        OPTIONAL MATCH (rr)-[:HAS_CHILD|CONTAINS]->(epic:Epic)
        OPTIONAL MATCH (epic)-[:HAS_CHILD|CONTAINS]->(story:UserStory)
        OPTIONAL MATCH (story)-[:HAS_CHILD|CONTAINS]->(task:Task)
        RETURN 
            COUNT(DISTINCT epic) as epicCount,
            COUNT(DISTINCT story) as userStoryCount,
            COUNT(DISTINCT task) as taskCount
        """
        result = await node_db.async_run(query, project_id=project_id)
        row = result[0] if result else None
        return {
            "epicCount": row["epicCount"] if row else 0,
            "userStoryCount": row["userStoryCount"] if row else 0,
            "taskCount": row["taskCount"] if row else 0
        }

    async def get_architecture_counts(node_db, project_id: int) -> Dict[str, int]:
        query = """
        MATCH (p:Project)-[:HAS_CHILD|CONTAINS]->(sc:SystemContext)
        WHERE ID(p) = $project_id AND NOT p:Discussion
        OPTIONAL MATCH (sc)-[:HAS_CHILD|CONTAINS]->(c:Container)
        WHERE NOT sc:Discussion AND NOT c:Discussion
        OPTIONAL MATCH (c)-[:HAS_CHILD|CONTAINS]->(comp:Component)
        WHERE NOT comp:Discussion
        OPTIONAL MATCH (c)-[:HAS_CHILD|CONTAINS]->(d:Deployment)
        WHERE NOT d:Discussion
        RETURN 
            COUNT(DISTINCT sc) as systemContextCount,
            COUNT(DISTINCT c) as containerCount,
            COUNT(DISTINCT comp) as componentCount,
            COUNT(DISTINCT d) as deploymentCount
        """
        result = await node_db.async_run(query, project_id=project_id)
        row = result[0] if result else None
        return {
            "systemContextCount": row["systemContextCount"] if row else 0,
            "containerCount": row["containerCount"] if row else 0,
            "componentCount": row["componentCount"] if row else 0,
            "deploymentCount": row["deploymentCount"] if row else 0
        }

    async def get_deployment_count(node_db, project_id: int) -> Dict[str, int]:
        query = """
        MATCH (p:Project)-[:HAS_CHILD|CONTAINS]->(sc:SystemContext)
        WHERE ID(p) = $project_id AND NOT p:Discussion
        OPTIONAL MATCH (sc)-[:HAS_CHILD|CONTAINS]->(c:Container)
        WHERE NOT sc:Discussion AND NOT c:Discussion
        OPTIONAL MATCH (c)-[:HAS_CHILD|CONTAINS]->(d:Deployment)
        WHERE NOT d:Discussion
        RETURN COUNT(DISTINCT d) as deploymentCount
        """
        result = await node_db.async_run(query, project_id=project_id)
        row = result[0] if result else None
        return {
            "deploymentCount": row["deploymentCount"] if row else 0
        }

    # async def get_project_titles(self, project_ids: List[int]) -> List[dict]:
    #     """
    #     Get project titles for a list of project IDs from Neo4j
        
    #     Args:
    #         project_ids: List of project IDs
    #     Returns:
    #         List of dicts containing project_id and project_title
    #     """
    #     try:
    #         print(f"Current Neo4j Database: {self.database}")
    #         print(f"Fetching titles for project IDs: {project_ids}")
            
    #         query = """
    #         MATCH (p:Project)
    #         WHERE ID(p) IN $project_ids
    #         RETURN (p.id) as project_id, p.Title as project_title
    #         """
            
    #         result = self.graph.run(query, parameters={"project_ids": project_ids})
    #         records = list(result)
            
    #         print(f"Found {len(records)} project titles in Neo4j")
            
    #         # Convert and format records
    #         formatted_records = [
    #             {
    #                 "project_id": record["project_id"],
    #                 "project_title": str(record["project_title"]) if record["project_title"] else "Unknown Project"
    #             }
    #             for record in records
    #         ]
            
    #         # Debug print each mapping
    #         for record in formatted_records:
    #             print(f"Project ID: {record['project_id']}, Title: {record['project_title']}")
                
    #         return formatted_records

    #     except Exception as e:
    #         print(f"Error fetching project titles: {str(e)}")
    #         print(f"Project IDs attempted: {project_ids}")
    #         return []
    
    async def get_project_titles(self, project_ids: List[int]) -> List[dict]:
        """
        Get project titles for a list of project IDs from Neo4j
        
        Args:
            project_ids: List of project IDs
        Returns:
            List of dicts containing project_id and project_title
        """
        try:
            print(f"Current Neo4j Database: {self.database}")
            print(f"Fetching titles for project IDs: {project_ids}")
            
            # Query using ID(n) pattern
            query = """
            MATCH (n:Project)
            WHERE ID(n) IN $project_ids
            RETURN {
                project_id: ID(n),
                project_title: n.Title
            } as result
            """
            
            result = self.graph.run(query, parameters={"project_ids": project_ids})
            records = list(result)
            
            print(f"Found {len(records)} project titles in Neo4j")
            
            # Convert and format records
            formatted_records = []
            for record in records:
                result = record["result"]
                project_id = int(result["project_id"]) if result["project_id"] is not None else None
                project_title = result["project_title"] if result["project_title"] else "Unknown Project"
                
                if project_id is not None:
                    formatted_records.append({
                        "project_id": project_id,
                        "project_title": str(project_title)
                    })
                    print(f"Project ID: {project_id}, Title: {project_title}")
            
            print("Final formatted records:", formatted_records)
            return formatted_records

        except Exception as e:
            print(f"Error fetching project titles: {str(e)}")
            print(f"Project IDs attempted: {project_ids}")
            return []
         

    async def mark_node_unused(self, node_ids, project_id, mongo_handler, labels_to_add = None, rel_types_to_process = None):  
        try:
            reconfig_doc = await mongo_handler.get_one(
                    {"project_id": project_id},
                    mongo_handler.db["reconfig_tracker.tasks"]
                )
            if not isinstance(node_ids, list):
                node_ids = [node_ids]
                
            if labels_to_add is None:
                labels_to_add = ["Unused"]
                
            if rel_types_to_process is None:
                rel_types_to_process = ["HAS_CHILD"]
            
            # Ensure rel_types_to_process is a list
            if not isinstance(rel_types_to_process, list):
                rel_types_to_process = [rel_types_to_process]
            
            timestamp = generate_timestamp()

            # First query to handle parent nodes
            parent_query = """
            MATCH (n)
            WHERE ID(n) IN $node_ids
            WITH n, labels(n) as oldLabels
            
            // Store original labels and metadata
            SET n.original_labels = oldLabels
            SET n.deleted_at = datetime($timestamp)
            
            // Add Unused label
            SET n:Unused
            
            // Return node ID and its labels for processing
            RETURN ID(n) as node_id, oldLabels as labels
            """

            parent_result = await self.async_run(parent_query, node_ids=node_ids, timestamp=timestamp)
            parent_data = parent_result.data()

            # Build a dynamic relationship query based on rel_types_to_process
            
            rel_types_clause = " OR ".join([f"type(r) = '{rel_type}'" for rel_type in rel_types_to_process])
            
            relationship_query = f"""
            MATCH (n)-[r]->(m)
            WHERE (ID(n) IN $node_ids OR ID(m) IN $node_ids)
            AND ({rel_types_clause})
            RETURN ID(r) as rel_id, type(r) as rel_type, ID(startNode(r)) as source_id, ID(endNode(r)) as target_id
            """

            rel_result = await self.async_run(relationship_query, node_ids=node_ids)
            rel_data = rel_result.data()
            
            # Find child nodes using the HAS_CHILD relationship (if it's in the list to process)
            child_ids = []
            if "HAS_CHILD" in rel_types_to_process:
                children_query = """
                MATCH (n)
                WHERE ID(n) IN $node_ids
                MATCH (n)-[:HAS_CHILD*]->(child)
                WHERE NOT 'Discussion' IN labels(child)
                RETURN DISTINCT ID(child) as child_id
                """
                
                children_result = await self.async_run(children_query, node_ids=node_ids)
                children_result = children_result.data()
                child_ids = [record['child_id'] for record in children_result]
            
            # Get labels for child nodes
            child_data = []
            if child_ids:
                child_labels_query = """
                MATCH (c)
                WHERE ID(c) IN $child_ids
                RETURN ID(c) as node_id, labels(c) as labels
                """
                
                child_labels_result = await self.async_run(child_labels_query, child_ids=child_ids)
                child_data = child_labels_result.data()
                
                # Mark child nodes as unused and store metadata
                child_update_query = """
                MATCH (c)
                WHERE ID(c) IN $child_ids
                WITH c, labels(c) as oldLabels
                SET c.original_labels = oldLabels
                SET c.deleted_at = datetime($timestamp)
                SET c:Unused
                RETURN COUNT(c) as updated_count
                """
                
                result = await self.async_run(child_update_query, child_ids=child_ids, timestamp=timestamp)
            
            # Process all nodes to remove their labels
            all_nodes_data = parent_data + child_data
            
            for record in all_nodes_data:
                node_id = record['node_id']
                labels = record['labels']
                
                # Skip the labels we want to keep
                labels_to_remove = [label for label in labels if label not in labels_to_add]
                
                if not labels_to_remove:
                    print(f"No labels to remove for node {node_id}")
                    continue
                
                # CRITICAL FIX: Use parameter binding for node ID and explicitly build REMOVE clauses
                remove_statements = []
                for label in labels_to_remove:
                    remove_statements.append(f"REMOVE n:{label}")
                
                remove_query = f"""
                MATCH (n)
                WHERE ID(n) = $node_id
                {' '.join(remove_statements)}
                RETURN COUNT(n) as updated
                """
                
                result = await self.async_run(remove_query, node_id=node_id)
                
            # Process relationships by type
            if rel_data:
                # Group relationships by type
                rel_by_type = {}
                for rel in rel_data:
                    rel_type = rel['rel_type']
                    if rel_type not in rel_by_type:
                        rel_by_type[rel_type] = []
                    rel_by_type[rel_type].append(rel['rel_id'])
                
                # Process each relationship type
                for rel_type, rel_ids in rel_by_type.items():
                    # Determine new relationship type
                    if rel_type.startswith('HAS_'):
                        new_rel_type = 'HAD' + rel_type[3:]
                    else:
                        new_rel_type = 'HAD_' + rel_type
                    
                    # Change relationships in batch
                    change_relations_query = f"""
                    UNWIND $rel_ids AS rel_id
                    MATCH ()-[r:{rel_type}]->() 
                    WHERE ID(r) = rel_id
                    WITH r, startNode(r) as source, endNode(r) as target, properties(r) as props
                    DELETE r
                    CREATE (source)-[:{new_rel_type} {{
                        original_type: $rel_type,
                        deleted_at: datetime($timestamp)
                    }}]->(target)
                    RETURN COUNT(*) as updated
                    """
                    
                    result = await self.async_run(
                        change_relations_query, 
                        rel_ids=rel_ids, 
                        rel_type=rel_type,
                        timestamp=timestamp
                    )

            print('Final result: node_ids + child_ids', node_ids, child_ids)
            
            # MongoDB integration to track deleted nodes
            if reconfig_doc:
                # Get all affected node IDs
                all_affected_ids = node_ids + child_ids
                
                # Update nodes in affected_nodes list to add deleted flag
                print('all_affected_ids', all_affected_ids)

                update_result = await mongo_handler.update_with_nested_object_and_filters(
                    {
                        "project_id": project_id,
                        "affected_nodes": { "$exists": True }
                    },
                    {
                        "affected_nodes.$[elem].is_deleted": True,
                        "affected_nodes.$[elem].deleted_at": timestamp
                    },
                    [{"elem.node_id": {"$in": all_affected_ids}}],
                    mongo_handler.db["reconfig_tracker.tasks"]
                )

            return True

        except Exception as e:
            import traceback
            print('Error in mark_node_unused:', str(e))
            print('traceback-->', traceback.format_exc())
            logger.error(f"Error in mark_node_unused: {str(e)}")
            raise

    async def get_test_cases(self, project_id: int):
        # Retrieve the TestCaseRoot node for the project
        test_case_root = await self.get_child_nodes(project_id, "TestCaseRoot")
        if not test_case_root:
            return []  # No test cases if TestCaseRoot doesn't exist
        
        # Fetch all test cases under the TestCaseRoot
        test_case_root_id = test_case_root[0]['id']
        test_cases = await self.get_child_nodes(test_case_root_id, "Test")
        return test_cases
    
    async def get_interfaces_from_component(self, node_id: int) -> Dict[str, Any]:
        try:
            # Query that works for both containers and components
            query = f"""
            MATCH (n)
            WHERE ID(n) = {node_id}
            WITH n
            MATCH (n)-[:HAS_CHILD]->(interface:Interface)
            WHERE NOT interface:Discussion
            RETURN collect(DISTINCT interface) as interfaces
            """

            result = await self.async_run(query)
            results = result.data()

            if not results:
                return {
                    'data': {
                        'interfaces': []
                    }
                }

            def format_node(node):
                if not node:
                    return None
                properties = dict(node)
                return {
                    'id': node.identity,
                    'type': list(node.labels)[0] if node.labels else 'Unknown',
                    'properties': properties
                }

            formatted_result = {
                'data': {
                    'interfaces': [format_node(interface) for interface in results[0]['interfaces'] if interface]
                }
            }

            return formatted_result

        except Exception as e:
            self.update_logger.error(f"Database error in get_interfaces_from_component: {str(e)}")
            # Return empty result on error instead of raising
            return {
                'data': {
                    'interfaces': []
                }
            }
        

    # create a db node with api_data as a dictionary for attributes
    async def clone_node(self, source_node_id, current_user, title):
        try:
            query = """
            // Get the source node that will be cloned using the internal node ID
            MATCH (source: Project) WHERE id(source) = $source_node_id
            WITH source, labels(source) AS sourceLabels

            // Create the new top-level clone node
            CREATE (clone)
            // Use APOC to add labels dynamically
            WITH clone, source, sourceLabels
            CALL apoc.create.addLabels([id(clone)], sourceLabels) YIELD node
            SET clone = source
            // Set current date for created_at property and created_by field
            SET clone.created_at = toString(datetime())
            SET clone.created_by = $current_user
            SET clone.cloneId = 'cloned-' + toString(timestamp())
            // SET clone.Title = source.Title + '-copy'
            SET clone.Title = coalesce($title, source.Title + '-copy')

            // Add the required WITH clause
            WITH source, clone

            // Get the complete subgraph
            CALL apoc.path.subgraphAll(source, {})
            YIELD nodes, relationships

            // Filter out Discussion nodes from the nodes to be cloned
            WITH source, clone, 
                [n IN nodes WHERE NONE(label IN labels(n) WHERE label IN ['Discussion', 'User'])] AS filteredNodes,
                [r IN relationships WHERE NONE(label IN labels(endNode(r)) WHERE label IN ['Discussion', 'User'])] AS filteredRelationships
            // Clone the subgraph connected to the source node
            CALL apoc.refactor.cloneSubgraph(
            filteredNodes, 
            filteredRelationships, 
            {
                standinNodes: [[source, clone]]
            }
            )
            YIELD input, output, error

            // Return only the clone node's ID and use DISTINCT to remove duplicates
            RETURN DISTINCT id(clone) AS cloneId
            """
            resp = await self.async_run(query, source_node_id=source_node_id, current_user=current_user, title=title)
            return resp
        
        except Exception as e:
            return {"status":"error","message": str(e)}

    async def get_interfaces_from_container(self, container_id: int) -> Dict[str, Any]:
        """
        Fetch interfaces that are direct children of a Container node.
        """
        try:
            query = f"""
            MATCH (container:Container)-[:HAS_CHILD]->(interface:Interface)
            WHERE ID(container) = {container_id}
            RETURN collect(DISTINCT interface) as interfaces
            """
            result = await self.async_run(query)
            results = result.data()

            if not results:
                return {
                    'data': {
                        'interfaces': []
                    }
                }

            def format_node(node):
                if not node:
                    return None
                properties = dict(node)
                return {
                    'id': node.identity,
                    'type': list(node.labels)[0] if node.labels else 'Unknown',
                    'properties': properties
                }

            formatted_result = {
                'data': {
                    'interfaces': [format_node(interface) for interface in results[0]['interfaces'] if interface]
                }
            }

            return formatted_result

        except Exception as e:
            self.update_logger.error(f"Database error in get_interfaces_from_container: {str(e)}")
            # Return empty result on error instead of raising
            return {
                'data': {
                    'interfaces': []
                }
            }
