from fastapi import APIRouter, HTTPException,Depends
from app.services.supabase_oauth import supabase_service
from app.services.supabase_database_service import SupabaseDatabaseService
from app.services.supabase_service import SupabaseLambdaService
import secrets
from datetime import datetime
from app.utils.auth_utils import get_current_user
from app.services.supabase_bootstrap import bootstrap_supabase_project_k8s, generate_supabase_connection_url
import os
from typing import Optional 
from datetime import datetime
from fastapi.responses import HTMLResponse
from app.utils.hash import encrypt_data
from pydantic import BaseModel, Field
from app.utils.hash import encrypt_data, decrypt_data
import secrets
import string
from pydantic import BaseModel, Field
from app.services.supabase_management_service import SupabaseManagementService
from app.core.Settings import settings
from app.connection.establish_db_connection import get_mongo_db
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME
from app.telemetry.logger_config import get_logger
import urllib.parse

router = APIRouter(prefix="/supabase", tags=["supabase"])


# Initialize logger for Supabase operations
logger = get_logger(__name__)
public_auth_router = APIRouter()

class UpdateSupabaseDBRequest(BaseModel):
    project_id: str = Field(..., min_length=1, description="The main project ID")
    project_id_supabase: str = Field(..., min_length=1, description="The Supabase project ID")
    db_password: str = Field(..., min_length=8, description="Database password for Supabase project")

class ExecuteSQLLambdaRequest(BaseModel):
    project_id: str = Field(..., description="Your main project ID")
    sql_query: str = Field(..., min_length=1, description="SQL query to execute via Lambda")
class UpdateSupabaseDBRequest(BaseModel):
    project_id: str = Field(..., min_length=1, description="The main project ID")
    project_id_supabase: str = Field(..., min_length=1, description="The Supabase project ID")
    db_password: str = Field(..., min_length=8, description="Database password for Supabase project")

class CreateProjectRequest(BaseModel):
    name: str = Field(..., min_length=1, max_length=50, description="Project name")
    organization_id: str = Field(..., description="Organization ID")
    region: str = Field(default="us-east-1", description="AWS region")
    db_password: Optional[str] = Field(None, min_length=8, description="Database password")
    

def should_use_lambda_bootstrap(user_tier: str = "free") -> bool:
    """Determine if we should use Lambda bootstrap based on user tier"""
    # For now, use Lambda for all users for better performance
    return True

# Initialize the services
lambda_service = SupabaseLambdaService()
supabase_db_service = SupabaseDatabaseService()
supabase_mgmt_service = SupabaseManagementService()

@router.get("/connect-supabase/{project_id}")
async def connect_supabase(
    project_id: str,
    current_user = Depends(get_current_user)
):
    """
    Single endpoint to initiate Supabase OAuth connection
    Returns auth URL instead of redirecting
    """
    try:
        user_id=current_user.get("cognito:username")  # Use 'sub' as we discussed earlier
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        print(f"[DEBUG] Processing OAuth for user_id: {user_id}, project_id: {project_id}")
        
        # Check if user already has Supabase connected for this project
        existing_tokens = await supabase_db_service.get_user_supabase_tokens(user_id, project_id)
        
        if existing_tokens:
            print(f"[DEBUG] Supabase already connected for this project")
            return {
                "success": True,
                "message": "Supabase already connected for this project",
                "status": "already_connected",
                "connected_at": existing_tokens.get("connected_at"),
                "auth_url": None
            }
        
        # Generate OAuth URL
        try:
            auth_url, state, code_verifier = supabase_service.get_authorization_url(user_id, project_id)
            print(f"[SUCCESS] Generated OAuth URL successfully")
        except Exception as e:
            print(f"[ERROR] Error in get_authorization_url: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to generate OAuth URL: {str(e)}")
        
        # Store OAuth state
        try:
            store_result = await supabase_db_service.store_oauth_state(state, code_verifier, user_id, project_id)
            if store_result.get("success"):
                print(f"[SUCCESS] Stored OAuth state successfully")
            else:
                raise Exception(store_result.get("error", "Unknown error"))
        except Exception as e:
            print(f"[ERROR] Error storing OAuth state: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to store OAuth state: {str(e)}")
        
        print(f"[DEBUG] AUTH_URL = {auth_url}")
        
        # Return auth URL in dictionary format
        return {
            "success": True,
            "message": "OAuth URL generated successfully",
            "status": "auth_url_generated",
            "auth_url": auth_url,
            "data": {
                "user_id": user_id,
                "project_id": project_id,
                "state": state
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error in connect_supabase: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to initiate OAuth: {str(e)}")


@public_auth_router.get("/auth/supabase/callback")
async def supabase_oauth_callback(
    code: str,
    state: str,
    error: Optional[str] = None
):
    """
    Handle OAuth callback and send message to parent window
    """
    
    if error:
        # Return error page that sends message to parent
        return HTMLResponse(f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Supabase Connection Error</title>
        </head>
        <body>
            <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                <h2>Connection Failed</h2>
                <p>Error: {error}</p>
                <p>This window will close automatically...</p>
            </div>
            <script>
                window.opener.postMessage({{
                    type: 'SUPABASE_OAUTH_ERROR',
                    error: '{error}'
                }}, window.location.origin);
                setTimeout(() => window.close(), 2000);
            </script>
        </body>
        </html>
        """)

    try:
        print(f"[DEBUG] Processing OAuth callback - code: {code[:10]}..., state: {state}")
        
        # 1. Get and validate the stored state
        oauth_data = await supabase_db_service.get_oauth_state(state)
        
        if not oauth_data:
            return HTMLResponse("""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Supabase Connection Error</title>
            </head>
            <body>
                <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                    <h2>Connection Failed</h2>
                    <p>Invalid or expired OAuth state</p>
                    <p>This window will close automatically...</p>
                </div>
                <script>
                    window.opener.postMessage({
                        type: 'SUPABASE_OAUTH_ERROR',
                        error: 'Invalid or expired OAuth state'
                    }, window.location.origin);
                    setTimeout(() => window.close(), 2000);
                </script>
            </body>
            </html>
            """)
        
        user_id = oauth_data["user_id"]
        project_id = oauth_data["project_id"]
        code_verifier = oauth_data["code_verifier"]
        
        # 2. Exchange authorization code for tokens
        tokens = await supabase_service.exchange_code_for_tokens(code, code_verifier)
        
        if isinstance(tokens, dict) and "error" in tokens:
            return HTMLResponse(f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Supabase Connection Error</title>
            </head>
            <body>
                <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                    <h2>Connection Failed</h2>
                    <p>Token exchange failed: {tokens['error']}</p>
                    <p>This window will close automatically...</p>
                </div>
                <script>
                    window.opener.postMessage({{
                        type: 'SUPABASE_OAUTH_ERROR',
                        error: 'Token exchange failed: {tokens["error"]}'
                    }}, window.location.origin);
                    setTimeout(() => window.close(), 2000);
                </script>
            </body>
            </html>
            """)
        
        # 3. Store credentials
        store_result = await supabase_db_service.store_user_supabase_tokens(user_id, project_id, tokens)
        
        if not store_result.get("success"):
            return HTMLResponse(f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Supabase Connection Error</title>
            </head>
            <body>
                <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                    <h2>Connection Failed</h2>
                    <p>Failed to store credentials: {store_result.get('error')}</p>
                    <p>This window will close automatically...</p>
                </div>
                <script>
                    window.opener.postMessage({{
                        type: 'SUPABASE_OAUTH_ERROR',
                        error: 'Failed to store credentials: {store_result.get("error")}'
                    }}, window.location.origin);
                    setTimeout(() => window.close(), 2000);
                </script>
            </body>
            </html>
            """)
        
        # 4. Update project status
        try:
            from app.connection.establish_db_connection import get_node_db
            node_db = get_node_db()
            await node_db.update_node_by_id(
                project_id,
                {
                    "supabase_connected": True,
                    "supabase_connected_at": datetime.utcnow().isoformat()
                }
            )
        except Exception as e:
            print(f"[WARNING] Failed to update project status: {e}")
        
        # 5. Return success page that sends message to parent
        connected_at = datetime.utcnow().isoformat()
        
        return HTMLResponse(f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Supabase Connected Successfully</title>
        </head>
        <body>
            <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                <div style="width: 64px; height: 64px; background: #10B981; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
                    <svg width="32" height="32" fill="white" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <h2 style="color: #10B981; margin-bottom: 10px;">Successfully Connected!</h2>
                <p style="color: #6B7280;">Your Supabase project is now connected.</p>
                <p style="color: #6B7280; font-size: 14px;">This window will close automatically...</p>
            </div>
            <script>
                window.opener.postMessage({{
                    type: 'SUPABASE_OAUTH_SUCCESS',
                    projectId: '{project_id}',
                    connectedAt: '{connected_at}',
                    status: 'connected'
                }}, window.location.origin);
                setTimeout(() => window.close(), 3000);
            </script>
        </body>
        </html>
        """)
        
    except Exception as e:
        print(f"[ERROR] Callback processing failed: {str(e)}")
        return HTMLResponse(f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Supabase Connection Error</title>
        </head>
        <body>
            <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                <h2>Connection Failed</h2>
                <p>Callback processing failed: {str(e)}</p>
                <p>This window will close automatically...</p>
            </div>
            <script>
                window.opener.postMessage({{
                    type: 'SUPABASE_OAUTH_ERROR',
                    error: 'Callback processing failed: {str(e)}'
                }}, window.location.origin);
                setTimeout(() => window.close(), 2000);
            </script>
        </body>
        </html>
        """)
        

@router.post("/supabase/bootstrap/{project_id_supabase}")
async def bootstrap_supabase_project_endpoint(
    project_id: str,
    project_id_supabase: str,
    db_password: str = None,
    current_user = Depends(get_current_user)
):
    """
    Bootstrap Supabase project with K8s-aware connection handling
    """
    try:
        user_id = current_user.get("cognito:username")
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")

        print(f"[DEBUG] Bootstrapping Supabase project: {project_id_supabase}")

        # Try stored credentials first
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        collection = mongo_db.db["supabase_user_credentials"]

        credentials = collection.find_one({
            "user_id": user_id,
            "project_id": project_id,
            "supabase_project_id": project_id_supabase
        })

        if credentials:
            from app.utils.hash import decrypt_data
            db_url = decrypt_data(credentials["db_url"])
            print(f"[DEBUG] Using stored credentials")
        elif db_password:
            # Generate proper connection URL based on environment
            is_k8s = 'KUBERNETES_SERVICE_HOST' in os.environ
            db_url = generate_supabase_connection_url(
                project_ref=project_id_supabase,
                password=db_password,
                use_pooler=is_k8s,  # Auto-detect K8s
                region=os.getenv('SUPABASE_REGION', 'ap-south-1')
            )
            print(f"[DEBUG] Generated {'pooler' if is_k8s else 'direct'} connection URL")
        else:
            raise HTTPException(
                status_code=400,
                detail="Database password required"
            )

        # Bootstrap with K8s-aware auto-fix
        import asyncio
        max_retries = 3
        retry_delay = 30

        for attempt in range(max_retries):
            try:
                print(f"[DEBUG] Bootstrap attempt {attempt + 1}/{max_retries}")

                # Use K8s-aware bootstrap with auto-fix
                bootstrap_result = bootstrap_supabase_project_k8s(
                    db_url, 
                    verbose=True, 
                    auto_fix=True  # Auto-fix connection strings
                )

                if bootstrap_result.success:
                    print(f"[SUCCESS] Bootstrap completed: {project_id_supabase}")
                    return {
                        "success": True,
                        "message": "Bootstrap completed successfully",
                        "project_id": project_id_supabase,
                        "attempts": attempt + 1,
                        "connection_type": bootstrap_result.details.get("connection_type", "unknown")
                    }
                else:
                    print(f"[WARNING] Attempt {attempt + 1} failed: {bootstrap_result.message}")
                    
                    # Check if it's an auth error (no point retrying)
                    if "Tenant or user not found" in bootstrap_result.message:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Authentication failed. Check project reference: {project_id_supabase}"
                        )
                    
                    if attempt < max_retries - 1:
                        print(f"[INFO] Waiting {retry_delay}s before retry...")
                        await asyncio.sleep(retry_delay)
                    else:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Bootstrap failed after {max_retries} attempts: {bootstrap_result.message}"
                        )

            except HTTPException:
                raise
            except Exception as e:
                print(f"[ERROR] Attempt {attempt + 1} error: {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
                else:
                    raise HTTPException(
                        status_code=500,
                        detail=f"Bootstrap failed: {str(e)}"
                    )

    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Bootstrap failed: {str(e)}")


@router.get("/bootstrap-status/{project_id_supabase}")
async def check_bootstrap_status(
    project_id: str,
    project_id_supabase: str,
    current_user = Depends(get_current_user)
):
    try:
        user_id = current_user.get("cognito:username")
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")

        # Get stored credentials
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        collection = mongo_db.db["supabase_user_credentials"]

        credentials = collection.find_one({
            "user_id": user_id,
            "project_id": project_id,
            "supabase_project_id": project_id_supabase
        })

        if not credentials:
            return {
                "success": False,
                "bootstrap_status": {"is_fully_bootstrapped": False},
                "message": "No credentials found"
            }

        # Check if bootstrap was completed via Lambda
        lambda_bootstrap_completed = credentials.get("bootstrap_completed", False)
        
        if lambda_bootstrap_completed:
            return {
                "success": True,
                "bootstrap_status": {"is_fully_bootstrapped": True},
                "message": "Bootstrap completed via Lambda"
            }

        # Fallback: Check actual database status
        from app.utils.hash import decrypt_data
        db_url = decrypt_data(credentials["db_url"])

        from app.services.supabase_bootstrap import SupabaseBootstrap
        bootstrap = SupabaseBootstrap(db_url, verbose=False)
        status = bootstrap.status()

        return {
            "success": True,
            "project_id": project_id_supabase,
            "bootstrap_status": status,
            "is_fully_bootstrapped": all(status.values()),
            "message": "Bootstrap status retrieved successfully"
        }

    except Exception as e:
        print(f"[ERROR] Error checking bootstrap status: {str(e)}")
        return {
            "success": False,
            "bootstrap_status": {"is_fully_bootstrapped": False},
            "message": f"Error checking status: {str(e)}"
        }
        
@router.get("/supabase/project-readiness/{project_id_supabase}")
async def check_project_readiness(
    project_id: str,
    project_id_supabase: str,
    current_user = Depends(get_current_user)
):
    """
    Check if a Supabase project is ready for use (status check + bootstrap status)
    """
    try:
        user_id = current_user.get("cognito:username")
        if not user_id:
            logger.error(f"[SUPABASE_READINESS] User ID not found in token for project: {project_id_supabase}")
            raise HTTPException(status_code=401, detail="User ID not found in token")

        logger.info(f"[SUPABASE_READINESS] Checking project readiness for user: {user_id}, project: {project_id_supabase}")

        # Get project details to check status
        project_response = await supabase_mgmt_service.get_supabase_project_details(
            project_id, project_id_supabase, user_id
        )

        if not project_response.get("success"):
            raise HTTPException(
                status_code=400,
                detail=project_response.get("message", "Failed to get project details")
            )

        project_details = project_response["data"]
        project_status = project_details.get("status", "UNKNOWN")

        # Helper function to determine project readiness (same as in list endpoint)
        def get_project_readiness_info(status):
            if status == "ACTIVE_HEALTHY":
                return {
                    "is_ready": True,
                    "can_continue": True,
                    "status_message": "Ready to use",
                    "ui_status": "success",
                    "description": "Project is fully initialized and ready for database operations"
                }
            elif status in ["COMING_UP", "INITIALIZING", "RESTORING"]:
                return {
                    "is_ready": False,
                    "can_continue": False,
                    "status_message": "Setting up...",
                    "ui_status": "loading",
                    "description": "Supabase project setup may take a few seconds. Please wait."
                }
            elif status in ["PAUSING", "PAUSED"]:
                return {
                    "is_ready": False,
                    "can_continue": False,
                    "status_message": "Project paused",
                    "ui_status": "warning",
                    "description": "Project is paused. Resume it from Supabase dashboard to continue."
                }
            elif status in ["INACTIVE", "UNKNOWN"]:
                return {
                    "is_ready": False,
                    "can_continue": False,
                    "status_message": "Project inactive",
                    "ui_status": "error",
                    "description": "Project appears to be inactive. Check Supabase dashboard for details."
                }
            else:
                return {
                    "is_ready": False,
                    "can_continue": False,
                    "status_message": f"Status: {status}",
                    "ui_status": "warning",
                    "description": "Project status unknown. Please check Supabase dashboard."
                }

        readiness_info = get_project_readiness_info(project_status)

        # If project is ready, also check bootstrap status
        bootstrap_status = None
        if readiness_info["is_ready"]:
            try:
                # Check if we have stored credentials to check bootstrap status
                mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
                collection = mongo_db.db["supabase_user_credentials"]

                credentials = collection.find_one({
                    "user_id": user_id,
                    "project_id": project_id,
                    "supabase_project_id": project_id_supabase
                })

                if credentials:
                    from app.utils.hash import decrypt_data
                    db_url = decrypt_data(credentials["db_url"])

                    from app.services.supabase_bootstrap import SupabaseBootstrap
                    bootstrap = SupabaseBootstrap(db_url, verbose=False)
                    bootstrap_status = bootstrap.status()

            except Exception as e:
                print(f"[WARNING] Could not check bootstrap status: {str(e)}")
                bootstrap_status = {"error": "Could not check bootstrap status"}

        return {
            "success": True,
            "project_id": project_id_supabase,
            "status": project_status,
            "readiness": readiness_info,
            "bootstrap_status": bootstrap_status,
            "message": "Project readiness checked successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Error checking project readiness: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to check project readiness: {str(e)}")

@router.get("/supabase/projects")
async def list_supabase_projects(
    project_id: str,
    organization_id: Optional[str] = None,
    include_keys: bool = True,
    include_all_status: bool = True,
    current_user = Depends(get_current_user)
):
    """
    List all Supabase projects with optional API keys and status information

    Args:
        include_all_status: If True, includes projects in all states (ACTIVE_HEALTHY, INITIALIZING, etc.)
                           If False, only includes ACTIVE_HEALTHY projects
    """
    try:
        user_id=current_user.get("cognito:username")
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        print(f"[DEBUG] Listing Supabase projects for user: {user_id}")
        
        # Get projects from Supabase Management API
        result = await supabase_mgmt_service.list_projects(user_id=user_id,project_id=project_id,organization_id=organization_id)
        
        if not result.get("success"):
            print(f"[ERROR] Failed to fetch projects: {result.get('error')}")
            raise HTTPException(
                status_code=400,
                detail=f"Failed to fetch projects: {result.get('error')}"
            )
        
        projects = result["data"]
        enhanced_projects = []

        # Helper function to determine project readiness
        def get_project_readiness_info(status):
            """Determine if project is ready and provide UI guidance"""
            if status == "ACTIVE_HEALTHY":
                return {
                    "is_ready": True,
                    "can_continue": True,
                    "status_message": "Ready to use",
                    "ui_status": "success",
                    "description": "Project is fully initialized and ready for database operations"
                }
            elif status in ["COMING_UP", "INITIALIZING", "RESTORING"]:
                return {
                    "is_ready": False,
                    "can_continue": False,
                    "status_message": "Setting up...",
                    "ui_status": "loading",
                    "description": "Supabase project setup may take a few seconds. Please wait."
                }
            elif status in ["PAUSING", "PAUSED"]:
                return {
                    "is_ready": False,
                    "can_continue": False,
                    "status_message": "Project paused",
                    "ui_status": "warning",
                    "description": "Project is paused. Resume it from Supabase dashboard to continue."
                }
            elif status in ["INACTIVE", "UNKNOWN"]:
                return {
                    "is_ready": False,
                    "can_continue": False,
                    "status_message": "Project inactive",
                    "ui_status": "error",
                    "description": "Project appears to be inactive. Check Supabase dashboard for details."
                }
            else:
                return {
                    "is_ready": False,
                    "can_continue": False,
                    "status_message": f"Status: {status}",
                    "ui_status": "warning",
                    "description": "Project status unknown. Please check Supabase dashboard."
                }
        
        for project in projects:
            project_id_supabase = project.get("id")
            
            # Check if project is connected (has OAuth tokens)
            try:
                tokens = await supabase_db_service.get_user_supabase_tokens(user_id, project_id)
                is_connected = bool(tokens)
                connected_at = tokens.get("connected_at") if tokens else None
            except:
                is_connected = False
                connected_at = None
            
            # Get API keys - try multiple approaches
            anon_key = None
            service_role_key = None
            
            if include_keys:
                try:
                    # Method 1: Try dedicated API keys endpoint
                    keys_result = await supabase_mgmt_service.get_project_api_keys(user_id,project_id,project_id_supabase)
                    if keys_result.get("success"):
                        keys_data = keys_result["data"]
                        if isinstance(keys_data, list):
                            for key in keys_data:
                                if key.get("name") == "anon":
                                    anon_key = key.get("api_key")
                                elif key.get("name") == "service_role":
                                    service_role_key = key.get("api_key")
                        else:
                            # Sometimes returned as dict
                            anon_key = keys_data.get("anon")
                            service_role_key = keys_data.get("service_role")
                    
                    # Method 2: If that fails, try project details endpoint
                    if not anon_key and not service_role_key:
                        details_result = await supabase_mgmt_service.get_project_details(user_id,project_id,project_id_supabase)
                        if details_result.get("success"):
                            project_details = details_result["data"]
                            anon_key = project_details.get("anon_key")
                            service_role_key = project_details.get("service_role_key")
                            
                            # Sometimes keys are nested
                            if not anon_key:
                                api_keys = project_details.get("api_keys", {})
                                anon_key = api_keys.get("anon")
                                service_role_key = api_keys.get("service_role")
                    
                except Exception as e:
                    print(f"[WARNING] Failed to fetch API keys for project {project_id_supabase}: {e}")
                    # If all else fails, extract from the original project data
                    anon_key = project.get("anon_key")
                    service_role_key = project.get("service_role_key")
            
            # Get readiness information based on status
            project_status = project.get("status", "UNKNOWN")
            readiness_info = get_project_readiness_info(project_status)

            enhanced_project = {
                "id": project_id_supabase,
                "name": project.get("name"),
                "organization_id": project.get("organization_id"),
                "region": project.get("region"),
                "status": project_status,
                "created_at": project.get("created_at"),
                "database_url": project.get("database", {}).get("host") if project.get("database") else f"db.{project_id_supabase}.supabase.co",
                "api_url": f"https://{project_id_supabase}.supabase.co",
                "anon_key": anon_key,
                "service_role_key": service_role_key,
                "is_connected": is_connected,
                "connected_at": connected_at,
                "dashboard_url": f"https://supabase.com/dashboard/project/{project_id_supabase}",
                # New readiness fields for UI
                "is_ready": readiness_info["is_ready"],
                "can_continue": readiness_info["can_continue"],
                "status_message": readiness_info["status_message"],
                "ui_status": readiness_info["ui_status"],
                "description": readiness_info["description"]
            }

            # Apply filtering if requested
            if include_all_status or enhanced_project["is_ready"]:
                enhanced_projects.append(enhanced_project)

        # Create status summary
        status_summary = {}
        ready_count = 0
        for project in enhanced_projects:
            status = project["status"]
            ui_status = project["ui_status"]

            if status not in status_summary:
                status_summary[status] = 0
            status_summary[status] += 1

            if project["is_ready"]:
                ready_count += 1

        print(f"[SUCCESS] Retrieved {len(enhanced_projects)} projects ({ready_count} ready)")

        return {
            "success": True,
            "message": f"Retrieved {len(enhanced_projects)} projects ({ready_count} ready)",
            "data": enhanced_projects,
            "count": len(enhanced_projects),
            "ready_count": ready_count,
            "status_summary": status_summary,
            "organization_id": organization_id,
            "include_all_status": include_all_status
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error listing projects: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to list projects: {str(e)}")
    

@router.get("/supabase/projects/{project_id_supabase}")
async def get_supabase_project_details__(
    project_id: str,
    project_id_supabase: str,
    current_user = Depends(get_current_user)
):
    """
    Get detailed information for a specific Supabase project
    """
    try:
        user_id=current_user.get("cognito:username")
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        print(f"[DEBUG] Getting project details for: {project_id_supabase}")
        
        # Get project details
        details_result = await supabase_mgmt_service.get_project_details(user_id,project_id,project_id_supabase)
        if not details_result.get("success"):
            raise HTTPException(
                status_code=404,
                detail=f"Project not found: {details_result.get('error')}"
            )
        
        project_data = details_result["data"]
        
        # Get API keys
        keys_result = await supabase_mgmt_service.get_project_api_keys(user_id,project_id,project_id_supabase)
        anon_key = None
        service_role_key = None
        
        if keys_result.get("success"):
            keys_data = keys_result["data"]
            for key in keys_data:
                if key.get("name") == "anon":
                    anon_key = key.get("api_key")
                elif key.get("name") == "service_role":
                    service_role_key = key.get("api_key")
        
        # Check connection status
        try:
            tokens = await supabase_db_service.get_user_supabase_tokens(user_id, project_id)
            is_connected = bool(tokens)
            connected_at = tokens.get("connected_at") if tokens else None
        except:
            is_connected = False
            connected_at = None
        
        return {
            "success": True,
            "message": "Project details retrieved successfully",
            "data": {
                "id": project_id_supabase,
                "name": project_data.get("name"),
                "organization_id": project_data.get("organization_id"),
                "region": project_data.get("region"),
                "status": project_data.get("status"),
                "created_at": project_data.get("created_at"),
                "database_url": project_data.get("database", {}).get("host"),
                "api_url": f"https://{project_id_supabase}.supabase.co",
                "dashboard_url": f"https://supabase.com/dashboard/project/{project_id_supabase}",
                "anon_key": anon_key,
                "service_role_key": service_role_key,
                "is_connected": is_connected,
                "connected_at": connected_at,
                "database": project_data.get("database", {}),
                "settings": project_data.get("settings", {})
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Error getting project details: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get project details: {str(e)}")
    
@router.get("/supabase/organizations")
async def list_supabase_organizations(
    project_id: str,
    current_user = Depends(get_current_user)
):
    """
    List user's Supabase organizations
    """
    try:
        user_id=current_user.get("cognito:username")
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        result = await supabase_mgmt_service.get_organizations(user_id,project_id)
        
        if not result.get("success"):
            raise HTTPException(
                status_code=400,
                detail=f"Failed to fetch organizations: {result.get('error')}"
            )
        
        return {
            "success": True,
            "message": "Organizations retrieved successfully",
            "data": result["data"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Error fetching organizations: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch organizations: {str(e)}")


@router.post("/supabase/create-with-lambda-bootstrap")
async def create_project_with_lambda_bootstrap(
    project_id: str,
    request: CreateProjectRequest,
    current_user = Depends(get_current_user)
):
    """
    Create Supabase project and bootstrap via direct Lambda invoke
    """
    try:
        user_id = current_user.get("cognito:username")
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found")
        
        # Generate password if not provided
        if not request.db_password:
            request.db_password = ''.join(
                secrets.choice(string.ascii_letters + string.digits + "!@#$%^&*") 
                for _ in range(16)
            )
        
        print(f"[DEBUG] Creating project: {request.name}")
        
        # Step 1: Create project via Supabase Management API
        create_result = await supabase_mgmt_service.create_project(
            user_id=user_id,
            project_id=project_id,
            name=request.name,
            organization_id=request.organization_id,
            db_password=request.db_password,
            region=request.region
        )
        
        if not create_result.get("success"):
            raise HTTPException(
                status_code=400,
                detail=f"Failed to create project: {create_result.get('error')}"
            )
        
        project_data = create_result["data"]
        supabase_project_id = project_data.get("id")
        
        print(f"[SUCCESS] Project created: {supabase_project_id}")
        
        # Step 2: Bootstrap via direct Lambda invoke
        print(f"[INFO] Starting Lambda bootstrap...")
        
        bootstrap_result = await lambda_service.bootstrap_project(
            project_id_supabase=supabase_project_id,
            db_password=request.db_password
        )
        
        bootstrap_success = bootstrap_result.get("success", False)
        
        if bootstrap_success:
            print(f"[SUCCESS] Bootstrap completed")
            
            # Step 3: Store credentials
            encoded_password = urllib.parse.quote(request.db_password, safe='')
            direct_db_url = f"postgresql://postgres:{encoded_password}@db.{supabase_project_id}.supabase.co:5432/postgres?sslmode=require"
            
            mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
            collection = mongo_db.db["supabase_user_credentials"]
            
            collection.insert_one({
                "user_id": user_id,
                "project_id": project_id,
                "supabase_project_id": supabase_project_id,
                "api_url": f"https://{supabase_project_id}.supabase.co",
                "db_url": encrypt_data(direct_db_url),
                "service_role_key": encrypt_data(project_data.get("service_role_key", "")),
                "anon_key": encrypt_data(project_data.get("anon_key", "")),
                "connection_type": "direct_lambda_ipv6",
                "bootstrap_completed": True,
                "created_at": datetime.utcnow()
            })
            
            print(f"[SUCCESS] Credentials stored")
        
        return {
            "success": True,
            "message": "Project created and bootstrap completed",
            "data": {
                "project_id": supabase_project_id,
                "name": request.name,
                "api_url": f"https://{supabase_project_id}.supabase.co",
                "dashboard_url": f"https://supabase.com/dashboard/project/{supabase_project_id}",
                "bootstrap": {
                    "success": bootstrap_success,
                    "message": bootstrap_result.get("message", ""),
                    "error": bootstrap_result.get("error") if not bootstrap_success else None,
                    "method": "direct_lambda_invoke"
                }
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Project creation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create project: {str(e)}")
    
@router.post("/supabase/projects")
async def create_supabase_project_router(
    project_id: str,
    request: CreateProjectRequest,
    current_user = Depends(get_current_user)
):
    """Route to appropriate project creation method"""
    
    if should_use_lambda_bootstrap():
        # Use Lambda bootstrap for better performance
        return await create_project_with_lambda_bootstrap(project_id, request, current_user)
    # else:
    #     # Fallback to original method if needed
    #     return await create_supabase_project(project_id, request, current_user)
     

@router.post("/supabase/update_db")
async def update_db(
    request: UpdateSupabaseDBRequest,
    current_user = Depends(get_current_user)
):
    try:
        project_id = request.project_id
        db_password = request.db_password
        project_id_supabase = request.project_id_supabase
        
        user_id = current_user.get("cognito:username")

        # Log the start of database update process
        logger.info(f"[SUPABASE_UPDATE_DB] Starting database update for user: {user_id}, project: {project_id}, supabase_project: {project_id_supabase}")
        if not user_id:
            logger.error(f"[SUPABASE_UPDATE_DB] User ID not found in token for project: {project_id}")
            raise HTTPException(status_code=401, detail="User ID not found in token")

        # Get project details
        project_response = await supabase_mgmt_service.get_supabase_project_details(
            project_id, project_id_supabase, user_id
        )
        
        if not project_response.get("success"):
            logger.error(f"[SUPABASE_UPDATE_DB] Failed to get project details: {project_response.get('message', 'Failed to get project details')}")
            raise HTTPException(
                status_code=400,
                detail=project_response.get("message", "Failed to get project details")
            )

        project_details = project_response["data"]
        
        # 🚀 CHANGE: Use Lambda bootstrap instead of slow pooler
        print(f"[DEBUG] Starting Lambda bootstrap: {project_id_supabase}")
        
        bootstrap_result = await lambda_service.bootstrap_project(
            project_id_supabase=project_id_supabase,
            db_password=db_password
        )
        
        bootstrap_success = bootstrap_result.get("success", False)
        bootstrap_message = bootstrap_result.get("message", "")
        
        if bootstrap_success:
            print(f"[SUCCESS] Lambda bootstrap completed: {project_id_supabase}")
            
            # Store credentials with Lambda-optimized connection
            encoded_password = urllib.parse.quote(db_password, safe='')
            db_url = f"postgresql://postgres:{encoded_password}@db.{project_id_supabase}.supabase.co:5432/postgres?sslmode=require"
            
            update_fields = {
                "supabase_project_id": project_id_supabase,
                "api_url": project_details['api_url'],
                "db_url": encrypt_data(db_url),
                "service_role_key": encrypt_data(project_details['service_role_key']),
                "anon_key": encrypt_data(project_details['anon_key']),
                "updated_at": datetime.utcnow(),
                "connection_type": "lambda_optimized",
                "bootstrap_completed": True
            }
        else:
            print(f"[WARNING] Lambda bootstrap failed: {bootstrap_message}")
            # Still store credentials but mark bootstrap as incomplete
            encoded_password = urllib.parse.quote(db_password, safe='')
            db_url = f"postgresql://postgres:{encoded_password}@db.{project_id_supabase}.supabase.co:5432/postgres?sslmode=require"
            
            update_fields = {
                "supabase_project_id": project_id_supabase,
                "api_url": project_details['api_url'],
                "db_url": encrypt_data(db_url),
                "service_role_key": encrypt_data(project_details['service_role_key']),
                "anon_key": encrypt_data(project_details['anon_key']),
                "updated_at": datetime.utcnow(),
                "connection_type": "lambda_optimized",
                "bootstrap_completed": False
            }
        
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        collection = mongo_db.db["supabase_user_credentials"]
        
        collection.update_one(
            {"user_id": user_id, "project_id": project_id},
            {
                "$set": update_fields,
                "$setOnInsert": {
                    "user_id": user_id,
                    "project_id": project_id,
                    "created_at": datetime.utcnow()
                }
            },
            upsert=True
        )

        return {
            "success": True,
            "message": "Database updated successfully",
            "bootstrap_success": bootstrap_success,
            "bootstrap_message": bootstrap_message,
            "connection_type": "lambda_optimized",
            "method": "lambda_bootstrap"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update database: {str(e)}")
    
@router.delete("/supabase/disconnect/{project_id}")
async def disconnect_supabase(
    project_id: str,
    current_user = Depends(get_current_user)
):
    """
    Disconnect Supabase from the project 
    """
    try:
        user_id = current_user.get("cognito:username")
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        print(f"[DEBUG] Disconnecting Supabase for user_id: {user_id}, project_id: {project_id}")
        
        # Check if user has Supabase connected for this project
        existing_tokens = await supabase_db_service.get_user_supabase_tokens(user_id, project_id)
        
        if not existing_tokens:
            print(f"[DEBUG] No Supabase connection found for this project")
            return {
                "success": True,
                "message": "No Supabase connection found for this project",
                "status": "not_connected",
                "project_id": project_id
            }
        
      
        delete__result = await supabase_db_service.disconnect_user_supabase(user_id, project_id)
        
        # If disconnect_user_supabase returns success: True, return response directly
        if delete__result.get("success"):
            print(f"[SUCCESS] OAuth tokens deleted successfully")
            
            disconnected_at = datetime.utcnow().isoformat()
            
            return {
                "success": True,
                "message": "Supabase disconnected successfully",
                "status": "disconnected",
                "project_id": project_id,
                "disconnected_at": disconnected_at,
                "data": {
                    "user_id": user_id,
                    "project_id": project_id,
                    "was_connected": True,
                    "disconnected_at": disconnected_at
                }
            }
        else:
            # If disconnect_user_supabase failed
            error_msg = delete__result.get("error", "Failed to delete OAuth tokens")
            print(f"[ERROR] Error deleting OAuth tokens: {error_msg}")
            raise HTTPException(status_code=500, detail=f"Failed to delete OAuth tokens: {error_msg}")
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error in disconnect_supabase: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to disconnect Supabase: {str(e)}")
    

@router.post("/supabase/execute-sql-lambda")
async def execute_sql_via_lambda_invoke(
    project_id: str,
    sql_query: str,
    current_user = Depends(get_current_user)
):
    """
    Execute SQL via direct Lambda invoke
    """
    try:
        user_id = current_user.get("cognito:username")
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found")
        
        # Get stored credentials
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        collection = mongo_db.db["supabase_user_credentials"]
        
        credentials = collection.find_one({
            "user_id": user_id,
            "project_id": project_id
        })
        
        if not credentials:
            raise HTTPException(
                status_code=404,
                detail="No credentials found. Please bootstrap project first."
            )
        
        # Extract password from stored URL
        from urllib.parse import urlparse
        db_url = decrypt_data(credentials["db_url"])
        parsed = urlparse(db_url)
        db_password = parsed.password
        
        if not db_password:
            raise HTTPException(
                status_code=400,
                detail="Could not extract database password"
            )
        
        # Execute via direct Lambda invoke
        result = await lambda_service.execute_sql(
            project_id_supabase=credentials["supabase_project_id"],
            db_password=db_password,
            sql=sql_query
        )
        
        return {
            "success": result.get("success", False),
            "sql_query": sql_query,
            "data": result.get("data"),
            "row_count": result.get("row_count"),
            "error": result.get("error"),
            "method": "direct_lambda_invoke"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] SQL execution failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"SQL execution failed: {str(e)}")
    
    
