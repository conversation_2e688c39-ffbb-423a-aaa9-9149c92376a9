import random
import string
import uuid
import re
from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Query, UploadFile, File, Form
from typing import List,Optional
from datetime import datetime, timedelta, timezone
import os

from github import Github
from app.connection.establish_db_connection import get_mongo_db, get_node_db
from app.connection.tenant_middleware import get_tenant_id
from app.core.Settings import settings
from app.core.websocket.client import WebSocketClient
from app.knowledge.code_query import KnowledegeBuild
from app.knowledge.redis_kg import getRedisKnowledge
from app.routes.code_query import generate_random_prefix
from app.utils.auth_utils import get_current_user
from git import Repo
from urllib.parse import urlparse
import logging
import time
import asyncio
from app.tasks import clone, detec_sync, upstream, ingest
from app.core.task_framework import Task
import shutil

from app.utils.kg_build.sync_ebs_efs import do_sync
from app.utils.kg_inspect.knowledge import Knowledge, KnowledgeCodeBase
from app.utils.kg_inspect.knowledge_helper import Knowledge_Helper
from app.utils.kg_inspect.knowledge_reporter import Reporter
from app.utils.datetime_utils import generate_timestamp
router = APIRouter()

from pydantic import BaseModel
from typing import List
from app.utils.kg_build.import_codebase import get_latest_commit_hash
from app.utils.hash import decrypt_string
from app.utils.public_repo_creator import create_kavia_repository
class RepoBranchRequest(BaseModel):
    repo_name: str
    branch_name: str
    repo_type: str  # 'public' or 'private'
    repo_id: str
    associated: bool
    encrypted_scm_id: Optional[str] = None
    account_type: Optional[str] = None
    public_git_url: Optional[str] = None
    
class CodebaseImportRequest(BaseModel):
    project_id: int
    repositories: List[RepoBranchRequest]
    encrypted_scm_id: str = ""


from pydantic import BaseModel

class BuildUrlsRequest(BaseModel):
    build_ids: List[str]

class SelectedBranchInfo(BaseModel):
    project_id: int
    build_id: str
    branch_name: str

def generate_build_id():
    """Generate a short, unique build ID"""
    # Get current timestamp in milliseconds (last 4 digits)
    timestamp = str(int(time.time() * 1000))[-4:]
    # Generate 2 random characters
    random_chars = ''.join(random.choices(string.ascii_lowercase + string.digits, k=2))
    # Combine to create a unique ID
    return f"b{timestamp}{random_chars}"

def generate_repo_id():
    """Generate a short, repo ID"""
    # Get current timestamp in milliseconds (last 6 digits)
    timestamp = str(int(time.time() * 1000))[-6:]
    # Generate 2 random numbers
    random_numbers = ''.join(random.choices('0123456789', k=2))
    # Combine to create a unique ID
    return f"{timestamp}{random_numbers}"



@router.post("/clone-nd-build")
async def import_codebase(request: CodebaseImportRequest, upstream=False, ignore_build=False, scm=False, current_user=Depends(get_current_user)):
    try:
        project_id = request.project_id
        user_id = current_user.get("cognito:username")

        document = {
            'project_id': project_id,
            'created_at': generate_timestamp(),
        }

        mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='project_repositories',
            user_id = user_id
        )

        # Get existing document
        existing_doc = await mongo_handler.get_one(
            filter={'project_id': project_id},
            db=mongo_handler.db
        )
        
        tenant_id = get_tenant_id()

        if scm:
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            print("SCM TRUE")
            print(root_dir)
        else:
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            print("NORMAL ACCOUNT")
            print(root_dir)
            
        data_dir = os.path.join(root_dir, 'data', tenant_id, str(project_id))
        print(data_dir)
        repositories = []
        build_ids = []
        tasks = []

        kg_status = 1
            
        for repo_data in request.repositories:
            # Check if this is a public repository (has public_git_url)
            if repo_data.public_git_url:
                print(generate_timestamp(), f"🌐 Detected public repository: {repo_data.public_git_url}")

                # Check if it's already from kavia-common account
                if 'github.com/kavia-common/' in repo_data.public_git_url:
                    print(generate_timestamp(), f"✅ Repository already from kavia-common, using directly")
                    _git_url = repo_data.public_git_url
                    if not _git_url.endswith('.git'):
                        _git_url += '.git'
                else:
                    # Use the public repository creator utility
                    try:
                        kavia_repo_result = await create_kavia_repository(
                            public_url=repo_data.public_git_url,
                            branch_name=repo_data.branch_name,
                            project_id=str(project_id)
                        )

                        if kavia_repo_result.get("status") == "success":
                            # Use the kavia-common repository URL
                            _git_url = kavia_repo_result["kavia_repository"]["clone_url"]
                            print(generate_timestamp(), f"✅ Created kavia-common repository: {_git_url}")

                            # Update repo_data to reflect kavia-common repository
                            original_repo_name = repo_data.repo_name
                            repo_data.repo_name = kavia_repo_result["kavia_repository"]["full_name"]
                            # Use the default branch which should be kavia-{branch_name}
                            kavia_branch = kavia_repo_result["kavia_repository"]["default_branch"]
                            repo_data.branch_name = kavia_branch

                            print(generate_timestamp(), f"📝 Updated repo: {original_repo_name} -> {repo_data.repo_name}")
                            print(generate_timestamp(), f"🌿 Updated branch: {repo_data.branch_name}")
                        else:
                            print(generate_timestamp(), f"⚠️ Failed to create kavia repository, using original URL")
                            _git_url = repo_data.public_git_url
                            if not _git_url.endswith('.git'):
                                _git_url += '.git'

                    except Exception as e:
                        print(generate_timestamp(), f"❌ Error creating kavia repository: {str(e)}")
                        _git_url = repo_data.public_git_url
                        if not _git_url.endswith('.git'):
                            _git_url += '.git'
            else:
                # Handle private repositories
                # If scm_id is present, determine the SCM type
                if repo_data.encrypted_scm_id:
                    scm_id = decrypt_string(repo_data.encrypted_scm_id)
                    
                    # Get SCM configuration from the database
                    scm_mongo_handler = get_mongo_db(
                        db_name=settings.MONGO_DB_NAME,
                        collection_name='scm_configurations'
                    )
                    scm_data = await scm_mongo_handler.git_get_by_scm_id(scm_id)
                    
                    if not scm_data:
                        raise HTTPException(
                            status_code=404,
                            detail=f"SCM configuration not found for ID: {scm_id}"
                        )
                        
                    scm_type = scm_data['scm_type']
                    
                    if scm_type == "GITLAB":
                        _git_url = f"https://gitlab.com/{repo_data.repo_name}.git"
                    elif scm_type == "GITHUB":
                        _git_url = f"https://github.com/{repo_data.repo_name}.git"
                    elif scm_type == "gerrit":
                        # Get Gerrit host and auth username from credentials
                        gerrit_host = scm_data['credentials'].get('gerrit_host')
                        auth_username = scm_data['credentials'].get('auth_username') or scm_data['credentials'].get('organization')
                        
                        if not gerrit_host:
                            raise HTTPException(
                                status_code=400,
                                detail="Gerrit host not found in SCM configuration"
                            )
                            
                        # Parse repo name to handle Gerrit style paths (a/project-name)
                        repo_parts = repo_data.repo_name.split('/')
                        if len(repo_parts) >= 2:
                            # If it's in format "a/project-name", take the project name
                            project_name = repo_parts[-1]
                        else:
                            project_name = repo_data.repo_name
                            
                        # Construct Gerrit Git clone URL (not Gitiles URL)
                        # For authenticated Gerrit clones, use /a/ prefix
                        _git_url = f"https://{gerrit_host}/a/{project_name}"
                    else:
                        # Default fallback
                        _git_url = f"https://{repo_data.account_type or 'github'}.com/{repo_data.repo_name}.git"
                else:
                    # No SCM ID present, use the standard format
                    _git_url = f"https://{repo_data.account_type or 'github'}.com/{repo_data.repo_name}.git"
            
            build_id = generate_build_id()
            build_ids.append(build_id)
            build_session_id = generate_random_prefix() + '-' + build_id
            build_path = data_dir + "/" + build_id + "/" + repo_data.repo_name.split('/')[-1]
            print(build_path)
            code_gen = 0
            if ignore_build:
                code_gen = 1

            # Generate a random repo_id if it's empty
            repo_id = repo_data.repo_id
            if not repo_id:
                repo_id = str(random.randint(*********, *********))

            repository = {
                "service": "github",
                'repo_id': repo_id,
                "repository_name": repo_data.repo_name,
                "associated": repo_data.associated,
                'git_url': _git_url,
                "repositoryStatus": "initialized",
                "clone_url_ssh": f"**************:{repo_data.repo_name}.git",
                'repo_type': repo_data.repo_type,
                'data_dir': data_dir,
                'branches': [
                    {
                        'name': repo_data.branch_name,
                        'latest_commit_hash': None,
                        'builds': {
                            'code_gen': code_gen,
                            'build_id': build_id,
                            'build_session_id':  build_session_id,
                            'path': build_path,
                            'kg_creation_status': kg_status,
                            'build_info': {
                                'start_time': generate_timestamp(),
                                'end_time': None,
                                'last_updated': None,
                                'duration_seconds': None
                            },
                            'last_updated': generate_timestamp(),
                            'user_id': user_id,
                            'error': None
                        }
                    }
                ],
                'selected_branch': repo_data.branch_name
            }

            if repo_data.encrypted_scm_id:
                repository["scm_id"] = decrypt_string(repo_data.encrypted_scm_id)
            repositories.append(repository)
            
            if ignore_build == False:
                
                print("Moved to celery tasks. - ", data_dir)
                # Schedule individual task for each repository
                task = Task.schedule_task(
                    clone,
                    project_id=project_id,
                    build_session_id=build_session_id,
                    build_id=build_id,
                    data_dir=data_dir,
                    repository=repository,  # Pass single repository instead of list
                    tenant_id=get_tenant_id(),
                    current_user=user_id, 
                    upstream=upstream,
                    current_user_obj = current_user
                )
                tasks.append(task.to_dict())

        # Update MongoDB with all repositories
        if existing_doc:
            existing_repos = existing_doc.get('repositories', [])
            for new_repo in repositories:
                repo_exists = False
                for existing_repo in existing_repos:
                    if existing_repo['service'] != "localFiles":
                        if existing_repo['git_url'].lower() == new_repo['git_url'].lower():
                            repo_exists = True
                            new_branch = new_repo['branches'][0]
                            branch_exists = False
                            for existing_branch in existing_repo['branches']:
                                if existing_branch['name'] == new_branch['name']:
                                    branch_exists = True
                                    break
                            if not branch_exists:
                                existing_repo['selected_branch'] = new_branch['name']
                                existing_repo['branches'].append(new_branch)
                            break
                if not repo_exists:
                    existing_repos.append(new_repo)
            repositories = existing_repos

        document['repositories'] = repositories

        await mongo_handler.update_one(
            filter={'project_id': project_id},
            element=document,
            upsert=True,
            db=mongo_handler.db
        )
        
        return {
            "status": "success",
            "message": f"Scheduled {len(tasks)} repository tasks successfully",
            "tasks": tasks,
            "data": document,
            "buildIds": build_ids
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to import codebase: {str(e)}"
        )
    
@router.post("/ingest-local-files")
async def ingest_local_files(
    project_id: int = Form(...),
    project_name: str = Form(...),
    files: List[UploadFile] = File(...),
    current_user=Depends(get_current_user)
):
    user_id = current_user.get("cognito:username")

    document = {
        'project_id': project_id,
        'created_at': generate_timestamp(),
    }
        
    mongo_handler = get_mongo_db(
        db_name=settings.MONGO_DB_NAME,
        collection_name='project_repositories'
    )
    
    root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    data_dir = os.path.join(root_dir, 'data', get_tenant_id(), str(project_id))
    
    tasks = []

    existing_doc = await mongo_handler.get_one(
            filter={'project_id': project_id},
            db=mongo_handler.db
        )

    build_id = generate_build_id()
    repo_id = generate_repo_id()
    build_session_id = generate_random_prefix() + '-' + build_id
    files_data = {
                    "service": "localFiles",
                    'repo_id': repo_id,
                    "repository_name": project_name,
                    'builds': {
                        'build_id': build_id,
                        'build_session_id':  build_session_id,
                        'path': data_dir + "/" + f"{build_id}/{project_name}",
                        'kg_creation_status': 1,
                        'build_info': {
                            'start_time': generate_timestamp(),
                            'end_time': None,
                            'last_updated': None,
                            'duration_seconds': None
                        },
                        'last_updated': generate_timestamp(),
                        'user_id': user_id,
                        'error': None
                    }
                }
    
    checking_path = os.path.join(data_dir, build_id, project_name)
    os.makedirs(checking_path, exist_ok=True)

    files_data['builds']['path'] = checking_path

    for file in files:
        file_path = os.path.join(checking_path, file.filename)

        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
    
    if existing_doc:
        existing_repo = existing_doc.get('repositories', [])
        existing_repo.append(files_data)
        document['repositories'] = existing_repo
    else: 
        document['repositories'] = [files_data]
    
    await mongo_handler.update_one(
        filter={'project_id': project_id},
        element=document,
        upsert=True,
        db=mongo_handler.db
    )
    
    task = Task.schedule_task(
        ingest,
        project_id=project_id,
        project_name=project_name,
        build_session_id=build_session_id,
        build_id=build_id,
        data_dir=data_dir,
        files_data=files_data,
        tenant_id=get_tenant_id(),
        current_user=user_id, 
    )
    tasks.append(task.to_dict())

    return {
        "status": "success",
        "message": f"Scheduled ingest task successfully",
        "tasks": tasks,
        "data": files_data,
        "buildIds": [build_id],
        "build_session_id": build_session_id
    }

@router.post("/do-sync-the-repo")
async def do_sync_the_repo(project_id: int, build_id: str, current_repo_status: int, current_user=Depends(get_current_user)):
    user_id = current_user.get("cognito:username")
    tasks = []    
    build_session_id = generate_random_prefix() + '-' + build_id
    
    kg = KnowledegeBuild()
    if(current_repo_status == 2):
        changed_status = 4
    else: 
        changed_status = 1
        
    await kg.update_kg_status(changed_status, project_id, [build_id], build_session_id)
    await kg.update_build_times(project_id, [build_id], "start_time", True)
    
    # Schedule individual task for each repository
    task = Task.schedule_task(
        upstream,
        project_id=project_id,
        build_session_id=build_session_id,
        build_id=build_id,
        tenant_id=get_tenant_id(),
        current_user=user_id, 
    )
    tasks.append(task.to_dict())
    
    return {
        'Upstream': tasks,
        'build_session_id': build_session_id,
        'build_id': build_id
    }
              
def should_rebuild_based_on_time(last_updated_str: str) -> tuple[bool, str]:
    """
    Check if rebuild is needed based on last update time
    Returns tuple of (should_rebuild: bool, message: str)
    """
    try:
        # Parse the last_updated timestamp
        last_updated = datetime.fromisoformat(last_updated_str.replace('Z', '+00:00'))
        
        # Get current time in UTC
        current_time = datetime.now(timezone.utc)
        
        # Calculate time difference
        time_difference = current_time - last_updated
        
        # If more than 10 minutes have passed
        if time_difference > timedelta(minutes=10):
            minutes_passed = int(time_difference.total_seconds() / 60)
            return True, f"Build is stale. Last update was {minutes_passed} minutes ago"
            
        return False, ""
        
    except Exception as e:
        return True, f"Invalid timestamp format: {str(e)}"
    
@router.post("/set-selected-branch")
async def switch_selected_branch(payload: SelectedBranchInfo):
    branch_exists = False
    mongo_handler = get_mongo_db(db_name=settings.MONGO_DB_NAME, collection_name="project_repositories")

    repo_data = await mongo_handler.get_one(
        filter={
            'project_id': payload.project_id,
        },
        db=mongo_handler.db
    )

    existing_data = next(
        (repo for repo in repo_data.get('repositories', []) 
        if any(branch.get('name') == payload.branch_name for branch in repo.get('branches', []))),
        None
    )

    if existing_data:
        branch_exists = True
    
    if branch_exists:
        await mongo_handler.update_with_nested_object_and_filters(
            filter={
                'project_id': payload.project_id,
            },
            update={
                'repositories.$[repo].selected_branch': payload.branch_name
            },
            array_filters=[{'repo.branches.builds.build_id': payload.build_id}],
            db=mongo_handler.db
        )
        return({'detail': f'Successfully set branch to {payload.branch_name}'})
    else:
        raise HTTPException(status_code=404, detail="Cannot select. Branch not found.")
    
@router.get("/kg-info/{project_id}")
async def get_kg_status(
    project_id: int,
    associate: bool = False,
    current_user=Depends(get_current_user)
):
    user_id = current_user.get("cognito:username")
    
    Task.schedule_task(
        detec_sync,
        project_id=project_id,
        tenant_id=get_tenant_id(),
        current_user=user_id
    )
    
    mongo_handler = get_mongo_db(
        db_name=settings.MONGO_DB_NAME,
        collection_name='project_repositories'
    )

    _result = await mongo_handler.get_one(
        filter={
            'project_id': project_id,
        },
        db=mongo_handler.db
    )
        
    if not _result:
        raise HTTPException(status_code=200, detail="No repository is found")
    
    repositories = _result.get('repositories', [])
    if associate:
        repositories = [repo for repo in repositories if repo.get('associated') is True]

    # should_delete = False
    # deletion_reason = ""

    # # Enhanced cleanup logic with priority-based decision tree
    # for repo in repositories:
    #     for branch in repo.get('branches', []):
    #         if branch['name'] == 'kavia-main':
    #             builds = branch.get('builds', {})
    #             save_and_merge = builds.get('save_and_merge', 0)
    #             code_gen = builds.get('code_gen', 0)

    #             # First Priority: Check if save_and_merge exists and equals 1
    #             if save_and_merge == 1:
    #                 print(generate_timestamp(), f"Repository {repo.get('repository_name', 'unknown')} protected by save_and_merge flag - will not be deleted")
    #                 continue  # Skip deletion for this repository - it's protected

    #             # Second Priority: Only if save_and_merge is missing or not equal to 1
    #             if code_gen == 1:
    #                 should_delete = True
    #                 deletion_reason = f"Repository {repo.get('repository_name', 'unknown')} has code_gen=1 and no successful save_and_merge operation"
    #                 print(generate_timestamp(), deletion_reason)
    #                 break
    #     if should_delete:
    #         break

    # if should_delete:
    #     # Add a flag to indicate this repository should be deleted
    #     _result['should_delete'] = True

    #     mongo_handler.db[mongo_handler.collection].delete_one(
    #             {"project_id": project_id}
    #         )

    #     # Return early with a message
    #     return {
    #         "details": [],
    #         "created_at": _result.get('created_at'),
    #         "message": f"Repository deleted: {deletion_reason}"
    #     }

    for repo_index, repo in enumerate(repositories):
        git_url = repo.get('git_url')
        
        for branch_index, branch in enumerate(repo.get('branches', [])):
            
            if repositories[repo_index]['branches'][branch_index]['builds']['kg_creation_status'] == -1:
                continue
                
            try: 
                # Redis knowledge check
                start_time_str = repositories[repo_index]['branches'][branch_index]['builds']['build_info']['start_time']
                last_update_at = repositories[repo_index]['branches'][branch_index]['builds']['build_info'].get('last_updated')
                if start_time_str is None:
                    repositories[repo_index]['branches'][branch_index]['rebuild'] = 1
                    repositories[repo_index]['branches'][branch_index]['rebuild_msg'] = "Start time not found or time limit for build exceeded."
                    repositories[repo_index]['branches'][branch_index]['builds']['kg_creation_status'] = 3
                
                # check if the progress get stuck      
                if repositories[repo_index]['branches'][branch_index]['builds']['kg_creation_status'] not in [2,3]:
                    
                    if last_update_at:
                        # Check if rebuild needed based on last update time
                        should_rebuild, rebuild_message = should_rebuild_based_on_time(last_update_at)
                        if should_rebuild:
                            repositories[repo_index]['branches'][branch_index]['rebuild'] = 1
                            repositories[repo_index]['branches'][branch_index]['rebuild_msg'] = rebuild_message
                            repositories[repo_index]['branches'][branch_index]['builds']['kg_creation_status'] = 3
                    elif not last_update_at:
                        
                        start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
                        current_time = datetime.now(timezone.utc)
                        time_difference = current_time - start_time

                        if time_difference > timedelta(minutes=10):
                            minutes_passed = int(time_difference.total_seconds() / 60)
                            repositories[repo_index]['branches'][branch_index]['rebuild'] = 1
                            repositories[repo_index]['branches'][branch_index]['rebuild_msg'] = f"Build started {minutes_passed} minutes ago and exceeded time limit."
                            repositories[repo_index]['branches'][branch_index]['builds']['kg_creation_status'] = 3
                    
            except Exception as e:
                logging.error(f"Ruild needed")
                continue
    
    
    return {
        "details": repositories,  # This will now contain all updated PR and commit information
        "created_at": _result.get('created_at')
    }

@router.post("/test-sync")
def test_sync():
    start = time.time()
    # Simulate blocking operations like your knowledge processing
    time.sleep(2)
    return {"duration": time.time() - start, "type": "sync"}

@router.post("/test-async") 
async def test_async():
    start = time.time()
    # Non-blocking async operation
    await asyncio.sleep(2)
    return {"duration": time.time() - start, "type": "async"}

@router.post("/test-cpu-intensive")
def test_cpu_intensive():
    start = time.time()
    # Simulate CPU-bound work like your LLM processing
    total = 0
    for i in range(10000000):  # CPU intensive loop
        total += i * i
    return {"duration": time.time() - start, "type": "cpu-intensive", "result": total}

@router.post("/test-file-io")
async def test_file_io():
    start = time.time()
    # Simulate your file reading operations
    import tempfile
    import os
    
    # Create a temporary file
    with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
        f.write("x" * 1000000)  # 1MB file
        temp_path = f.name
    
    try:
        # Read file multiple times (like your chunking)
        content = ""
        for _ in range(10):
            with open(temp_path, 'r') as f:
                content += f.read()
    finally:
        os.unlink(temp_path)
    
    return {"duration": time.time() - start, "type": "file-io", "size": len(content)}

@router.post("/test-concurrent-load")
async def test_concurrent_load():
    start = time.time()
    
    # Simulate multiple concurrent operations like your worker threads
    async def worker_task(worker_id):
        await asyncio.sleep(1)  # Simulate I/O
        return f"worker-{worker_id}-done"
    
    # Create 15 concurrent tasks (like your kw1-kw15 workers)
    tasks = [asyncio.create_task(worker_task(i)) for i in range(15)]
    results = await asyncio.gather(*tasks)
    
    return {
        "duration": time.time() - start, 
        "type": "concurrent", 
        "workers": len(results)
    }

@router.post("/discard-and-exit/{project_id}")
async def discard_and_exit(
    project_id: int,
    current_user=Depends(get_current_user)
):
    """
    Endpoint to discard and delete a project based on repository build status.
    Implements priority-based decision tree for deletion logic.
    """
    try:
        user_id = current_user.get("cognito:username")

        mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='project_repositories',
            user_id=user_id
        )

        # Get the project document
        _result = await mongo_handler.get_one(
            filter={'project_id': project_id},
            db=mongo_handler.db
        )

        if not _result:
            raise HTTPException(status_code=404, detail="Project not found")

        repositories = _result.get('repositories', [])
        should_delete = False
        deletion_reason = ""

        # Enhanced cleanup logic with priority-based decision tree
        for repo in repositories:
            for branch in repo.get('branches', []):
                if branch['name'] == 'kavia-main':
                    builds = branch.get('builds', {})
                    save_and_merge = builds.get('save_and_merge', 0)
                    code_gen = builds.get('code_gen', 0)

                    # First Priority: Check if save_and_merge exists and equals 1
                    if save_and_merge == 1:
                        print(generate_timestamp(), f"Repository {repo.get('repository_name', 'unknown')} protected by save_and_merge flag - will not be deleted")
                        continue  # Skip deletion for this repository - it's protected

                    # Second Priority: Only if save_and_merge is missing or not equal to 1
                    if code_gen == 1:
                        should_delete = True
                        deletion_reason = f"Repository {repo.get('repository_name', 'unknown')} has code_gen=1 and no successful save_and_merge operation"
                        print(generate_timestamp(), deletion_reason)
                        break
            if should_delete:
                break

        if should_delete:
            # Delete the document from MongoDB
            delete_result = await mongo_handler.delete_one(
                filter={"project_id": project_id},
                db=mongo_handler.db
            )

            if delete_result.deleted_count > 0:
                return {
                    "status": "success",
                    "message": f"Repository deleted: {deletion_reason}",
                    "project_id": project_id,
                    "deleted": True,
                    "created_at": _result.get('created_at')
                }
            else:
                raise HTTPException(status_code=500, detail="Failed to delete project from database")
        else:
            return {
                "status": "protected",
                "message": "Project is protected from deletion due to save_and_merge flag or no code_gen flag found",
                "project_id": project_id,
                "deleted": False,
                "created_at": _result.get('created_at')
            }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process discard and exit: {str(e)}"
        )

@router.get("/get_mermaid_code/{mermaid_type}")
async def create_tmp_files():
    try:
        db = get_node_db("T0005")

        data = await db.get_nodes_by_label('Container')
        container_diagrams = []

        for container in data:
            if 'properties' in container and 'ContainerDiagram' in container['properties']:
                diagram_info = {
                    'title': container['properties'].get('Title', 'Untitled'),
                    'diagram': container['properties']['ContainerDiagram']
                }
                container_diagrams.append(diagram_info)

        return container_diagrams

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating temporary files: {str(e)}")
