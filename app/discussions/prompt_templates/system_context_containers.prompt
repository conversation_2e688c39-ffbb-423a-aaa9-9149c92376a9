{% extends "base_discussion.prompt" %}

You are an expert software architect.

{% block task_description_common_preface %}

{% if config_state != "configured" %}
As an expert software architect, design the internal container architecture based on the C4 model:
Note : Each container (node) should be treated as a separate repository, independently managed and deployed.

{% else %}
You are an expert system architect reviewing the Container architecture for potential reconfiguration.

1. Original Container Architecture:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Containers: {{ bg_info.get('containers', {}) | tojson(indent=2) }}
   - Interfaces: {{ bg_info.get('interfaces', {}) | tojson(indent=2) }}

2. Current Architecture (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated containers: {{ new_bg.get('containers', {}) | tojson(indent=2) }}
   - Updated interfaces: {{ new_bg.get('interfaces', {}) | toj<PERSON>(indent=2) }}

3. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}

{% endif %}

GUIDELINES:  

1. Review Requirements Context:
   - Project Context: {{ details_for_discussion.get('project_details') | tojson(indent=2) }}
   - Requirements:
     - Functional Requirements:
       {% for req in details_for_discussion.get('requirements_context', {}).get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in details_for_discussion.get('requirements_context', {}).get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

2. Container Architecture:
   - Create containers as child nodes based on:
     * System responsibilities
     * Architecture strategy
     * Functional/Non-Functional requirements
   - For new Containers:
     * Use IDs like 'NEW-ARCH-1', 'NEW-ARCH-2', etc.
   - Container Properties:
     * Title: Clear, descriptive name
     * Description: Purpose and responsibilities
     * Type: Container
     * ContainerType: "internal" 

3. Interface Definition and Creation:
   - AUTOMATICALLY CREATE interface nodes as child nodes when containers need to communicate
   - For each interface node:
     * Create as a child node with unique ID (e.g., 'NEW-INTERFACE-1', 'NEW-INTERFACE-2')
     * Set Type: "Interface"
     * Properties to include:
       - Title: Clear, descriptive name
       - Description: Detailed description of the interface purpose
       - InterfaceType: Determine appropriate type based on:
         - Communication patterns (synchronous/asynchronous)
         - Performance requirements
         - Reliability needs
         - Integration constraints
       - Protocol: Specific protocol used
       - DataFormat: Data exchange format
       - ApiSignature: Examples of API calls or message formats

   Example interface creation scenarios:
   - Container-to-container communication requires REST API
   - Asynchronous processing needs Message Queue interface
   - Database access requires Database Interface
   - File operations need File Interface

4. Define interactions between containers through these interfaces

5. Generate C4 Container Diagram:
   - Use Mermaid syntax
   - Show all containers
   - Include interfaces
   - Show relationships and dependencies

6. Generate Generic Cloud Infrastructure Diagram:

   - Use **D2 syntax** instead of Mermaid.
   - Illustrate how the system and its containers are deployed within a virtual private cloud (VPC) or equivalent.
   - Show key infrastructure components such as load balancers, compute clusters (VMs or container orchestrators), managed databases, object/block storage, and monitoring/logging services.
   - Indicate the flow of traffic from external users through the load balancer to the application containers, and from containers to databases and storage.
   - Use generic names (e.g., "Compute Cluster", "Managed Database") to ensure cloud-agnostic representation.
   - Follow D2 validation rules.

   title: "Generic Cloud Infrastructure"

   # =========================
   # EDGE & GLOBAL SERVICES
   # =========================
   Global: {
   shape: rectangle
   style: {
      fill: "#f9f9f9"
      stroke-dash: 4
   }

   DNS: "🧭 Global DNS / Anycast GSLB" {
      shape: rectangle
      style.fill: "#e6f7ff"
   }
   CDN: "🚀 CDN / Edge Caching" {
      shape: rectangle
      style.fill: "#e6f7ff"
   }
   DDoS: "🛡️ DDoS Protection" {
      shape: rectangle
      style.fill: "#f4cccc"
   }
   WAF_Global: "🛡️ Global WAF" {
      shape: rectangle
      style.fill: "#f4cccc"
   }
   IAM_Global: "🔐 Global IAM / SSO / IdP" {
      shape: rectangle
      style.fill: "#ead1dc"
   }
   Artifact: "📦 Artifact / Container Registry" {
      shape: rectangle
      style.fill: "#d9ead3"
   }
   DevPortal: "📚 API Developer Portal" {
      shape: rectangle
      style.fill: "#e6f7ff"
   }
   ObservabilityHub: "📡 Central Observability (Metrics/Logs/Traces)" {
      shape: rectangle
      style.fill: "#c9daf8"
   }
   SIEM_Global: "🛰️ Global SIEM / Threat Intel" {
      shape: rectangle
      style.fill: "#cfe2f3"
   }
   KeyMgmt_Global: "🔐 KMS / HSM (Global Keys)" {
      shape: rectangle
      style.fill: "#ead1dc"
   }
   }

   # External Users & Partners
   Internet: "🌐 External Users / Partners" {
   shape: cloud
   style.fill: "#e6f7ff"
   }

   # High-level edge routing
   Internet -> Global.DNS: "DNS Query"
   Global.DNS -> Global.CDN: "Edge Routing"
   Global.CDN -> Global.DDoS
   Global.DDoS -> Global.WAF_Global

   # =========================
   # REGION A (PRIMARY)
   # =========================
   RegionA: {
   label: "Region A (Primary)"
   shape: rectangle
   style: {
      fill: "#ffffff"
      stroke-dash: 2
   }

   # --- Networking / VPC ---
   VPC_A: {
      label: "VPC - Region A"
      shape: rectangle
      style: { fill: "#f9f9f9"; stroke-dash: 4 }

      IGW_A: "🌉 Internet Gateway" { shape: rectangle; style.fill: "#e6f7ff" }
      NAT_A: "🔁 NAT Gateway" { shape: rectangle; style.fill: "#e6f7ff" }
      FW_A: "🧱 Network Firewall" { shape: rectangle; style.fill: "#f4cccc" }
      SubnetPub_A: "🌐 Public Subnets" { shape: rectangle; style.fill: "#e6f7ff" }
      SubnetPriv_A: "🔒 Private App Subnets" { shape: rectangle; style.fill: "#fff2cc" }
      SubnetData_A: "🗄️ Data Subnets" { shape: rectangle; style.fill: "#d9d2e9" }
      Bastion_A: "🛠️ Bastion Host / SSM" { shape: rectangle; style.fill: "#e6f7ff" }
      Peering_A: "🔗 VPC Peering / PrivateLink" { shape: rectangle; style.fill: "#d0e0e3" }

      # --- Edge In-Region ---
      WAF_A: "🛡️ Regional WAF" { shape: rectangle; style.fill: "#f4cccc" }
      LB_A: "⚖️ App Load Balancer" { shape: rectangle; style.fill: "#cce5ff" }
      APIGW_A: "🌐 API Gateway" { shape: rectangle; style.fill: "#cce5ff" }

      # --- Service Mesh & Ingress ---
      Mesh_A: "🕸️ Service Mesh (mTLS, Policy)" { shape: rectangle; style.fill: "#cfe2f3" }
      Ingress_A: "🚪 Mesh Ingress" { shape: rectangle; style.fill: "#cfe2f3" }

      # --- Compute / Microservices ---
      ASG_A: "🖥️ Compute Cluster (ASG/K8s)" { shape: hexagon; style.fill: "#d9ead3" }
      Web_A: "🖥️ Web Frontend / BFF" { shape: rectangle; style.fill: "#fff2cc" }
      API_A: "🧩 API Services" { shape: rectangle; style.fill: "#fff2cc" }
      Worker_A: "⚙️ Background Workers" { shape: rectangle; style.fill: "#fff2cc" }
      FeatureFlags_A: "🚦 Feature Flags / A/B" { shape: rectangle; style.fill: "#fff2cc" }

      # --- Messaging / Streaming ---
      MQ_A: "📩 Message Queue (SQS/Rabbit)" { shape: parallelogram; style.fill: "#fff2cc" }
      Kafka_A: "🌀 Stream Bus (Kafka)" { shape: parallelogram; style.fill: "#d0e0e3" }
      StreamProc_A: "🔄 Stream Processing (Flink/Spark)" { shape: parallelogram; style.fill: "#d0e0e3" }

      # --- Data Services ---
      Cache_A: "⚡ In-Memory Cache (Redis)" { shape: cylinder; style.fill: "#f9cb9c" }
      DB_Primary_A: "🗄️ Primary DB Cluster" { shape: cylinder; style.fill: "#d9d2e9" }
      DB_Read_A: "📀 Read Replicas" { shape: cylinder; style.fill: "#d9d2e9" }
      Search_A: "🔎 Search (OpenSearch/ES)" { shape: cylinder; style.fill: "#c9daf8" }
      Storage_A: "💾 Object/Block Storage" { shape: cylinder; style.fill: "#fce5cd" }
      Backup_A: "📂 Backups & Snapshots" { shape: cylinder; style.fill: "#fce5cd" }

      # --- Analytics / DWH / Lake ---
      Lake_A: "🌊 Data Lake (Raw/Bronze/Silver/Gold)" { shape: cylinder; style.fill: "#d0e0e3" }
      Catalog_A: "🏷️ Data Catalog / Governance" { shape: rectangle; style.fill: "#e6f7ff" }
      DWH_A: "📊 Data Warehouse" { shape: cylinder; style.fill: "#c9daf8" }
      ETL_A: "🧪 ETL / Batch (Spark/DBT)" { shape: parallelogram; style.fill: "#d0e0e3" }
      BI_A: "📈 BI / Reporting" { shape: rectangle; style.fill: "#e6f7ff" }

      # --- ML / AI ---
      FeatureStore_A: "🧰 Feature Store" { shape: rectangle; style.fill: "#d9ead3" }
      ModelReg_A: "📗 Model Registry" { shape: rectangle; style.fill: "#d9ead3" }
      Train_A: "🏋️ Model Training Jobs" { shape: rectangle; style.fill: "#d9ead3" }
      Inference_A: "🤖 Inference Service" { shape: rectangle; style.fill: "#d9ead3" }

      # --- Security & Compliance ---
      IAM_A: "🔐 IAM / RBAC" { shape: rectangle; style.fill: "#ead1dc" }
      Secrets_A: "🔑 Secrets Manager" { shape: rectangle; style.fill: "#ead1dc" }
      KMS_A: "🔐 KMS / HSM (Region)" { shape: rectangle; style.fill: "#ead1dc" }
      Audit_A: "🧾 Audit Logs" { shape: rectangle; style.fill: "#fce5cd" }
      Vault_A: "🗄️ Vault / Key Escrow" { shape: rectangle; style.fill: "#ead1dc" }
      Compliance_A: "🏛️ Compliance / Policy" { shape: rectangle; style.fill: "#f4cccc" }

      # --- Observability ---
      Metrics_A: "📈 Metrics (Prometheus)" { shape: rectangle; style.fill: "#c9daf8" }
      Logs_A: "🪵 Logs (ELK/Cloud Logs)" { shape: rectangle; style.fill: "#c9daf8" }
      Traces_A: "🧵 Traces (Jaeger/OTel)" { shape: rectangle; style.fill: "#c9daf8" }
      APM_A: "🩺 APM / Synthetics" { shape: rectangle; style.fill: "#c9daf8" }

      # --- DevEx / Delivery / Governance ---
      CI_A: "🔧 CI/CD Pipeline" { shape: rectangle; style.fill: "#d9ead3" }
      IaC_A: "🏗️ IaC (Terraform/Pulumi)" { shape: rectangle; style.fill: "#d9ead3" }
      Policy_A: "📜 Policy as Code (OPA/Kyverno)" { shape: rectangle; style.fill: "#f4cccc" }
      Config_A: "⚙️ Config Service" { shape: rectangle; style.fill: "#fff2cc" }
      FinOps_A: "💸 FinOps / Cost Mgmt" { shape: rectangle; style.fill: "#e6f7ff" }

      # --- Internal Flow (Region A) ---
      # Edge
      Global.WAF_Global -> WAF_A
      WAF_A -> LB_A
      LB_A -> APIGW_A
      APIGW_A -> Mesh_A
      Mesh_A -> Ingress_A
      Ingress_A -> ASG_A

      # App ingress to services
      ASG_A -> Web_A
      ASG_A -> API_A
      ASG_A -> Worker_A
      API_A -> FeatureFlags_A

      # Service <-> Data
      API_A -> Cache_A
      API_A -> DB_Primary_A
      API_A -> Search_A
      Worker_A -> MQ_A
      Worker_A -> Kafka_A
      Kafka_A -> StreamProc_A
      MQ_A -> Worker_A
      DB_Primary_A -> DB_Read_A
      DB_Primary_A -> Backup_A
      Storage_A -> Backup_A

      # Analytics
      StreamProc_A -> Lake_A
      ETL_A -> DWH_A
      Lake_A -> ETL_A
      DWH_A -> BI_A

      # ML
      Lake_A -> FeatureStore_A
      FeatureStore_A -> Train_A
      Train_A -> ModelReg_A
      ModelReg_A -> Inference_A
      Inference_A -> API_A

      # Security
      APIGW_A -> IAM_A
      API_A -> Secrets_A
      Worker_A -> Secrets_A
      DB_Primary_A -> KMS_A
      Storage_A -> KMS_A
      Audit_A -> Logs_A
      Compliance_A -> Policy_A

      # Observability
      API_A -> Metrics_A
      API_A -> Logs_A
      API_A -> Traces_A
      Worker_A -> Metrics_A
      DB_Primary_A -> Metrics_A
      LB_A -> Metrics_A
      APIGW_A -> Metrics_A
      Metrics_A -> Global.ObservabilityHub
      Logs_A -> Global.ObservabilityHub
      Traces_A -> Global.ObservabilityHub

      # DevEx / Delivery
      Global.Artifact -> CI_A
      CI_A -> ASG_A
      CI_A -> API_A
      CI_A -> Web_A
      IaC_A -> VPC_A
      Policy_A -> CI_A
      Config_A -> API_A
      Config_A -> Worker_A
      FinOps_A -> Global.ObservabilityHub

      # Networking primitives
      SubnetPub_A -> LB_A
      SubnetPriv_A -> ASG_A
      SubnetData_A -> DB_Primary_A
      IGW_A -> SubnetPub_A
      NAT_A -> SubnetPriv_A
      FW_A -> SubnetPriv_A
      Bastion_A -> SubnetPriv_A
      Peering_A -> SubnetPriv_A
   }
   }

   # =========================
   # REGION B (DR / ACTIVE-ACTIVE)
   # =========================
   RegionB: {
   label: "Region B (DR / Secondary)"
   shape: rectangle
   style: { fill: "#ffffff"; stroke-dash: 2 }

   VPC_B: {
      label: "VPC - Region B"
      shape: rectangle
      style: { fill: "#f9f9f9"; stroke-dash: 4 }

      LB_B: "⚖️ App Load Balancer" { shape: rectangle; style.fill: "#cce5ff" }
      APIGW_B: "🌐 API Gateway" { shape: rectangle; style.fill: "#cce5ff" }
      Mesh_B: "🕸️ Service Mesh" { shape: rectangle; style.fill: "#cfe2f3" }
      ASG_B: "🖥️ Compute Cluster" { shape: hexagon; style.fill: "#d9ead3" }
      API_B: "🧩 API Services (DR)" { shape: rectangle; style.fill: "#fff2cc" }
      DB_Standby_B: "🗄️ Standby DB Cluster" { shape: cylinder; style.fill: "#d9d2e9" }
      Cache_B: "⚡ Cache (DR)" { shape: cylinder; style.fill: "#f9cb9c" }
      Storage_B: "💾 Object/Block Storage (DR)" { shape: cylinder; style.fill: "#fce5cd" }
      Metrics_B: "📈 Metrics (DR)" { shape: rectangle; style.fill: "#c9daf8" }
      Logs_B: "🪵 Logs (DR)" { shape: rectangle; style.fill: "#c9daf8" }

      # DR Internal
      LB_B -> APIGW_B
      APIGW_B -> Mesh_B
      Mesh_B -> ASG_B
      ASG_B -> API_B
      API_B -> Cache_B
      API_B -> DB_Standby_B
      API_B -> Storage_B
      API_B -> Metrics_B
      API_B -> Logs_B
   }
   }

   # =========================
   # CROSS-REGION LINKS
   # =========================
   Global.WAF_Global -> RegionA.VPC_A.LB_A: "Primary Route"
   Global.WAF_Global -> RegionB.VPC_B.LB_B: "Failover / Active-Active"

   RegionA.VPC_A.DB_Primary_A -> RegionB.VPC_B.DB_Standby_B: "🔁 Cross-Region Replication" {
   style.stroke-dash: 4
   }
   RegionA.VPC_A.Storage_A -> RegionB.VPC_B.Storage_B: "📂 Cross-Region Backup"
   RegionA.VPC_A.Metrics_A -> Global.ObservabilityHub
   RegionB.VPC_B.Metrics_B -> Global.ObservabilityHub
   RegionA.VPC_A.Logs_A -> Global.SIEM_Global
   RegionB.VPC_B.Logs_B -> Global.SIEM_Global
   Global.KeyMgmt_Global -> RegionA.VPC_A.KMS_A
   Global.KeyMgmt_Global -> RegionB.VPC_B.API_B

   # =========================
   # EXTERNAL INTEGRATIONS
   # =========================
   Partners: {
   shape: rectangle
   style: { fill: "#f9f9f9"; stroke-dash: 4 }

   Payments: "💳 Payment Gateway" { shape: rectangle; style.fill: "#ffe6cc" }
   BankAPIs: "🏦 Banking / Open APIs" { shape: cloud; style.fill: "#e6f7ff" }
   EmailSMS: "📬 Email / SMS Provider" { shape: rectangle; style.fill: "#e6f7ff" }
   CRM: "💼 CRM / ERP" { shape: rectangle; style.fill: "#e6f7ff" }
   KYC: "🪪 KYC / Identity Verification" { shape: rectangle; style.fill: "#fce5cd" }
   AnalyticsExt: "📊 External Analytics" { shape: cloud; style.fill: "#e6f7ff" }
   RegulatorAPI: "🏛️ Regulator / Compliance API" { shape: cloud; style.fill: "#f2dcdb" }
   }

   # Integration Flows
   Partners.Payments -> RegionA.VPC_A.APIGW_A: "Transactions"
   Partners.BankAPIs -> RegionA.VPC_A.API_A: "Bank Feeds"
   Partners.EmailSMS -> RegionA.VPC_A.Worker_A: "Notifications"
   Partners.CRM -> RegionA.VPC_A.API_A: "Integrations"
   Partners.KYC -> RegionA.VPC_A.API_A: "Onboarding"
   Partners.AnalyticsExt -> RegionA.VPC_A.BI_A: "Insights"
   Partners.RegulatorAPI -> RegionA.VPC_A.Compliance_A: "Reporting"

   # =========================
   # USER ENTRY
   # =========================
   Internet -> Global.DNS: "DNS"
   Global.DNS -> Global.CDN: "Edge"
   Global.CDN -> Global.WAF_Global: "Protected Traffic"

   Note: When generating the diagram:
   - Replace generic names with actual infrastructure component names if available.
   - Update descriptions to match the actual deployment context.
   - Show all relevant infrastructure components and their relationships.
   - Ensure the diagram remains cloud-agnostic unless a specific provider is required.

7. Generate Integration Architecture Diagram:
 
   - Use **D2** syntax.
   - Illustrate how the system integrates with external systems (e.g., CRM, ERP, Payment Gateway, Third-Party APIs).
   - Show interfaces and integration patterns (e.g., REST, SOAP, message queues, events).
   - Highlight data flow and communication between external systems, the integration/API layer, core services, and internal data stores.
   - Include monitoring/logging where relevant.
   - Use generic, cloud-agnostic names unless specific technologies are provided.
   - Follow D2 validation rules.
 
   Generic Integration Architecture Diagram Reference (D2 syntax):
   ```d2
         title: "Generic Integration Architecture - Data Flow & Interfaces"

         CRM: "💼 CRM System" {
         shape: cylinder
         style.fill: "#cce5ff"
         }
         ERP: "🏢 ERP System" {
         shape: cylinder
         style.fill: "#cce5ff"
         }
         Payment: "💳 Payment Gateway" {
         shape: rectangle
         style.fill: "#ffe6cc"
         }
         ThirdParty: "☁️ Third-Party API" {
         shape: cloud
         style.fill: "#e6f7ff"
         }
         Messaging: "📩 Message Queue / Event Bus" {
         shape: parallelogram
         style.fill: "#fff2cc"
         }
         
         System: {
         shape: cloud
         style: {
            stroke-dash: 4
            fill: "#f2f2f2"
         }
         
         API: "🌐 Integration Layer / API Gateway" {
            shape: rectangle
            style.fill: "#d9ead3"
         }
         Service1: "⚙️ Core Service A" {
            shape: rectangle
            style.fill: "#f9cb9c"
         }
         Service2: "⚙️ Core Service B" {
            shape: rectangle
            style.fill: "#f9cb9c"
         }
         DB: "🗄️ Application Database" {
            shape: cylinder
            style.fill: "#d9d2e9"
         }
         Monitoring: "📊 Monitoring & Logging" {
            shape: rectangle
            style.fill: "#c9daf8"
         }
         
         API -> Service1
         API -> Service2
         Service1 -> DB
         Service2 -> DB
         Service1 -> Monitoring
         Service2 -> Monitoring
         DB -> Monitoring
         }

         CRM -> System.API: "🌐 REST" {
         style.stroke: "#4caf50"
         style.stroke-width: 2
         }
         ERP -> System.API: "📜 SOAP" {
         style.stroke: "#ff9800"
         style.stroke-dash: 4
         style.stroke-width: 2
         }
         Payment -> System.API: "💳 REST" {
         style.stroke: "#4caf50"
         style.stroke-width: 2
         }
         System.API -> Messaging: "📩 Publish Events" {
         style.stroke: "#2196f3"
         style.stroke-dash: 3
         }
         Messaging -> System.Service1: "📥 Subscribe Events" {
         style.stroke: "#2196f3"
         style.stroke-dash: 3
         }
         System.Service2 -> ThirdParty: "🔐 REST / OAuth2" {
         style.stroke: "#9c27b0"
         style.stroke-width: 2
         }

 
   Note: When generating the diagram:
   - Replace CRM, ERP, Payment, ThirdParty with actual external system names if available.
   - Replace REST, SOAP, Events with the actual integration patterns used.
   - Extend with additional services/interfaces depending on the real integration context.
   - Ensure data flow arrows clearly indicate direction of communication.

8. Generate High-Level Data Model (Data Architecture) Diagram:
 
   - Use D2 syntax instead of Mermaid.
   - Represent a high-level ER diagram or class diagram showing entities, attributes, and relationships.
   - Include data sources, sinks, and flows where applicable (e.g., external systems, APIs, databases, warehouses).
   - Show data governance aspects such as ownership (e.g., “owned by HR”, “managed by IT”).
   - Use generic, technology-agnostic labels unless specific technologies are provided.
   - Ensure relationships are directional and cardinalities are expressed (1:1, 1:N, M:N).
   - Follow D2 validation rules.
   
 
   IMPORTANT: Use this diagram as a structural reference only. Generate your diagram based on:
   - The identified users from the requirements
   - The actual relationships and interfaces discovered
   - The specific system name and purpose from the project context
 
   Generic High-Level Data Model (Data Architecture) Reference:
```   
   title: "High-Level Data Model - Entities, Relationships, and Data Flows"
   # Entities
   User: {
     shape: sql_table
     id: int
     name: string
     role: string
     governance: "Owned by HR"
   }
   
   WorkPermit: {
     shape: sql_table
     id: int
     status: string
     start_date: date
     end_date: date
     applicant_id: int
     governance: "Managed by Compliance Team"
   }
   
   # Relationship
   User -> WorkPermit: "applies (1:N)"
   
   # External Data Sources & Sinks
   HR_System: "HR Data Source"
   Gov_API: "Government API (Permit Verification)"
   Analytics: "Data Warehouse / Analytics Sink"
   
   # Data Flows
   HR_System -> User: "Sync Employee Data"
   User -> WorkPermit: "Application Data"
   WorkPermit -> Gov_API: "Validation Request"
   WorkPermit -> Analytics: "Permit Metrics"
   User -> Analytics: "User Activity Data"
```   
   
Notes for Generation:
  - Replace User, WorkPermit, etc., with actual domain entities.
  - Extend with additional entities (e.g., Department, Project, Document) as per context.
  - Add data governance tags (owner, steward, regulator).
  - Show flows to/from external systems (data sources/sinks).
  - Keep diagram readable and at high level.

{% block mermaid_diagram_requirements %}
    {% include 'includes/mermaid_guidance.jinja2' %}
{% endblock %}

   IMPORTANT: Use this diagram as a structural reference only. Generate your diagram based on:
   - The containers identified in the system context
   - The actual interfaces and relationships discovered
   - The specific technologies and protocols used
   - The external systems including databases from the context

   Generic Container Diagram Reference:
```
graph TB
   %% External Users
   PrimaryUser["Primary User<br/>[Person]<br/><i>Main system user</i>"]
   AdminUser["Admin User<br/>[Person]<br/><i>System administrator</i>"]

   subgraph CoreSystem[Core System Containers]
       %% Frontend Applications
       WebUI["Web Interface<br/>[Container: Frontend Tech]<br/><i>Main user interface</i>"]
       AdminUI["Admin Interface<br/>[Container: Frontend Tech]<br/><i>Administration interface</i>"]
       
       %% API Layer
       ApiGateway["API Gateway<br/>[Container: API Tech]<br/><i>Request routing and<br/>authentication</i>"]
       
       %% Core Services
       MainService["Main Service<br/>[Container: Backend Tech]<br/><i>Core business logic</i>"]
       
       SupportService["Support Service<br/>[Container: Backend Tech]<br/><i>Supporting functionality</i>"]
       
       %% Message Handling
       MessageBus["Message Bus<br/>[Container: Message Tech]<br/><i>Async communication</i>"]
   end

   %% External Systems
   PrimaryDB["Main Database<br/>[System_Ext]<br/><i>Primary data storage</i>"]
   CacheSystem["Cache Service<br/>[System_Ext]<br/><i>Data caching</i>"]
   AuthService["Auth System<br/>[System_Ext]<br/><i>Authentication</i>"]

   %% Relationships
   PrimaryUser -->|"Uses<br/>Protocol"| WebUI
   AdminUser -->|"Uses<br/>Protocol"| AdminUI
   
   WebUI -->|"API calls<br/>Protocol"| ApiGateway
   AdminUI -->|"API calls<br/>Protocol"| ApiGateway
   
   ApiGateway -->|"Routes<br/>Protocol"| MainService
   ApiGateway -->|"Routes<br/>Protocol"| SupportService
   
   MainService -->|"Reads/Writes<br/>Protocol"| PrimaryDB
   MainService -->|"Caches<br/>Protocol"| CacheSystem
   
   MainService -->|"Publishes<br/>Protocol"| MessageBus
   SupportService -->|"Subscribes<br/>Protocol"| MessageBus
   
   ApiGateway -->|"Authenticates<br/>Protocol"| AuthService

   %% Styling
   classDef person fill:#08427b,stroke:#052e56,color:#ffffff
   classDef container fill:#1168bd,stroke:#0b4884,color:#ffffff
   classDef external fill:#666666,stroke:#0b4884,color:#ffffff
   classDef boundary fill:none,stroke:#cccccc,stroke-dasharray:4

   class PrimaryUser,AdminUser person
   class WebUI,AdminUI,ApiGateway,MainService,SupportService,MessageBus container
   class PrimaryDB,CacheSystem,AuthService external
   class CoreSystem boundary
```

Note: When generating the diagram:
1. Replace generic names with actual container and system names
2. Use actual technologies in container descriptions
3. Show correct protocols in relationships
4. Include all identified containers and external systems
5. Represent actual system boundaries and dependencies

Change Needed: 
   - Set to False if changes are not required

Change Log:
   - Capture history of changes
{% endblock %}

{% block autoconfig %}
Design the internal container architecture focusing on containers and their interfaces.
{% endblock %}

{% block information_about_task %}
{{ super() }}
    Existing Containers:
    {{ details_for_discussion.get('containers') | tojson(indent=2) }}
    Existing interfaces: {{ details_for_discussion.get('interfaces') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
{{ super() }}
    Project Details: {{ details_for_discussion.get('project_details') }} 
    Architectural Requirements: {{ details_for_discussion.get('architectural_requirements', '') }}
    Functional Requirements: {{ details_for_discussion.get('functional_requirements', '') }}
    Existing Containers: {{ details_for_discussion.get('containers') | tojson(indent=2) }}
    Other Containers in the system: {{ details_for_discussion.get('other_containers') | tojson(indent=2) }}
{% endblock %} 