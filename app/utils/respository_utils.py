import os
import git
from typing import Dict, Any
from app.connection.tenant_middleware import get_user_id
from app.routes.scm_route import scm_manager
from app.utils.datetime_utils import generate_timestamp
from app.utils.hash import decrypt_string
from git import <PERSON>o
import json
from app.utils.code_generation_utils import get_codegeneration_path
from app.utils.project_utils import name_to_slug
from fastapi import HTTP<PERSON>xception, Depends
from app.connection.establish_db_connection import get_mongo_db, get_node_db, NodeDB
from app.core.constants import GIT_IGNORE_CONTENT
import logging
from github import Github
from app.core.Settings import settings
from app.models.scm import ACCESS_TOKEN_PATH
import asyncio
import threading
import uuid
import subprocess
import time
from contextlib import contextmanager

# Global set to track active clone operations
_active_clone_operations = set()
_clone_operations_lock = threading.Lock()


@contextmanager
def repository_lock(project_id: int, container_id: int):
    """
    Local lock for repository operations using threading.

    Args:
        project_id: Project ID
        container_id: Container ID
    """
    lock_key = f"repo_lock:{project_id}:{container_id}"

    # Use a simple threading lock for local synchronization
    with _clone_operations_lock:
        if lock_key in _active_clone_operations:
            print(generate_timestamp(), f"⚠️ Repository operation already in progress for project {project_id}, container {container_id}")
            raise Exception(f"Repository operation already in progress for project {project_id}, container {container_id}")

        _active_clone_operations.add(lock_key)
        print(generate_timestamp(), f"🔒 Acquired repository lock for project {project_id}, container {container_id}")

    try:
        yield
    finally:
        with _clone_operations_lock:
            _active_clone_operations.discard(lock_key)
            print(generate_timestamp(), f"🔓 Released repository lock for project {project_id}, container {container_id}")


async def cleanup_duplicate_repositories(project_id: int, db: NodeDB = None) -> Dict[str, Any]:
    """
    Identify and clean up duplicate repositories for a project.

    Args:
        project_id: Project ID to clean up
        db: Database connection

    Returns:
        Dictionary with cleanup results
    """
    if not db:
        db = get_node_db()

    try:
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")

        if not project_details:
            return {"error": "Project not found"}

        project_repositories = json.loads(project_details.get("repositories", "{}"))

        # Group repositories by container_id to find duplicates
        container_repos = {}
        duplicates_found = []

        for container_id, repo_data in project_repositories.items():
            repo_name = repo_data.get("repositoryName", "")

            # Check for repositories with similar names (indicating duplicates)
            base_name = repo_name.split('-')[0] if '-' in repo_name else repo_name

            if base_name not in container_repos:
                container_repos[base_name] = []
            container_repos[base_name].append({
                "container_id": container_id,
                "repo_data": repo_data,
                "repo_name": repo_name
            })

        # Identify duplicates (more than one repo per base name)
        for base_name, repos in container_repos.items():
            if len(repos) > 1:
                duplicates_found.append({
                    "base_name": base_name,
                    "repositories": repos,
                    "count": len(repos)
                })

        cleanup_results = {
            "project_id": project_id,
            "duplicates_found": len(duplicates_found),
            "duplicate_groups": duplicates_found,
            "total_repositories": len(project_repositories)
        }

        print(generate_timestamp(), f"Found {len(duplicates_found)} duplicate repository groups for project {project_id}")

        return cleanup_results

    except Exception as e:
        print(generate_timestamp(), f"Error during repository cleanup: {str(e)}")
        return {"error": str(e)}


async def consolidate_duplicate_repositories(project_id: int, dry_run: bool = True, db: NodeDB = None) -> Dict[str, Any]:
    """
    Consolidate duplicate repositories by keeping the most recent one and removing others.

    Args:
        project_id: Project ID to consolidate
        dry_run: If True, only report what would be done without making changes
        db: Database connection

    Returns:
        Dictionary with consolidation results
    """
    if not db:
        db = get_node_db()

    try:
        cleanup_results = await cleanup_duplicate_repositories(project_id, db)

        if "error" in cleanup_results:
            return cleanup_results

        if cleanup_results["duplicates_found"] == 0:
            return {"message": "No duplicate repositories found", "project_id": project_id}

        consolidation_actions = []

        for duplicate_group in cleanup_results["duplicate_groups"]:
            repositories = duplicate_group["repositories"]

            # Sort by repository creation time or use the first one as canonical
            canonical_repo = repositories[0]  # Keep the first one as canonical
            repos_to_remove = repositories[1:]  # Remove the rest

            action = {
                "base_name": duplicate_group["base_name"],
                "canonical_repository": {
                    "container_id": canonical_repo["container_id"],
                    "repo_name": canonical_repo["repo_name"]
                },
                "repositories_to_remove": [
                    {
                        "container_id": repo["container_id"],
                        "repo_name": repo["repo_name"]
                    }
                    for repo in repos_to_remove
                ]
            }

            consolidation_actions.append(action)

            if not dry_run:
                # Actually perform the consolidation
                project = await db.get_node_by_id(project_id)
                project_details = project.get("properties")
                project_repositories = json.loads(project_details.get("repositories", "{}"))

                # Remove duplicate repositories from project
                for repo_to_remove in repos_to_remove:
                    container_id = repo_to_remove["container_id"]
                    if container_id in project_repositories:
                        del project_repositories[container_id]
                        print(generate_timestamp(), f"Removed duplicate repository {repo_to_remove['repo_name']} for container {container_id}")

                # Update project with consolidated repositories
                await db.update_node_by_id(
                    project_id,
                    {"repositories": json.dumps(project_repositories)}
                )

        result = {
            "project_id": project_id,
            "dry_run": dry_run,
            "consolidation_actions": consolidation_actions,
            "total_duplicates_removed": sum(len(action["repositories_to_remove"]) for action in consolidation_actions)
        }

        if dry_run:
            result["message"] = "Dry run completed. Use dry_run=False to actually consolidate repositories."
        else:
            result["message"] = f"Successfully consolidated {result['total_duplicates_removed']} duplicate repositories."

        return result

    except Exception as e:
        print(generate_timestamp(), f"Error during repository consolidation: {str(e)}")
        return {"error": str(e)}
from app.connection.tenant_middleware import get_tenant_id
from typing import Dict, Any
import asyncio


def get_github_client():
    return Github(settings.GITHUB_ACCESS_TOKEN)

def ensure_git_safe_directory(repo_path):
    """
    Configure Git to trust the repository directory if ownership issues are detected.
    This addresses the 'dubious ownership' error that can occur when Git's
    ownership security checks fail.
    
    Args:
        repo_path (str): Path to the git repository
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        import subprocess
        # Add the directory to Git's safe.directory configuration
        cmd = ["git", "config", "--global", "--add", "safe.directory", repo_path]
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(generate_timestamp(),f"Added repository to Git safe directories: {repo_path}")
        #filemode for repository
        cmd = ["git", "config", "--global", "core.filemode", "false"]
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(generate_timestamp(),f"Set core.filemode to false for repository: {repo_path}")
        return True
    except subprocess.CalledProcessError as e:
        print(generate_timestamp(),f"Error adding repository to Git safe directories: {e}")
        print(generate_timestamp(),f"Command stderr: {e.stderr}")
        return False
    except Exception as e:
        print(generate_timestamp(),f"Unexpected error configuring Git safe directory: {e}")
        return False



async def create_repository_in_mongo(
    project_id: int,
    repo_id: str,
    repo_name: str = "example/dummy-repo",
    branch_name: str = "main",
    repo_type: str = "source",
    associated: bool = True,
    upstream: bool = False
) -> Dict[str, Any]:
   
    from app.connection.tenant_middleware import get_tenant_id
    from app.routes.code_query import generate_random_prefix
    from app.routes.kg_route import generate_build_id
    user_id = 'test'
    
    """
    Create a dummy repository document in MongoDB for testing purposes.
    
    Args:
        project_id: The project identifier
        user_id: The user identifier
        repo_name: Repository name in format "owner/repo"
        branch_name: Branch name to use
        repo_type: Type of repository ("source", "test", etc.)
        associated: Whether repository is associated
        upstream: Whether this is an upstream repository
        
    Returns:
        Dict containing the created document and metadata
    """
    try:
        # Generate required IDs
        build_id = generate_build_id()
        build_session_id = f"{generate_random_prefix()}-{build_id}"
        
        # Get tenant and data directory
        tenant_id = get_tenant_id()
        root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        data_dir = os.path.join(root_dir, 'data', tenant_id, str(project_id))
        
        # Create repository structure
        dummy_repository = {
            "service": "github",
            "repo_id": repo_id,
            "repository_name": repo_name,
            "associated": associated,
            "git_url": f"https://github.com/{repo_name}.git",
            "repositoryStatus": "initialized",
            "clone_url_ssh": f"**************:{repo_name}.git",
            "repo_type": repo_type,
            "branches": [
                {
                    "name": branch_name,
                    "latest_commit_hash": None,
                    "builds": {
                        "build_id": build_id,
                        "build_session_id": build_session_id,
                        "path": f"{data_dir}/{build_id}/{repo_name.split('/')[-1]}",
                        "kg_creation_status": 1,
                        "build_info": {
                            "start_time": generate_timestamp(),
                            "end_time": None,
                            "last_updated": None,
                            "duration_seconds": None
                        },
                        "last_updated": generate_timestamp(),
                        "user_id": user_id,
                        "error": None
                    }
                }
            ],
            "selected_branch": branch_name
        }
        
        # Create document structure
        document = {
            "project_id": project_id,
            "created_at": generate_timestamp(),
            "repositories": [dummy_repository]
        }
        
        # Initialize MongoDB handler
        mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='project_repositories',
            user_id=user_id
        )
        
        # Check for existing document
        existing_doc = await mongo_handler.get_one(
            filter={'project_id': project_id},
            db=mongo_handler.db
        )
        
        if existing_doc:
            # Append to existing repositories
            existing_repos = existing_doc.get('repositories', [])
            existing_repos.append(dummy_repository)
            document['repositories'] = existing_repos
        
        # Upsert the document
        await mongo_handler.update_one(
            filter={'project_id': project_id},
            element=document,
            upsert=True,
            db=mongo_handler.db
        )
        
        return {
            "status": "success",
            "message": "Dummy repository created successfully",
            "data": {
                "project_id": project_id,
                "repository": dummy_repository,
                "build_id": build_id,
                "build_session_id": build_session_id
            }
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"Failed to create dummy repository: {str(e)}",
            "data": None
        }





def manage_filesystem_lock(operation: str, repo_name: str, process_id: int = None):
    """
    Manage filesystem lock file for repository operations.
    
    Args:
        operation: 'add' or 'remove'
        repo_name: Name of the repository
        process_id: Process ID to track (for add operation)
    """
    lock_file_path = os.path.join(get_codegeneration_path(), ".filesystem.lock")
    
    try:
        # Ensure the directory exists
        os.makedirs(os.path.dirname(lock_file_path), exist_ok=True)
        
        # Read existing PIDs
        pids = set()
        if os.path.exists(lock_file_path):
            try:
                with open(lock_file_path, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and line.isdigit():
                            pids.add(int(line))
            except (IOError, ValueError):
                pids = set()
        
        if operation == 'add':
            if process_id is None:
                process_id = os.getpid()
            
            pids.add(process_id)
            print(generate_timestamp(), f"🔒 Added PID {process_id} to filesystem lock for {repo_name}")
            
        elif operation == 'remove':
            if process_id is None:
                process_id = os.getpid()
            
            pids.discard(process_id)
            print(generate_timestamp(), f"🔓 Removed PID {process_id} from filesystem lock for {repo_name}")
        
        # Write updated PIDs or delete file if empty
        if pids:
            with open(lock_file_path, 'w') as f:
                for pid in sorted(pids):
                    f.write(f"{pid}\n")
        else:
            # Delete lock file if no PIDs remain
            if os.path.exists(lock_file_path):
                os.remove(lock_file_path)
                print(generate_timestamp(), f"🗑️ Deleted empty filesystem lock file")
            
    except Exception as e:
        print(generate_timestamp(), f"Error managing filesystem lock: {str(e)}")
        
        
def set_repository_permissions(repo_path: str):
    """
    Simple function to set repository permissions to 777 recursively.
    
    Args:
        repo_path: Path to the repository
    """
    try:
        # Set permissions using chmod command
        subprocess.run(["chmod", "-R", "777", repo_path], check=True, capture_output=True)
        print(f"🔒 Set 777 permissions for repository: {repo_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to set permissions for {repo_path}: {str(e)}")
        return False

def _run_clone_in_background(
    repository_metadata: Dict,
    tenant_id: str,
    task_id: str = None,
    repo_name: str = None,
    github_repo: str = None
):
    """Background function to run the clone process in a daemon thread."""
    # For deep-query operations, use a simpler key to ensure only one clone per repository
    if task_id and task_id.startswith("deep-query"):
        clone_key = f"deep-query:{repo_name}"
        print(generate_timestamp(), f"🔍 Deep query operation: Using single repository clone for {repo_name}")
    else:
        clone_key = f"{repo_name}:{task_id}" if task_id else repo_name

    # Check if clone operation is already in progress
    with _clone_operations_lock:
        if clone_key in _active_clone_operations:
            print(generate_timestamp(), f"⚠️ Clone operation already in progress for repository: {repo_name}, skipping duplicate")
            return
        _active_clone_operations.add(clone_key)

    try:
        # Add PID to filesystem lock
        manage_filesystem_lock('add', repo_name, os.getpid())

        print(generate_timestamp(), f"🔄 Starting background clone for repository: {repo_name}")

        # Perform the actual cloning
        clone_path = clone_repository(repository_metadata, tenant_id, task_id)
        
        if github_repo:
            gitignore = github_repo.get_contents(".gitignore")
            github_repo.update_file(
                path=".gitignore",
                message="Update .gitignore",
                content=GIT_IGNORE_CONTENT,
                sha=gitignore.sha,
                branch="main"
            )
        
        if clone_path and os.path.exists(clone_path):
            set_repository_permissions(clone_path)
        
        print(generate_timestamp(), f"✅ Background clone completed for repository: {repo_name} at {clone_path}")

    except Exception as e:
        import traceback
        traceback.print_exc()
        print(generate_timestamp(), f"❌ Error in background clone thread for {repo_name}: {str(e)}")
    finally:
        manage_filesystem_lock('remove', repo_name, os.getpid())
        # Remove from active clone operations
        with _clone_operations_lock:
            _active_clone_operations.discard(clone_key)
        
def create_kavia_main_branch(github_repo):
    try:
        main_branch = github_repo.get_branch("main")
        github_repo.create_git_ref(ref="refs/heads/kavia-main", sha=main_branch.commit.sha)
        github_repo.edit(default_branch="kavia-main")
        
    except Exception as e:
        print(generate_timestamp(),f"Warning: Could not set kavia-main as default branch: {str(e)}")
        # Continue with the repository creation even if branch operations fail

async def create_repository_in_workspace(project_id: int, container_id: int ,db: NodeDB = Depends(get_node_db)):
    # Use local lock to prevent race conditions
    with repository_lock(project_id, container_id):
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")

        # Double-check if repository already exists after acquiring lock
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        existing_repository = project_repositories.get(str(container_id))

        if existing_repository:
            print(generate_timestamp(), f"Repository already exists for project {project_id}, container {container_id}: {existing_repository.get('repositoryName')}")
            return {"repository": existing_repository}

        try:
            print(generate_timestamp(),"Creating repository in workspace")
            # Generate deterministic repository name based on project and container IDs
            repository_name = name_to_slug(
                f"{project_details.get('Title', project_details.get('Name'))}"
            )
            # Use deterministic naming instead of random UUID to prevent duplicates
            repository_name = f'{repository_name}-{project_id}-{container_id}'

            github_client = get_github_client()

            # Rename 'repo' to 'github_repo' to avoid confusion
            github_repo = github_client.get_user().create_repo(
                name=repository_name,
                private=False,
                auto_init=True,
                gitignore_template="Python"
            )

            create_kavia_main_branch(github_repo)

            repository_metadata = {
                'service': 'github',
                'repositoryName': github_repo.name,
                'repositoryId': str(github_repo.id),
                'cloneUrlHttp': github_repo.clone_url,
                'cloneUrlSsh': github_repo.ssh_url,
                'organization': github_client.get_user().login,
                'access_token_path': ACCESS_TOKEN_PATH.KAVIA_MANANGED.value,
                'repositoryStatus': 'initialized'
            }

            print(generate_timestamp(),"Repository meta data")
            print(generate_timestamp(),repository_metadata)

            # Update project repositories
            project_repositories = json.loads(project_details.get("repositories", "{}"))
            project_repositories[str(container_id)] = repository_metadata

            # Update the node database with repository metadata
            await db.update_node_by_id(
                project_id,
                {"repositories": json.dumps(project_repositories)}
            )

            if os.getenv("task_id"):
                tenant_id = get_tenant_id()
                task_id = os.environ.get("task_id", f"repo-{project_id}-{container_id}")
                access_token = repository_metadata.get("access_token")
                if not access_token or repository_metadata.get("organization") == settings.GITHUB_DEFAULT_ORGANIZATION:
                    access_token = settings.GITHUB_ACCESS_TOKEN
                repository_metadata["access_token"] = access_token
                clone_thread = threading.Thread(
                    target=_run_clone_in_background,
                    args=(
                        repository_metadata,
                        tenant_id,
                        task_id,
                        repository_name,
                        github_repo
                    ),
                    daemon=True
                )
                clone_thread.start()
                print(generate_timestamp(),generate_timestamp(), f"Started background clone thread for repository: {repository_name}")

            return repository_metadata

        except Exception as e:
            print(generate_timestamp(),f"Error creating local repository: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to create local repository: {str(e)}")
    
async def fork_repository_in_workspace(
    project_id: int, 
    container_id: int, 
    db: NodeDB = Depends(get_node_db)
):
    """
    Fork an existing repository into the workspace.
    
    Args:
        project_id: ID of the project
        container_id: ID of the container
        source_repo_url: URL of the repository to fork (e.g., 'owner/repo-name')
        db: Database dependency
        
    Returns:
        dict: Repository metadata of the forked repository
        
    Raises:
        HTTPException: If forking fails
    """
    try:
        # Fetch project and container details
        project, container = await asyncio.gather(
            db.get_node_by_id(project_id),
            db.get_node_by_id(container_id)
        )
        
        project_details = project.get("properties", {})
        container_details = container.get("properties", {})
        
        repo_info = project_details.get('repositories','{}')
        data = json.loads(repo_info)
        # Collect all cloneUrlHttp values
        clone_urls = [value["cloneUrlHttp"] for value in data.values()]
        if clone_urls :
            source_repo_url = clone_urls[0]
        else:
            raise HTTPException(status_code=400, detail="no source_repo_url found to fork")    
        print(generate_timestamp(),f"Forking repository {source_repo_url} in workspace")
        
        # Parse source repository URL to extract owner and repo name
        source_owner, source_repo_name = _parse_github_url(source_repo_url)

        # Generate new repository name for the fork
        project_title = project_details.get('Title') or project_details.get('Name', 'project')
        fork_name = name_to_slug(f"{project_title}-{source_repo_name}")
        fork_name = f'{fork_name}-{container_id}'
        
        github_client = get_github_client()
        user = github_client.get_user()
        
        # Get the source repository
        source_repo = github_client.get_repo(f"{source_owner}/{source_repo_name}")
        
        # Fork the repository
        forked_repo = github_client.get_user().create_fork(source_repo)
        # forked_repo = user.create_fork(source_repo)
        
        # Rename the forked repository if needed
        if forked_repo.name != fork_name:
            forked_repo.edit(name=fork_name)
        
        # Prepare repository metadata
        repository_metadata = {
            'service': 'github',
            'repositoryName': forked_repo.name,
            'repositoryId': str(forked_repo.id),
            'cloneUrlHttp': forked_repo.clone_url,
            'cloneUrlSsh': forked_repo.ssh_url,
            'organization': user.login,
            'access_token_path': ACCESS_TOKEN_PATH.KAVIA_MANANGED.value,
            'repositoryStatus': 'forked',
            'sourceRepository': {
                'owner': source_owner,
                'name': source_repo_name,
                'url': source_repo.html_url
            },
            'forkedAt': forked_repo.created_at.isoformat()
        }
        
        # Update project repositories
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        project_repositories[str(container_id)] = repository_metadata
        
        await db.update_node_by_id(
            project_id,
            {"repositories": json.dumps(project_repositories)}
        )
        
        print(generate_timestamp(),f"Successfully forked repository: {forked_repo.full_name}")
        return repository_metadata
        
    except ValueError as ve:
        error_msg = f"Invalid repository URL format: {str(ve)}"
        raise HTTPException(status_code=400, detail=error_msg)
    except Exception as e:
        error_message = str(e).lower()
        # Determine appropriate HTTP status based on error message
        if "not found" in error_message or "404" in error_message:
            status_code = 404
            detail = f"Source repository not found: {source_repo_url}"
        elif "forbidden" in error_message or "403" in error_message:
            status_code = 403
            detail = "Access forbidden: Check repository permissions and API token"
        elif "unauthorized" in error_message or "401" in error_message:
            status_code = 401
            detail = "Unauthorized: Invalid or missing GitHub token"
        elif "rate limit" in error_message:
            status_code = 429
            detail = "GitHub API rate limit exceeded"
        else:
            status_code = 500
            detail = f"Failed to fork repository: {str(e)}"
        
        print(generate_timestamp(),f"Error forking repository: {detail}")
        raise HTTPException(status_code=status_code, detail=detail)

def _parse_github_url(repo_url: str) -> tuple[str, str]:
    """
    Parse GitHub repository URL to extract owner and repository name.
    """
    # Clean the URL - remove .git suffix if present
    clean_url = repo_url.rstrip('/').rstrip('.git')  # This should remove .git
    
    if clean_url.startswith(('http://', 'https://')):
        # Extract from full URL: https://github.com/owner/repo
        parts = clean_url.split('/')
        if len(parts) >= 2 and 'github.com' in clean_url:
            owner = parts[-2]
            repo_name = parts[-1]
            
            # Double-check to ensure .git is removed
            if repo_name.endswith('.git'):
                repo_name = repo_name[:-4]
                
            return owner, repo_name
        else:
            raise ValueError(f"Invalid GitHub URL format: {repo_url}")
    elif '/' in clean_url:
        # Handle 'owner/repo' format
        parts = clean_url.split('/')
        if len(parts) == 2:
            owner = parts[0]
            repo_name = parts[1]
            
            # Double-check to ensure .git is removed
            if repo_name.endswith('.git'):
                repo_name = repo_name[:-4]
                
            return owner, repo_name
        else:
            raise ValueError(f"Invalid repository format: {repo_url}. Expected 'owner/repo'")
    else:
        raise ValueError(f"Invalid repository URL: {repo_url}")

def process_files(input_dir: str, stop_point: str, new_base: str):
    """
    Process files in the input directory by modifying the filename and content.
    The filename is modified by replacing the path up to the stop_point with new_base.
    The content is modified by replacing the path up to the stop_point with new_base.
    """
    # Walk through all files in the input directory
    for root, _, files in os.walk(input_dir):
        for file in files:
            file_path = os.path.join(root, file)
            
            try:
                # Read the file content
                with open(file_path, 'r') as f:
                    content = f.read()
                
                # Parse JSON content
                try:
                    data = json.loads(content)
                except json.JSONDecodeError:
                    print(generate_timestamp(),f"Skipping {file}: Not a valid JSON file")
                    continue
                
                # Process filename modification
                print(generate_timestamp(),f"Processing: {file}")
                old_filename = file
                
                # Find the index of stop_point in the filename
                stop_idx = old_filename.find(stop_point)
                if stop_idx == -1:
                    print(generate_timestamp(),f"Stop point '{stop_point}' not found in {file}")
                    continue
                
                # Extract the part after stop_point
                remaining_path = old_filename[stop_idx:]
                
                # Create new filename with new_base
                new_filename = "_".join(new_base.split("/")) + remaining_path
                
                # Process content modification
                if 'filename' in data:
                    # Get the old path up to stop_point
                    old_path = data['filename']
                    stop_idx_content = old_path.rfind(stop_point)
                    
                    if stop_idx_content != -1:
                        # Replace the path up to stop_point with new_base
                        # Add a slash between new_base and the remaining path
                        if not new_base.endswith('/'):
                            new_base = new_base + '/'
                        data['filename'] = new_base + old_path[stop_idx_content:]
                
                # Create new file path
                new_file_path = os.path.join(root, new_filename)
                
                # Write modified content to new file
                with open(new_file_path, 'w') as f:
                    json.dump(data, f, indent=2)
                
                # Remove old file if new filename is different
                if new_file_path != file_path:
                    os.remove(file_path)
                
                print(generate_timestamp(),f"Processed: {file} -> {new_filename}")
                
            except Exception as e:
                print(generate_timestamp(),f"Error processing {file}: {str(e)}")
                
def setup_git_credentials(repo_path: str, access_token: str, remote_url: str):
    """Configure git credentials for the repository"""
    # First, add the repository to Git's safe directories
    # Import the ensure_git_safe_directory function if available, or define it inline
    try:
        ensure_git_safe_directory(repo_path)
    except Exception as e:
        print(generate_timestamp(),f"Error ensuring Git safe directory: {e}")
        # Define the function inline if import fails
        import subprocess
        try:
            cmd = ["git", "config", "--global", "--add", "safe.directory", repo_path]
            subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(generate_timestamp(),f"Added repository to Git safe directories: {repo_path}")
        except Exception as e:
            print(generate_timestamp(),f"Error adding repository to Git safe directories: {e}")
    
    # Now continue with the original function
    repo = Repo(repo_path)
    
    # Set remote URL with embedded credentials
    if 'https://' in remote_url:
        # For HTTPS URLs, embed the token in the URL
        parsed_url = remote_url.replace('https://', '')
        new_url = f'https://oauth2:{access_token}@{parsed_url}'
        repo.git.remote('set-url', 'origin', new_url)
    else:
        # For SSH URLs, we might need to configure SSH keys
        # This would be handled by the system's SSH configuration
        pass

def create_session_branches(repo: Repo, task_id: str = None) -> Dict[str, str]:
    """
    Create and manage session branches for repository operations.
    
    Creates kavia-main branch from current branch if it doesn't exist,
    and optionally creates cga-{task_id} branch from kavia-main.
    Can also update .gitignore file when kavia-main is created.
    
    Args:
        repo: GitPython Repo object
        task_id: Optional task ID to create task-specific branch
    
    Returns:
        Dict with information about created/switched branches
    """
    
    gitignore_content = GIT_IGNORE_CONTENT
    
    print(generate_timestamp(),"### SESSION MANAGEMENT ###")
    print(generate_timestamp(),"### Creating session branches")
    result = {
        "kavia_main_status": "",
        "task_branch_status": "",
        "current_branch": "",
        "gitignore_update": "",
        "errors": []
    }
    
    try:
        # Fetch latest remote branches first
        try:
            repo.git.fetch('origin')
            print(generate_timestamp(),"Fetched latest remote branches")
        except Exception as e:
            print(generate_timestamp(),f"Warning: Could not fetch remote branches: {str(e)}")
            result["errors"].append(f"Fetch failed: {str(e)}")
        
        # Get existing branches - use more reliable method for remote branches
        existing_local_branches = [ref.name.split('/')[-1] for ref in repo.refs if ref.name.startswith('refs/heads/')]
        
        # Get remote branches using git command for accuracy after fetch
        try:
            remote_branch_output = repo.git.branch('-r')
            existing_remote_branches = []
            for line in remote_branch_output.split('\n'):
                line = line.strip()
                if line and 'origin/' in line and '->' not in line:  # Skip HEAD pointer
                    branch_name = line.replace('origin/', '').strip()
                    existing_remote_branches.append(branch_name)
            print(generate_timestamp(),f"Found remote branches: {existing_remote_branches}")
        except Exception as e:
            print(generate_timestamp(),f"Warning: Could not get remote branches, falling back to refs: {str(e)}")
            existing_remote_branches = [ref.name.split('/')[-1] for ref in repo.refs if ref.name.startswith('refs/remotes/origin/')]
        
        # === KAVIA-MAIN BRANCH OPERATIONS ===
        kavia_main_created = False
        if 'kavia-main' not in existing_local_branches:
            print(generate_timestamp(),"kavia-main branch does not exist locally, creating it...")
            
            # Check if kavia-main exists remotely
            if 'kavia-main' in existing_remote_branches:
                print(generate_timestamp(),"kavia-main exists remotely, creating tracking branch...")
                try:
                    # Stash any untracked files before checkout
                    try:
                        repo.git.stash('--include-untracked')
                        print(generate_timestamp(),"Stashed untracked files")
                    except Exception as stash_e:
                        print(generate_timestamp(),f"Note: Could not stash untracked files: {str(stash_e)}")
                        result["errors"].append(f"Stash failed: {str(stash_e)}")
                    
                    # Create local branch that tracks remote
                    repo.git.checkout('-b', 'kavia-main', '--track', 'origin/kavia-main')
                    print(generate_timestamp(),"Created kavia-main branch tracking remote")
                    result["kavia_main_status"] = "created_tracking_remote"
                    kavia_main_created = True
                    
                    # Try to pop stash if we stashed earlier
                    try:
                        repo.git.stash('pop')
                        print(generate_timestamp(),"Popped stashed changes")
                    except Exception as pop_e:
                        if "No stash entries found" not in str(pop_e):
                            print(generate_timestamp(),f"Note: Could not pop stash: {str(pop_e)}")
                            result["errors"].append(f"Stash pop failed: {str(pop_e)}")
                            
                except Exception as e:
                    print(generate_timestamp(),f"Error tracking remote kavia-main: {str(e)}")
                    result["errors"].append(f"Track remote kavia-main failed: {str(e)}")
                    # Fall back to creating from current branch
                    current_branch = None
                    try:
                        current_branch = repo.active_branch.name
                        print(generate_timestamp(),f"Falling back to creating from current branch: {current_branch}")
                    except Exception:
                        print(generate_timestamp(),"Could not determine current branch")
                    
                    if current_branch:
                        try:
                            # Create kavia-main from the current branch
                            repo.git.checkout('-b', 'kavia-main')
                            print(generate_timestamp(),f"Created kavia-main branch from {current_branch}")
                            result["kavia_main_status"] = f"created_from_{current_branch}"
                            kavia_main_created = True
                        except Exception as create_e:
                            print(generate_timestamp(),f"Error: Could not create kavia-main branch: {str(create_e)}")
                            result["errors"].append(f"Create kavia-main failed: {str(create_e)}")
                            result["kavia_main_status"] = f"failed_fallback_to_{current_branch}"
            else:
                # Remote branch doesn't exist, proceed with creating from current branch
                current_branch = None
                try:
                    current_branch = repo.active_branch.name
                    print(generate_timestamp(),f"Current branch: {current_branch}")
                except Exception:
                    print(generate_timestamp(),"Could not determine current branch")
                
                if current_branch:
                    try:
                        # Create kavia-main from the current branch
                        repo.git.checkout('-b', 'kavia-main')
                        print(generate_timestamp(),f"Created kavia-main branch from {current_branch}")
                        result["kavia_main_status"] = f"created_from_{current_branch}"
                        kavia_main_created = True
                        
                        # Update .gitignore if content is provided
                        if gitignore_content and kavia_main_created:
                            try:
                                # Check if .gitignore exists
                                gitignore_path = os.path.join(repo.working_dir, '.gitignore')
                                if os.path.exists(gitignore_path):
                                    # Update existing .gitignore
                                    with open(gitignore_path, 'w') as f:
                                        f.write(gitignore_content)
                                    # Stage the changes
                                    repo.git.add('.gitignore')
                                    # Commit the changes
                                    repo.git.commit('-m', 'Update .gitignore')
                                    print(generate_timestamp(), "Updated .gitignore file")
                                    result["gitignore_update"] = "updated"
                                else:
                                    # Create new .gitignore
                                    with open(gitignore_path, 'w') as f:
                                        f.write(gitignore_content)
                                    # Stage the changes
                                    repo.git.add('.gitignore')
                                    # Commit the changes
                                    repo.git.commit('-m', 'Add .gitignore')
                                    print(generate_timestamp(), "Created .gitignore file")
                                    result["gitignore_update"] = "created"
                            except Exception as gitignore_e:
                                print(generate_timestamp(), f"Error updating .gitignore: {str(gitignore_e)}")
                                result["errors"].append(f"Gitignore update failed: {str(gitignore_e)}")
                                result["gitignore_update"] = "failed"
                        
                        # Push kavia-main to origin with upstream tracking only if it doesn't exist remotely
                        try:
                            repo.git.push('--set-upstream', 'origin', 'kavia-main')
                            print(generate_timestamp(),f"Successfully pushed kavia-main branch to origin with upstream tracking")
                            result["kavia_main_status"] += "_and_pushed"
                        except Exception as push_e:
                            print(generate_timestamp(),f"Warning: Could not push kavia-main to origin: {str(push_e)}")
                            result["errors"].append(f"Push kavia-main failed: {str(push_e)}")
                            
                    except Exception as create_e:
                        print(generate_timestamp(),f"Error: Could not create kavia-main branch: {str(create_e)}")
                        result["errors"].append(f"Create kavia-main failed: {str(create_e)}")
                        # Fall back to current branch
                        result["kavia_main_status"] = f"failed_fallback_to_{current_branch}"
                else:
                    print(generate_timestamp(),"Error: Could not find suitable base branch for kavia-main")
                    result["errors"].append("No suitable base branch found for kavia-main")
                    result["kavia_main_status"] = "failed_no_base_branch"
        else:
            # kavia-main exists locally, switch to it
            try:
                repo.git.checkout('kavia-main')
                result["kavia_main_status"] = "switched_to_existing"
                
                # Try to pull latest changes
                try:
                    repo.git.pull('origin', 'kavia-main')
                    print(generate_timestamp(),f"Switched to kavia-main branch and pulled latest changes")
                    result["kavia_main_status"] += "_and_pulled"
                except Exception as e:
                    print(generate_timestamp(),f"Note: Could not pull kavia-main (may not exist on remote): {str(e)}")
                    result["errors"].append(f"Pull kavia-main failed: {str(e)}")
                    
            except Exception as e:
                print(generate_timestamp(),f"Error: Could not switch to kavia-main: {str(e)}")
                result["errors"].append(f"Switch to kavia-main failed: {str(e)}")
                result["kavia_main_status"] = "failed_to_switch"
        
        # === TASK BRANCH OPERATIONS ===
        if task_id:
            task_branch_name = f"cga-{task_id}"
            
            if task_branch_name not in existing_local_branches:
                # Check if branch exists remotely but not locally
                if task_branch_name in existing_remote_branches:
                    try:
                        # Checkout existing remote branch - use simple checkout to track automatically
                        repo.git.checkout(task_branch_name)
                        print(generate_timestamp(),f"Checked out existing remote branch: {task_branch_name}")
                        result["task_branch_status"] = "checked_out_remote"
                        
                        # Try to pull latest changes from remote
                        try:
                            repo.git.pull('origin', task_branch_name)
                            print(generate_timestamp(),f"Pulled latest changes for branch: {task_branch_name}")
                            result["task_branch_status"] += "_and_pulled"
                        except Exception as e:
                            print(generate_timestamp(),f"Note: Could not pull latest changes: {str(e)}")
                            result["errors"].append(f"Pull task branch failed: {str(e)}")
                            
                    except Exception as e:
                        print(generate_timestamp(),f"Error: Could not checkout remote task branch: {str(e)}")
                        # Fallback: try with explicit tracking
                        try:
                            repo.git.checkout('-b', task_branch_name, f'origin/{task_branch_name}')
                            print(generate_timestamp(),f"Checked out remote branch with explicit tracking: {task_branch_name}")
                            result["task_branch_status"] = "checked_out_remote_fallback"
                        except Exception as e2:
                            print(generate_timestamp(),f"Error: Fallback checkout also failed: {str(e2)}")
                            result["errors"].append(f"Checkout remote task branch failed: {str(e)} | Fallback failed: {str(e2)}")
                            result["task_branch_status"] = "failed_checkout_remote"
                else:
                    try:
                        # Create new branch from kavia-main (assuming we're on kavia-main now)
                        repo.git.checkout('-b', task_branch_name)
                        print(generate_timestamp(),f"Created and switched to new branch: {task_branch_name} from kavia-main")
                        result["task_branch_status"] = "created_from_kavia_main"
                        
                        # Push the new branch to set upstream tracking
                        try:
                            repo.git.push('--set-upstream', 'origin', task_branch_name)
                            print(generate_timestamp(),f"Successfully pushed branch {task_branch_name} to origin with upstream tracking")
                            result["task_branch_status"] += "_and_pushed"
                        except Exception as e:
                            print(generate_timestamp(),f"Warning: Could not push task branch to origin: {str(e)}")
                            result["errors"].append(f"Push task branch failed: {str(e)}")
                            
                    except Exception as e:
                        print(generate_timestamp(),f"Error: Could not create task branch: {str(e)}")
                        result["errors"].append(f"Create task branch failed: {str(e)}")
                        result["task_branch_status"] = "failed_to_create"
            else:
                try:
                    # Branch exists locally, just checkout
                    repo.git.checkout(task_branch_name)
                    print(generate_timestamp(),f"Switched to existing local branch: {task_branch_name}")
                    result["task_branch_status"] = "switched_to_existing"
                    
                    # Optionally pull latest changes if tracking remote
                    try:
                        repo.git.pull()
                        print(generate_timestamp(),f"Pulled latest changes for branch: {task_branch_name}")
                        result["task_branch_status"] += "_and_pulled"
                    except Exception as e:
                        print(generate_timestamp(),f"Note: Could not pull latest changes: {str(e)}")
                        result["errors"].append(f"Pull task branch failed: {str(e)}")
                        
                except Exception as e:
                    print(generate_timestamp(),f"Error: Could not switch to task branch: {str(e)}")
                    result["errors"].append(f"Switch to task branch failed: {str(e)}")
                    result["task_branch_status"] = "failed_to_switch"
        
        # Get final current branch
        print(generate_timestamp(),"### Getting final current branch")
        try:
            result["current_branch"] = repo.active_branch.name
        except Exception:
            result["current_branch"] = "unknown"
            
    except Exception as e:
        print(generate_timestamp(),f"Error in create_session_branches: {str(e)}")
        result["errors"].append(f"General error: {str(e)}")
    
    return result

def clone_repository(repository_metadata: Dict, tenant_id: str, task_id: str = None) -> str:
    """
    Clone repository with proper authentication and optionally create a task-specific branch
    
    Args:
        repository_metadata: Repository details dictionary
        tenant_id: Tenant ID for SCM configuration lookup
        task_id: Optional task ID to create a task-specific branch
    
    Returns:
        str: Path to cloned repository
    """
    print("Repository metadata: ",repository_metadata)
    with open("/home/<USER>/graphnode-backend-api/clone.txt","a") as f:
                    f.write(f"Repository metadata: {repository_metadata}")
    if not repository_metadata:
        raise Exception("Repository metadata is required")
    
    service = repository_metadata.get('service', '').lower()
    repo_name = repository_metadata.get('repositoryName')
    access_token = repository_metadata.get('access_token')
    if not access_token:
        encrypted_scm_id = repository_metadata.get('encrypted_scm_id')
        decrypted_scm_id = decrypt_string(encrypted_scm_id)
        config = scm_manager.get_configuration(tenant_id, scm_id=decrypted_scm_id)
        if not config:
            raise Exception("Failed to get SCM configuration")
        access_token = config.credentials.access_token
    
    if not all([service, repo_name, access_token]):
        raise Exception("Invalid repository metadata: missing required fields")

    # Generate clone path
    clone_path = os.path.join(get_codegeneration_path(), repo_name)

    # Handle different task types with proper null checks
    if task_id:
        if task_id.startswith("cm"):
            clone_path = os.path.join(get_codegeneration_path("CodeMaintenance"), repo_name)
        elif task_id.startswith("deep-query"):
            clone_path = os.path.join(get_codegeneration_path("DocumentCreation"), repo_name)
        # Note: "cg" tasks use the default CodeGeneration path, so no special handling needed

    # Clean up existing repository if it exists
    if os.path.exists(clone_path):
        import shutil
        import time

        print(generate_timestamp(), f"🔄 Repository already exists at {clone_path}, cleaning up before clone")

        try:
            # First attempt: Use shutil.rmtree with ignore_errors=False to catch issues
            shutil.rmtree(clone_path, ignore_errors=False)
            print(generate_timestamp(), f"✅ Successfully removed existing directory: {clone_path}")

        except PermissionError as pe:
            print(generate_timestamp(), f"⚠️ Permission error removing directory, trying to fix permissions: {str(pe)}")
            try:
                # Try to fix permissions and remove again
                import subprocess
                subprocess.run(["chmod", "-R", "777", clone_path], check=True)
                shutil.rmtree(clone_path, ignore_errors=False)
                print(generate_timestamp(), f"✅ Successfully removed directory after fixing permissions: {clone_path}")
            except Exception as fix_e:
                print(generate_timestamp(), f"❌ Failed to fix permissions: {str(fix_e)}")
                raise

        except OSError as oe:
            print(generate_timestamp(), f"⚠️ OS error removing directory, trying force removal: {str(oe)}")
            try:
                # Force removal using system command
                import subprocess
                subprocess.run(["rm", "-rf", clone_path], check=True)
                print(generate_timestamp(), f"✅ Successfully force-removed directory: {clone_path}")
            except Exception as force_e:
                print(generate_timestamp(), f"❌ Force removal failed: {str(force_e)}")
                raise

        except Exception as e:
            print(generate_timestamp(), f"❌ Unexpected error removing directory: {str(e)}")
            raise

        # Verify directory is completely removed
        retry_count = 0
        max_retries = 10
        while os.path.exists(clone_path) and retry_count < max_retries:
            print(generate_timestamp(), f"⏳ Directory still exists, waiting... (attempt {retry_count + 1}/{max_retries})")
            time.sleep(1)
            retry_count += 1

            # Try one more removal attempt
            try:
                if os.path.isdir(clone_path):
                    shutil.rmtree(clone_path, ignore_errors=True)
                elif os.path.isfile(clone_path):
                    os.remove(clone_path)
            except Exception as retry_e:
                print(generate_timestamp(), f"Retry removal attempt failed: {str(retry_e)}")

        # Final verification
        if os.path.exists(clone_path):
            # Create a unique path instead of failing
            import time
            unique_suffix = str(int(time.time()))
            original_clone_path = clone_path
            clone_path = f"{clone_path}_{unique_suffix}"
            print(generate_timestamp(), f"⚠️ Could not remove existing directory {original_clone_path}, using alternative path: {clone_path}")
        else:
            print(generate_timestamp(), f"✅ Directory successfully cleaned up: {clone_path}")

    try:
        repo = None
        if service == 'github':
            # Use HTTPS URL with token authentication
            clone_url = repository_metadata.get('cloneUrlHttp')
            if not clone_url:
                raise Exception("No HTTPS clone URL found")
                
                
            # Clone with token
            auth_username = repository_metadata.get('auth_username', 'oauth2')  # Use auth_username or default to oauth2
            auth_url = clone_url.replace('https://',
                                    f'https://{auth_username}:{access_token}@')
            #if resume clone from a particular branch 
            if os.getenv("resume"):
                resume_branch = f"cga-{task_id}"
                repo = Repo.clone_from(auth_url, clone_path, branch=resume_branch)
                ## ignore filemode
                try:
                    repo.git.config("core.filemode", "false")
                except Exception as e:
                    print(generate_timestamp(), f"Warning: Could not set filemode to false: {str(e)}")

            else:
                
                with open("/home/<USER>/graphnode-backend-api/clone.txt","a") as f:
                    f.write(f"Auth url: {auth_url} \n Clone path: {clone_path}")
                repo = Repo.clone_from(auth_url, clone_path)
                try:
                    repo.git.config("core.filemode", "false")
                except Exception as e:
                    print(generate_timestamp(), f"Warning: Could not set filemode to false: {str(e)}")
                
            setup_git_credentials(clone_path, access_token, clone_url)

        elif service == 'gitlab':
            clone_url = repository_metadata.get('cloneUrlHttp')
            if not clone_url:
                raise Exception("No HTTPS clone URL found")
                
            # For GitLab, we can use token in URL directly
            auth_username = repository_metadata.get('auth_username', 'oauth2')  # Use auth_username or default to oauth2
            auth_url = clone_url.replace('https://',
                                    f'https://{auth_username}:{access_token}@')
            repo = Repo.clone_from(auth_url, clone_path)
            setup_git_credentials(clone_path, access_token, clone_url)
            
        else:
            raise Exception(f"Unsupported repository service: {service}")
        
        # Create session branches (kavia-main and optionally task branch)
        if task_id and repo:
            try:
                branch_result = create_session_branches(repo, task_id)
                print(generate_timestamp(),f"Branch operations completed: {branch_result}")
            except Exception as e:
                print(generate_timestamp(),f"Warning: Could not create session branches: {str(e)}")
            
    except git.GitCommandError as e:
        if "Permission denied" in str(e):
            raise Exception(f"Authentication failed when cloning repository") from e
        else:
            raise Exception(f"Error cloning repository: {e}") from e
            
    if not os.path.exists(clone_path):
        raise Exception("Repository cloning failed: Clone path does not exist")
    
    try:
        subprocess.run(["chmod", "-R", "777", clone_path], check=True)
    except Exception as chmod_e:
        print(generate_timestamp(),f"Warning: Could not set permissions: {str(chmod_e)}")
        
    return clone_path

def check_branch_exists(owner, repo, branch_name, access_token):
    """
    Check if a branch exists in a GitHub repository.

    Args:
        owner: Repository owner/organization name
        repo: Repository name
        branch_name: Name of the branch to check
        access_token: GitHub access token

    Returns:
        Dict containing check result
    """
    try:
        print(generate_timestamp(), f"Checking if branch '{branch_name}' exists in {owner}/{repo}")

        # Create GitHub client with provided token
        github_client = Github(access_token)

        # Get the repository
        repository = github_client.get_repo(f"{owner}/{repo}")

        # Try to get the branch
        try:
            branch = repository.get_branch(branch_name)
            print(generate_timestamp(), f"✅ Branch '{branch_name}' exists in {owner}/{repo}")
            return {
                "exists": True,
                "branch_name": branch_name,
                "sha": branch.commit.sha,
                "error": None
            }
        except Exception as branch_error:
            # Branch doesn't exist or access denied
            if "Branch not found" in str(branch_error) or "404" in str(branch_error):
                print(generate_timestamp(), f"❌ Branch '{branch_name}' does not exist in {owner}/{repo}")
                return {
                    "exists": False,
                    "branch_name": branch_name,
                    "sha": None,
                    "error": f"Branch '{branch_name}' not found"
                }
            else:
                # Some other error (permissions, etc.)
                print(generate_timestamp(), f"❌ Error checking branch '{branch_name}' in {owner}/{repo}: {str(branch_error)}")
                return {
                    "exists": False,
                    "branch_name": branch_name,
                    "sha": None,
                    "error": f"Error checking branch: {str(branch_error)}"
                }

    except Exception as e:
        print(generate_timestamp(), f"❌ Error accessing repository {owner}/{repo}: {str(e)}")
        return {
            "exists": False,
            "branch_name": branch_name,
            "sha": None,
            "error": f"Repository access error: {str(e)}"
        }


def merge_branches(owner, repo, source_branch, target_branch, access_token, commit_message=None):
    """
    Simple function to merge branches using GitHub API.
    
    Args:
        owner: Repository owner/organization name
        repo: Repository name
        source_branch: Name of the branch to merge (head branch)
        target_branch: Name of the target branch (base branch)
        access_token: GitHub access token
        commit_message: Optional commit message for the merge
        
    Returns:
        Dict containing merge result
    """
    try:
        print(generate_timestamp(), f"Merging {source_branch} -> {target_branch} in {owner}/{repo}")
        
        # Create GitHub client with provided token
        github_client = Github(access_token)
        
        # Get the repository
        repository = github_client.get_repo(f"{owner}/{repo}")
        
        # Perform the merge
        message = commit_message or f"Merge branch '{source_branch}' into '{target_branch}'"
        merge_result = repository.merge(
            base=target_branch,
            head=source_branch,
            commit_message=message
        )
        
        print(generate_timestamp(), f"Successfully merged {source_branch} into {target_branch}")

        # Check if merge_result is None (can happen when branches are already up to date)
        if merge_result is None:
            print(generate_timestamp(), f"Merge result is None - branches may already be up to date")
            return {
                "success": True,
                "merged": False,  # No actual merge needed
                "sha": None,
                "message": f"No merge needed - {source_branch} is already up to date with {target_branch}"
            }

        # For direct repository merge, merge_result is a Commit object if successful
        return {
            "success": True,
            "merged": True,  # If we reach here, merge was successful
            "sha": merge_result.sha,
            "message": message
        }
        
    except Exception as e:
        print(generate_timestamp(), f"Error merging branches: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }