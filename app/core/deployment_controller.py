from datetime import datetime
from typing import Dict, Any, Callable
import os
import json
import subprocess
from app.models.code_generation_model import Message
from app.core.constants import TASKS_COLLECTION_NAME, DEPLOYMENT_COLLECTION_NAME
from app.utils.datetime_utils import generate_timestamp
from app.core.websocket.client import WebSocketClient
from app.connection.establish_db_connection import get_mongo_db
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME
from code_generation_core_agent.agents.task_execution_agent import TaskExecutionAgent
from app.connection.task_queue import TaskQueue
import boto3
from app.core.Settings import settings
import time
import uuid
from pydantic import BaseModel
import yaml
import asyncio
from urllib.parse import urlparse
import threading
import concurrent.futures
from app.core.custom_docker_executor import DockerExecutor, DockerShellCallback
from typing import Optional
import functools
from app.utils.code_generation_utils import get_codegeneration_path
from app.connection.tenant_middleware import get_tenant_id
from app.models.organization_models import Organization
import os
# from app.core.deployment_controller import DeploymentController

def run_in_daemon_thread(func):
    """Decorator to run function in a daemon thread"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        thread = threading.Thread(
            target=func,
            args=args,
            kwargs=kwargs,
            daemon=True,
            name=f"{func.__name__}_daemon"
        )
        thread.start()
        return thread
    return wrapper

class DeploymentModel(BaseModel):
    _id: str
    deployment_id: str
    project_id: str
    task_id: str
    app_id: str
    branch_name: str
    app_url: str
    artifact_path: str
    custom_domain: str
    job_id: str
    status: str
    message: str
    command: str
    root_path: str
    build_path: str
    created_at: datetime
    updated_at: datetime
    subdomain: str = ""  # Added subdomain field with default empty string
    project_name: str = ""  # Added project_name field with default empty string
    tenant_id: str = ""
    tenant_name: str = ""



# class DeploymentCommandModel(BaseModel):



class DeploymentController:
    
    class BuildOutputCallback(DockerShellCallback):
        """Custom callback for streaming build output"""
        
        def __init__(self, send_message_func, task_queue, task_id):
            self.send_message = send_message_func
            self.task_queue = task_queue
            self.task_id = task_id
            self.exit_code = None
            self.build_completed = False
            
        def on_output(self, output):
            """Handle live output from Docker container"""
            if output and output.strip():
                # Send each line of output as it comes
                lines = output.decode('utf-8') if isinstance(output, bytes) else str(output)
                for line in lines.strip().split('\n'):
                    if line.strip():
                        # Check for build completion indicators
                        if "Build completed successfully" in line:
                            self.build_completed = True
                            formatted_line = f"✅ {line.strip()}"
                        elif "npm ERR!" in line or "Error:" in line:
                            formatted_line = f"❌ {line.strip()}"
                        elif "npm WARN" in line or "warning" in line.lower():
                            formatted_line = f"⚠️ {line.strip()}"
                        else:
                            formatted_line = f"🔨 {line.strip()}"
                        
                        self.send_message(formatted_line)
                        
                        # Enqueue live build log to TaskQueue
                        try:
                            self.task_queue.enqueue(self.task_id, {
                                "type": "deployment_queue",
                                "event_type": "build_log",
                                "message": formatted_line,
                                "timestamp": generate_timestamp(),
                                "log_level": "error" if "❌" in formatted_line else "warning" if "⚠️" in formatted_line else "info"
                            })
                        except Exception as e:
                            print(f"Failed to enqueue build log: {str(e)}")
                        
        def on_exit(self, return_code):
            """Handle process completion"""
            self.exit_code = return_code
            completion_message = ""
            
            if self.build_completed:
                completion_message = "✅ Build process completed successfully!"
            else:
                completion_message = "⚠️ Build process completed but no completion message detected"
            self.send_message(completion_message)
            
            # Enqueue build completion success to TaskQueue
            try:
                self.task_queue.enqueue(self.task_id, {
                    "type": "deployment_queue",
                    "event_type": "build_completed",
                    "status": "success",
                    "message": completion_message,
                    "exit_code": return_code,
                    "build_completed": self.build_completed,
                    "timestamp": generate_timestamp()
                })
            except Exception as e:
                print(f"Failed to enqueue build completion: {str(e)}")
            
    
    def __init__(self, task_id,agent, ws_client, db):
        self.task_id = task_id
        self.ws_client = ws_client
        self.db = db
        self.kaviarootdb = get_mongo_db(KAVIA_ROOT_DB_NAME).db
        self.task_queue = TaskQueue()  # Initialize TaskQueue
        self._command_map = self._initialize_command_map()
        self.agent: TaskExecutionAgent = agent
        
    def _initialize_command_map(self) -> Dict[str, Callable]:
        """Initialize mapping of command names to their handler methods"""
        return {
            'list_dir': self._handle_list_dir,
            'start_deploy': self._handle_start_deploy,
            'deployment_status': self._handle_deployment_status,
            'manifest': self._handle_manifest,
            'save_configuration': self._handle_save_configuration
        }
    



    def _run_build_and_deploy(self, deployment_id: str, deployment_type: str, build_command: str, root_path: str, data: Dict[str, Any]):
        """Run build and deployment process with retry only for missing build path"""
        
        max_retries = 3
        build_path = None
        main_loop = None
        
        try:
            # Create main event loop for this thread
            main_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(main_loop)
            
            # Retry only until we find a build path
            for attempt in range(max_retries):
                try:
                    self._send_message(f"🔄 Attempt {attempt + 1} of {max_retries}")
                    
                    # Run build
                    self._send_message("🔨 Starting build process...")
                    main_loop.run_until_complete(
                        self.build(deployment_id, deployment_type, build_command, root_path)
                    )
                    
                    # Check for build path inside container
                    build_path = self.get_build_path(root_path)
                    
                    if build_path:
                        self._send_message(f"✅ Build path found: {build_path}")
                        break  # Exit retry loop - we found the build path
                    else:
                        self._send_message(f"❌ No build path found on attempt {attempt + 1}")
                        if attempt < max_retries - 1:
                            wait_time = 5 + (attempt * 2)
                            self._send_message(f"⏳ Waiting {wait_time} seconds before retry...")
                            time.sleep(wait_time)
                            
                except Exception as e:
                    self._send_message(f"❌ Build error on attempt {attempt + 1}: {str(e)}")
                    if attempt < max_retries - 1:
                        wait_time = 5 + (attempt * 2)
                        time.sleep(wait_time)
            
            # After retry loop - proceed with deployment if build path exists
            if build_path:
                try:
                    self._send_message("🚀 Starting deployment...")
                    data["build_path"] = build_path
                    
                    deployment_result = main_loop.run_until_complete(
                        self.handle_deploy_artifact(build_path, self.ws_client, data)
                    )
                    self._send_success_message(deployment_id, deployment_result, build_path, root_path)
                    
                except Exception as e:
                    self._send_failure_message(deployment_id, f"Deployment failed: {str(e)}")
            else:
                self._send_failure_message(deployment_id, f"No build path found after {max_retries} attempts")
                
        finally:
            # Clean up the main event loop
            if main_loop:
                try:
                    main_loop.close()
                except:
                    pass

    def _send_success_message(self, deployment_id: str, deployment_result: dict, build_path: str, root_path: str):
        """Send success notification"""
        app_url = deployment_result.get('app_url', '')
        
        self._send_message(f"🎉 Deployment successful! App is live at {app_url}")
        
        self.ws_client.send_message("deployment_complete", {
            "id": deployment_id,
            "status": "success",
            "message": f"🎉 Deployment completed successfully!",
            "app_url": app_url,
            "subdomain": deployment_result.get('subdomain', ''),
            "build_path": build_path,
            "root_path": root_path
        })

    def _send_failure_message(self, deployment_id: str, error_msg: str):
        """Send failure notification"""
        self._send_message(f"❌ {error_msg}")
        
        # Update database
        self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
            {"_id": deployment_id},
            {"$set": {
                "status": "failed",
                "message": error_msg,
                "updated_at": datetime.now()
            }}
        )
        
        # Send WebSocket message
        self.ws_client.send_message("deployment_complete", {
            "id": deployment_id,
            "status": "failed",
            "message": f"❌ {error_msg}",
            "error": error_msg
        })

    def get_build_path(self, root_path: str) -> Optional[str]:
        """
        Check for common build directories (build, dist, out, Angular, Next.js) in the root path.
        Only returns the path if it contains an index.html file.
        
        Returns:
            Optional[str]: Path to first found build directory containing index.html, or None
        """
        if not os.path.exists(root_path) or not os.path.isdir(root_path):
            raise ValueError(f"Invalid root path: {root_path}")
        
        # Check common build directories including Angular and Next.js
        build_dirs = ['build', 'dist', 'out', 'dist/angular/browser', '.next/out']
        
        for build_dir in build_dirs:
            build_path = os.path.join(root_path, build_dir)
            self._send_message(f"Checking for build directory: {build_path}")
            if os.path.exists(build_path) and os.path.isdir(build_path):
                # Check if index.html exists in the build directory
                index_path = os.path.join(build_path, 'index.html')
                if os.path.exists(index_path) and os.path.isfile(index_path):
                    self._send_message(f"Found build directory with index.html: {build_path}")
                    return os.path.abspath(build_path)
                else:
                    self._send_message(f"Build directory found but no index.html: {build_path}")

            time.sleep(2)
        
        self._send_message(f"No build directory with index.html found in {root_path}")
        return None

    # def _validate_build_artifacts(self, root_path: str, deployment_type: str) -> dict:
    #     """
    #     Validate that build artifacts were actually created.
        
    #     Args:
    #         root_path (str): The root directory path where build should have occurred
    #         deployment_type (str): Type of deployment (react_app, etc.)
            
    #     Returns:
    #         dict: Validation result with 'valid' boolean and 'message' string
    #     """
    #     try:
    #         # Get the build directory path
    #         build_path = self.get_build_path(root_path)
            
    #         if not build_path:
    #             return {
    #                 "valid": False,
    #                 "message": "No build directory found (build/, dist/, or out/)",
    #                 "artifact_count": 0
    #             }
            
    #         self._send_message(f"🔍 Validating build artifacts in: {build_path}")
            
    #         # Count files in build directory and collect critical files
    #         file_count = 0
    #         critical_files = []
    #         all_files = []
    #         has_index_html = False
    #         index_html_locations = []
            
    #         # Walk through all files in the build directory
    #         for root, dirs, files in os.walk(build_path):
    #             file_count += len(files)
                
    #             # Log the directory being scanned
    #             rel_root = os.path.relpath(root, build_path)
    #             if rel_root == '.':
    #                 self._send_message(f"📁 Scanning root of build directory:")
    #             else:
    #                 self._send_message(f"📁 Scanning subdirectory: {rel_root}")
                
    #             self._send_message(f"   Files found: {files}")
                
    #             # Check for critical files based on deployment type
    #             for file in files:
    #                 file_rel_path = os.path.join(rel_root, file) if rel_root != '.' else file
    #                 all_files.append(file_rel_path)
                    
    #                 # Check for index.html (case insensitive)
    #                 if file.lower() == 'index.html':
    #                     has_index_html = True
    #                     index_location = os.path.join(root, file)
    #                     index_html_locations.append(index_location)
    #                     critical_files.append(f'index.html ({file_rel_path})')
    #                     self._send_message(f"✅ Found index.html: {file} at {index_location}")
                        
    #                 # Check for other critical files
    #                 elif file.endswith('.js') and ('main' in file.lower() or 'index' in file.lower() or 'app' in file.lower() or 'chunk' in file.lower()):
    #                     critical_files.append(f'js: {file}')
    #                     self._send_message(f"✅ Found JS file: {file}")
    #                 elif file.endswith('.css') and ('main' in file.lower() or 'index' in file.lower() or 'app' in file.lower()):
    #                     critical_files.append(f'css: {file}')
    #                     self._send_message(f"✅ Found CSS file: {file}")
    #                 elif file == 'manifest.json':
    #                     critical_files.append('manifest.json')
    #                     self._send_message(f"✅ Found manifest.json")
    #                 elif file.endswith('.html') and file.lower() != 'index.html':
    #                     critical_files.append(f'html: {file}')
    #                     self._send_message(f"✅ Found HTML file: {file}")
            
    #         self._send_message(f"📊 Validation Summary:")
    #         self._send_message(f"   Total files: {file_count}")
    #         self._send_message(f"   Has index.html: {has_index_html}")
    #         if has_index_html:
    #             self._send_message(f"   index.html locations: {index_html_locations}")
    #         self._send_message(f"   Critical files found: {critical_files}")
    #         self._send_message(f"   Sample files: {all_files[:15]}")
            
    #         # Enhanced validation rules based on deployment type
    #         deployment_type_lower = deployment_type.lower()
            
    #         # For web/frontend frameworks, be more flexible about index.html requirement
    #         if deployment_type_lower in ['react_app', 'web', 'frontend', 'nextjs', 'angular', 'vue', 'react', 'next']:
                
    #             # Check for minimum file count first
    #             if file_count < 1:
    #                 self._send_message(f"❌ Validation failed: no files found in build directory")
    #                 return {
    #                     "valid": False,
    #                     "message": "No files found in build output directory",
    #                     "artifact_count": file_count,
    #                     "critical_files": critical_files
    #                 }
                
    #             # For modern frameworks, check for index.html or other HTML files
    #             html_files = [f for f in all_files if f.lower().endswith('.html')]
    #             has_any_html = len(html_files) > 0
                
    #             if not has_index_html and not has_any_html:
    #                 # Check if this might be an API/backend build (no HTML needed)
    #                 js_files = [f for f in all_files if f.endswith('.js')]
    #                 if len(js_files) > 0:
    #                     self._send_message(f"⚠️ No HTML files found, but found {len(js_files)} JS files - might be API/backend build")
    #                     # Allow API builds to pass without HTML
    #                     return {
    #                         "valid": True,
    #                         "message": f"Build validation successful for API/backend: {file_count} files found",
    #                         "artifact_count": file_count,
    #                         "critical_files": critical_files,
    #                         "build_path": build_path,
    #                         "has_index_html": False,
    #                         "build_type": "api_backend"
    #                     }
    #                 else:
    #                     self._send_message(f"❌ Validation failed: no HTML files found in frontend build")
    #                     self._send_message(f"   All files checked: {all_files}")
    #                     return {
    #                         "valid": False,
    #                         "message": "Critical files missing: no HTML files found in frontend build output",
    #                         "artifact_count": file_count,
    #                         "critical_files": critical_files,
    #                         "all_files": all_files[:20]  # Include sample for debugging
    #                     }
                
    #             # If we have HTML files but no index.html, warn but allow
    #             if not has_index_html and has_any_html:
    #                 self._send_message(f"⚠️ No index.html found, but found other HTML files: {html_files}")
    #                 self._send_message(f"   This might work but index.html is recommended for web apps")
                
    #         # If we get here, validation passed
    #         build_type = "frontend" if has_index_html or has_any_html else "api_backend"
    #         self._send_message(f"✅ Build validation successful! (type: {build_type})")
            
    #         return {
    #             "valid": True,
    #             "message": f"Build validation successful: {file_count} files, critical files: {critical_files}",
    #             "artifact_count": file_count,
    #             "critical_files": critical_files,
    #             "build_path": build_path,
    #             "has_index_html": has_index_html,
    #             "build_type": build_type,
    #             "html_files": html_files if 'html_files' in locals() else []
    #         }
            
    #     except Exception as e:
    #         error_msg = f"Build validation error: {str(e)}"
    #         self._send_message(f"❌ {error_msg}")
    #         return {
    #             "valid": False,
    #             "message": error_msg,
    #             "artifact_count": 0
    #         }

    def _cleanup_node_modules_if_needed(self, root_path: str):
        """
        Clean up problematic node_modules directories before build.
        This helps prevent ENOTEMPTY errors during npm install.
        """
        try:
            node_modules_path = os.path.join(root_path, 'node_modules')
            
            if os.path.exists(node_modules_path):
                self._send_message("🧹 Cleaning up existing node_modules to prevent installation conflicts...")
                
                # Use system rm -rf which is more reliable than Python's rmtree for this case
                import subprocess
                result = subprocess.run(['rm', '-rf', node_modules_path], 
                                     capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    self._send_message("✅ node_modules cleanup completed")
                else:
                    self._send_message(f"⚠️ node_modules cleanup warning: {result.stderr}")
                    
        except Exception as e:
            self._send_message(f"⚠️ node_modules cleanup failed (continuing anyway): {str(e)}")

    async def build(self, deployment_id: str, deployment_type: str, build_command: str, root_path: str):
        self._send_message(f"Building deployment {deployment_id} with type {deployment_type} and command {build_command} and root path {root_path}")
        
        try:
            result = await self._execute_build(deployment_id, deployment_type, build_command, root_path)
            
            if result.get('status') == 'success':
                self._send_message(f"✅ Build completed successfully!")
            else:
                self._send_message(f"❌ Build failed: {result.get('message', 'Unknown error')}")
            
            return result
            
        except Exception as e:
            error_msg = f"Build process failed: {str(e)}"
            self._send_message(f"❌ {error_msg}")
            
            # Update deployment status in database for failure
            try:
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                    {"_id": deployment_id},
                    {"$set": {
                        "status": "build_failed",
                        "message": f"Build failed: {str(e)} at {current_time}",
                        "updated_at": datetime.now()
                    }}
                )
            except Exception as db_error:
                self._send_message(f"❌ Failed to update database: {str(db_error)}")
            
            return {
                "status": "failed",
                "message": f"Build failed: {str(e)}",
                "return_code": -1
            }
        
        finally:
            # Clean up Docker executor if needed
            try:
                if 'docker_executor' in locals():
                    self.agent.executor.cleanup()
            except Exception as cleanup_error:
                self._send_message(f"Warning: Docker executor cleanup failed: {str(cleanup_error)}")

    async def _execute_build(self, deployment_id: str, deployment_type: str, build_command: str, root_path: str):
        """Execute the build process once"""
        try:
            # Initialize Docker executor
            self._send_message(f"Initializing Docker executor for build process...")
            docker_executor = self.agent.executor
            
            # Validate root path
            if not os.path.exists(root_path):
                raise FileNotFoundError(f"Root path does not exist: {root_path}")
            
            self._send_message(f"Starting build process in directory: {root_path}")
            self._send_message(f"Executing build command: {build_command}")
            
            # Split the build command to handle each step separately
            build_steps = [step.strip() for step in build_command.split('&&')]
            
            # Execute each step separately for better error tracking
            for i, step in enumerate(build_steps):
                step_name = f"Step {i+1}: {step}"
                self._send_message(f"🔨 Executing {step_name}")
                
                # Create enhanced command with explicit error handling
                enhanced_step_command = f"{step}"
                
                # Create custom callback for streaming output
                build_callback = self.BuildOutputCallback(self._send_message, self.task_queue, self.task_id)
                
                # Execute the step
                subprocess = await docker_executor.create_subprocess_shell(
                    cmd=enhanced_step_command,
                    callback=build_callback,
                    work_directory=root_path
                )
                
                # Set timeout for each step
                timeout = 1800  # 30 minutes per step
                
                try:
                    # Handle subprocess.wait() return value properly
                    wait_result = await subprocess.wait(timeout=timeout)
                    
                    # subprocess.wait() now should always return a proper tuple from DockerSubprocess
                    if isinstance(wait_result, tuple) and len(wait_result) >= 2:
                        return_code, output = wait_result[0], wait_result[1]
                    elif isinstance(wait_result, tuple) and len(wait_result) == 1:
                        return_code = wait_result[0]
                        output = ""
                    elif isinstance(wait_result, (int, float)):
                        return_code = int(wait_result)
                        output = ""
                    else:
                        # Fallback for any unexpected case
                        return_code = 0
                        output = ""
                        self._send_message(f"⚠️ Unexpected wait_result format, assuming success")
                        
                except asyncio.TimeoutError:
                    self._send_message(f"❌ {step_name} timed out after {timeout} seconds")
                    await subprocess.terminate()
                    raise Exception(f"{step_name} timed out after {timeout} seconds")
                except Exception as step_error:
                    # Catch any other exceptions during this step
                    self._send_message(f"❌ Unexpected error during {step_name}: {str(step_error)}")
                    # Log the full error for debugging
                    import traceback
                    self._send_message(f"❌ Error details: {traceback.format_exc()}")
                    return_code = -1
                    output = ""
                
                # Check if this step failed
                # Fix: Use return_code directly as it's the authoritative source
                # The callback's exit_code might not be set yet due to async timing
                step_exit_code = return_code
                
                if step_exit_code != 0:
                    error_msg = f"Build step failed: {step} (exit code: {step_exit_code})"
                    self._send_message(f"❌ {error_msg}")
                    
                    # Update database status to "build_failed" immediately
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    existing_deployment = self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].find_one({"_id": deployment_id})
                    created_at = existing_deployment.get("created_at", datetime.now()) if existing_deployment else datetime.now()

                    self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                        {"_id": deployment_id},
                        {"$set": {
                            "status": "build_failed",
                            "message": f"{error_msg} at {current_time}",
                            "build_path": root_path,
                            "failed_step": step,
                            "created_at": created_at,
                            "updated_at": datetime.now()
                        }}
                    )
                    
                    return {
                        "status": "failed",
                        "message": error_msg,
                        "return_code": step_exit_code,
                        "failed_step": step
                    }
                
                self._send_message(f"✅ {step_name} completed successfully")
            
            # After all steps complete, validate build artifacts were created
            # build_validation_result = self._validate_build_artifacts(root_path, deployment_type)
            
            # if not build_validation_result["valid"]:
            #     error_msg = f"Build validation failed: {build_validation_result['message']}"
            #     self._send_message(f"❌ {error_msg}")
                
            #     # Update database status to "build_validation_failed" immediately
            #     current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            #     existing_deployment = self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].find_one({"_id": deployment_id})
            #     created_at = existing_deployment.get("created_at", datetime.now()) if existing_deployment else datetime.now()

            #     self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
            #         {"_id": deployment_id},
            #         {"$set": {
            #             "status": "build_validation_failed",
            #             "message": f"{error_msg} at {current_time}",
            #             "build_path": root_path,
            #             "created_at": created_at,
            #             "updated_at": datetime.now()
            #         }}
            #     )
                
            #     return {
            #         "status": "failed",
            #         "message": error_msg,
            #         "return_code": -1
            #     }
            
            # All steps completed and validation passed
            self._send_message("🎉 Build completed successfully!")
            self._send_message(f"✅ Build artifacts validated:  files found")
            
            # Enqueue final build success to TaskQueue
            try:
                self.task_queue.enqueue(self.task_id, {
                    "type": "deployment_queue",
                    "event_type": "build_final_status",
                    "deployment_id": deployment_id,
                    "status": "success",
                    "message": f"Build completed successfully",
                    "build_path": root_path,
                    "artifact_count": 0,
                    "return_code": 0,
                    "timestamp": generate_timestamp()
                })
            except Exception as e:
                print(f"Failed to enqueue final build success: {str(e)}")
            
            # Update deployment status in database
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            existing_deployment = self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].find_one({"_id": deployment_id})
            created_at = existing_deployment.get("created_at", datetime.now()) if existing_deployment else datetime.now()
            
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": deployment_id},
                {"$set": {
                    "status": "build_success",
                    "message": f"Build completed successfully with artifacts at {current_time}",
                    "build_path": root_path,
                    "artifact_count": 0,
                    "created_at": created_at,
                    "updated_at": datetime.now()
                }}
            )
            
            return {
                "status": "success",
                "message": "Build completed successfully",
                "return_code": 0,
                "artifact_count": 0
            }
                
        except Exception as e:
            error_msg = f"Build process failed: {str(e)}"
            self._send_message(f"❌ {error_msg}")

            # Update database status to "build_failed" immediately
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": deployment_id},
                {"$set": {
                    "status": "build_failed",
                    "message": f"{error_msg} at {current_time}",
                    "updated_at": datetime.now()
                }}
            )

            return {
                "status": "failed",
                "message": error_msg,
                "return_code": -1
            }
        
        finally:
            # Clean up Docker executor if needed
            try:
                if 'docker_executor' in locals():
                    docker_executor.cleanup()
            except Exception as cleanup_error:
                self._send_message(f"Warning: Docker executor cleanup failed: {str(cleanup_error)}")

    def _get_tenant_name_sync(self, tenant_id: str) -> str:
        """
        Synchronously get tenant name by creating event loop for async call
        """
        try:
            # Create new event loop for this sync function
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Run the async organization get call
            org_data = loop.run_until_complete(Organization.get(tenant_id))
            if org_data:
                org = Organization(**org_data)
                return str(org.name)
            else:
                return "Unknown"
                
        except Exception as e:
            print(f"Error getting tenant name: {str(e)}")
            return "Unknown"
        finally:
            # Clean up the event loop
            try:
                loop.close()
            except:
                pass

    @run_in_daemon_thread
    def _handle_start_deploy(self, data: Dict[str, Any]):
        """Handle start deployment request - runs in daemon thread"""
        print(f"Handling start deployment command with input: {data}")
        if not data.get("id", ""):
            data["id"] = str(uuid.uuid4())[:8]
        
        # Handle root_path normalization globally (regardless of branch_name)
        original_root_path = data.get("root_path", "")
        root_path = original_root_path
        
        if root_path.startswith("/"):
            # Check if path already starts with workspace directories to avoid double-prepending
            if not root_path.startswith(get_codegeneration_path()):
                # Strip leading slash to avoid double slashes in concatenation
                path_suffix = root_path.lstrip("/")
                root_path = os.path.join(get_codegeneration_path(), path_suffix)
                # Update data immediately after normalization
                data["root_path"] = root_path
                self._send_message(f"🔧 Normalized root_path: '{original_root_path}' → '{root_path}'")
        
        if not root_path:
            # Fallback to getting root directory if not provided in data
            root_path = self._get_root_directory()
            data["root_path"] = root_path
            self._send_message(f"🔧 Using fallback root_path: '{root_path}'")
        
        # Ensure data is always updated with final root_path
        data["root_path"] = root_path
        
        # Ensure we have a valid branch name
        branch_name = data.get("branch_name", "")
        if not branch_name or len(branch_name.strip()) == 0:
            # Try to get current branch from git instead of defaulting
            branch_name = self._get_current_branch_name(root_path)
            data["branch_name"] = branch_name
            self._send_message(f"Auto-detected branch name: '{branch_name}' from path: {root_path}")
        else:
            self._send_message(f"Using provided branch name: '{branch_name}'")
        
        self._send_message("Deployment started")
        
        # Get task details first to include project_id in data
        task_details = self.db[TASKS_COLLECTION_NAME].find_one({"_id": self.task_id})

        
        
        # Convert project_id to string if it's not already
        project_id = task_details.get("project_id", "")
        if not isinstance(project_id, str):
            project_id = str(project_id)
        
        # Add project_id and other task details to data
        data["project_id"] = project_id
        
        # 🔍 CHECK FOR EXISTING APP_ID IN MONGODB
        existing_deployment = None
        existing_app_id = ""
        existing_app_url = ""
        
        # Check if there's already a deployment record with the same project_id
        if project_id:
            existing_deployment = self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].find_one({
                "project_id": project_id,
                "app_id": {"$exists": True, "$ne": ""}
            }, sort=[("created_at", -1)])  # Get the most recent one
            
            if existing_deployment:
                existing_app_id = existing_deployment.get("app_id", "")
                existing_app_url = existing_deployment.get("app_url", "")
                existing_custom_domain = existing_deployment.get("custom_domain", "")
                existing_subdomain = existing_deployment.get("subdomain", "")
                existing_project_name = existing_deployment.get("project_name", "")
                
                self._send_message(f"🔄 Found existing deployment for project_id: {project_id}")
                self._send_message(f"   • Existing app_id: {existing_app_id}")
                self._send_message(f"   • Existing app_url: {existing_app_url}")
                
                if existing_custom_domain:
                    self._send_message(f"   • Existing custom domain: {existing_custom_domain}")
                
                if existing_subdomain:
                    self._send_message(f"   • Existing subdomain: {existing_subdomain}")
                    
                if existing_project_name:
                    self._send_message(f"   • Existing project name: {existing_project_name}")
                    
                # Add these values to data to preserve them
                if existing_subdomain:
                    data["subdomain"] = existing_subdomain
                    
                if existing_project_name:
                    data["project_name"] = existing_project_name
                    
                if existing_custom_domain:
                    data["custom_domain"] = existing_custom_domain
        
        # Use existing app_id and app_url if found, otherwise use task details or data
        if existing_app_id:
            final_app_id = existing_app_id
            final_app_url = existing_app_url
            self._send_message(f"✅ Reusing existing app_id: {final_app_id}")
            self._send_message(f"✅ Reusing existing app_url: {final_app_url}")
        else:
            # Use app_id from task details or data as fallback
            final_app_id = str(task_details.get("app_id", "") or data.get("app_id", ""))
            final_app_url = str(data.get("app_url", ""))
            self._send_message(f"🆕 Using new app_id: {final_app_id}")
            self._send_message(f"🆕 Using new app_url: {final_app_url}")
        
        # Set the final values in data
        data["app_id"] = final_app_id
        data["app_url"] = final_app_url
        
        # ✅ CREATE INITIAL DEPLOYMENT RECORD HERE (before build starts)
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        created_at = datetime.now()
        tenant_id = get_tenant_id()
        deployment_model = DeploymentModel(
            _id=str(data.get("id", "")),
            deployment_id=str(data.get("id", "")),
            project_id=str(project_id),  # Use the converted string project_id
            task_id=str(self.task_id),
            app_id=str(final_app_id),  # Use the final determined app_id
            branch_name=str(branch_name),  # Use validated branch name
            app_url=str(final_app_url),  # Use the final determined app_url
            custom_domain=str(data.get("custom_domain", "")),  # Use existing custom_domain if available
            artifact_path=str(data.get("artifact_path", "")),
            command=str(data.get("command", "")),
            root_path=str(data.get("root_path", "")),
            build_path="",  # Initialize with empty string
            job_id="",  # Initialize with empty string
            status="processing",  # Initial status
            message=f"Deployment started at {current_time}",
            created_at=created_at,
            updated_at=datetime.now(),
            tenant_id=str(tenant_id),
            tenant_name=self._get_tenant_name_sync(tenant_id),
            subdomain=str(data.get("subdomain", "")),  # Use existing subdomain if available
            project_name=str(data.get("project_name", ""))  # Use existing project_name if available
        )   
        
        # Log what we're storing in the database
        self._send_message(f"Storing initial deployment record with:")
        self._send_message(f"   • branch_name: {branch_name}")
        self._send_message(f"   • app_id: {final_app_id}")
        self._send_message(f"   • app_url: {final_app_url}")
        self._send_message(f"   • project_id: {project_id}")
        
        # Insert initial document in database
        self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
            {"_id": data.get("id", "")},
            {"$set": deployment_model.model_dump()},
            upsert=True
        )
        
        # Start build process in this daemon thread
        self._send_message("🚀 Starting build process...")
        self._send_message(f"🔧 Using project_id: {project_id}")
        
        # Enqueue build started event to TaskQueue
        try:
            self.task_queue.enqueue(self.task_id, {
                "type": "deployment_queue",
                "event_type": "build_started",
                "deployment_id": data.get("id", ""),
                "deployment_type": data.get("deployment_type", "web"),
                "build_command": data.get("command", ""),
                "root_path": data.get("root_path", ""),
                "project_id": project_id,
                "branch_name": branch_name,
                "message": "🚀 Build process started",
                "timestamp": generate_timestamp()
            })
        except Exception as e:
            self._send_message(f"Warning: Failed to enqueue build started event: {str(e)}")
        
        self._run_build_and_deploy(
            deployment_id=data.get("id", ""), 
            deployment_type=data.get("deployment_type", "web"), 
            build_command=data.get("command", ""), 
            root_path=data.get("root_path", ""),
            data=data
        )
        
        # ✅ REMOVED: No longer overwriting the status at the end!

    @run_in_daemon_thread
    def _handle_deployment_status(self, data: Dict[str, Any]):
        """Handle deployment status request - runs in daemon thread"""
        print(f"Handling deployment status command with input: {data}")
        # Placeholder for actual deployment status logic
        self._send_message("Deployment status updated")
        self.ws_client.send_message("deployment_status", data)
        
        status = data.get("status", "").lower()
        # Build success
        if status == "success":
            self._send_message("Deployment completed successfully")
            data["status"] = "deploying"
            self.ws_client.send_message("deployment_status", data)
            # Create event loop for async call since we're in a sync daemon thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(
                    self.handle_deploy_artifact(data.get("build_path", ""), self.ws_client, data)
                )
            finally:
                loop.close()
        else:
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": data.get("id", "")},
                {"$set": {
                    "status": status, 
                    "message": data.get("message", ""),
                    "build_path": data.get("build_path", ""),
                    "updated_at": datetime.now()
                }}
            )
    
    def handle_command(self, command: str, input_data: Dict[str, Any] = None) -> bool:
        """Dynamically handle deployment commands - handlers run in daemon threads"""
        print(f"Handling deployment command: {command} with input: {input_data}")
        handler = self._command_map.get(command)
        if handler:
            # Handler is decorated with @run_in_daemon_thread, so it returns immediately
            # and runs in background. Any exceptions are handled within the daemon thread.
            handler(input_data or {})
            return True
        return False

    def _send_message(self, content: str):
        """Helper method to send messages through websocket"""
        # Truncate content if it exceeds 2500 characters
        if len(content) > 2500:
            content = content[:2497] + "..."
            
        message_obj = Message(
            content=content,
            sender="Deployment",
            timestamp=generate_timestamp()
        )
        print(message_obj)
        if self.ws_client is not None:
            self.ws_client.send_message("deployment_output", message_obj.to_dict())

        # Note: Removed database updates to prevent performance issues
        # All deployment logs are already stored via TaskQueue and deployment status updates
    
    def _get_root_directory(self):
        """Get the root workspace directory for the current task"""
        return get_codegeneration_path()
    
    @run_in_daemon_thread
    def _handle_list_dir(self, input_data: Dict[str, Any]):
        """Handle listing directory contents - runs in daemon thread"""
        try:
            base_dir = input_data.get("base_dir", "")
            
            # If base_dir is "root_folder", use the root directory
            if base_dir == "root_folder":
                dir_path = self._get_root_directory()
            else:
                # Otherwise, join with the root directory if it's not an absolute path
                if os.path.isabs(base_dir):
                    dir_path = base_dir
                else:
                    dir_path = os.path.join(self._get_root_directory(), base_dir)
            
            # Check if directory exists
            if not os.path.exists(dir_path):
                self._send_message(f"Directory not found: {dir_path}")
                self.ws_client.send_message("dir_list", {"error": f"Directory not found: {dir_path}"})
                return
            
            # List files and directories
            dir_contents = []
            for item in os.listdir(dir_path):
                item_path = os.path.join(dir_path, item)
                item_type = "folder" if os.path.isdir(item_path) else "file"
                dir_contents.append({"name": item, "type": item_type})
            
            # Send directory listing to client
            self.ws_client.send_message("dir_list", {
                "path": dir_path,
                "contents": dir_contents
            })
            
            # Format message for display
            message = f"**Directory Listing for {dir_path}:**\n\n"
            for item in dir_contents:
                icon = "📁" if item["type"] == "folder" else "📄"
                message += f"{icon} {item['name']}\n"
            
            self._send_message(message)
        except Exception as e:
            error_message = f"Error listing directory: {str(e)}"
            self._send_message(error_message)
            self.ws_client.send_message("dir_list", {"error": error_message}) 
    
    @run_in_daemon_thread
    def _handle_manifest(self, input_data: Dict[str, Any]):
        """Handle manifest reading and parsing - runs in daemon thread"""
        try:
            # Get the root directory
            root_dir = self._get_root_directory()
            manifest_path = os.path.join(root_dir, '.project_manifest.yaml')
            
            self._send_message(f"Reading manifest file from: {manifest_path}")
            
            # Check if manifest file exists
            if not os.path.exists(manifest_path):
                error_msg = f"Manifest file not found: {manifest_path}"
                self._send_message(error_msg)
                self.ws_client.send_message("manifest", {"error": error_msg})
                return
            
            # Read and parse the YAML manifest file
            with open(manifest_path, 'r', encoding='utf-8') as file:
                manifest_data = yaml.safe_load(file)
            
            if not manifest_data or 'containers' not in manifest_data:
                error_msg = "Invalid manifest format: 'containers' section not found"
                self._send_message(error_msg)
                self.ws_client.send_message("manifest", {"error": error_msg})
                return
            
            # Parse containers and format response
            response = {}
            containers = manifest_data.get('containers', [])
            
            for container in containers:
                container_name = container.get('container_name', '')
                container_type = container.get('container_type', '')
                workspace = container.get('workspace', '')
                
                if not container_name or not container_type:
                    self._send_message(f"Skipping container with missing name or type: {container}")
                    continue
                
                base_path = f'{workspace}/{container_name}'
                
                container_info = {
                    "root_path": root_dir,
                    "base_path": base_path,
                    "container_name": container_name,
                    "framework": container.get('framework', ''),
                    "type": container_type,
                    "ports": container.get('ports', ''),
                    "buildCommand": container.get('buildCommand', ''),
                    "startCommand": container.get('startCommand', ''),
                    "lintCommand": container.get('lintCommand', ''),
                    "env": container.get('env', {}),
                    "container_details": container.get('container_details', {})
                }
                
                # Add to response under the container type
                if container_type in response:
                    # If multiple containers of same type, convert to list or handle as needed
                    if not isinstance(response[container_type], list):
                        response[container_type] = [response[container_type]]
                    response[container_type].append(container_info)
                else:
                    response[container_type] = container_info
            
            # Send successful response 
            self._send_message(f"Successfully parsed manifest with {len(containers)} containers")
            
            # Add project info directly to response
            response["project_name"] = manifest_data.get('overview', {}).get('project_name', '')
            response["description"] = manifest_data.get('overview', {}).get('description', '')
            
            self.ws_client.send_message("manifest", response)
            
        except yaml.YAMLError as e:
            error_msg = f"Error parsing YAML manifest: {str(e)}"
            self._send_message(error_msg)
            self.ws_client.send_message("manifest", {"error": error_msg})
        except Exception as e:
            error_msg = f"Error reading manifest: {str(e)}"
            self._send_message(error_msg)
            self.ws_client.send_message("manifest", {"error": error_msg})
    
    def _handle_save_configuration(self, input_data: Dict[str, Any]):
        """Handle saving environment configuration to manifest file"""
        try:
            # Get the root directory
            root_dir = self._get_root_directory()
            manifest_path = os.path.join(root_dir, '.project_manifest.yaml')
            
            self._send_message(f"Reading manifest file from: {manifest_path}")
            
            # Check if manifest file exists
            if not os.path.exists(manifest_path):
                error_msg = f"Manifest file not found: {manifest_path}"
                self._send_message(error_msg)
                self.ws_client.send_message("save_configuration", {"error": error_msg})
                return
            
            # Read and parse the YAML manifest file
            with open(manifest_path, 'r', encoding='utf-8') as file:
                manifest_data = yaml.safe_load(file)
            
            if not manifest_data or 'containers' not in manifest_data:
                error_msg = "Invalid manifest format: 'containers' section not found"
                self._send_message(error_msg)
                self.ws_client.send_message("save_configuration", {"error": error_msg})
                return
            
            # Get environment data from input
            env_data = input_data.get('env_data', {})
            container_name = input_data.get('container_name', '')
            
            if not env_data:
                error_msg = "No environment data provided"
                self._send_message(error_msg)
                self.ws_client.send_message("save_configuration", {"error": error_msg})
                return
            
            if not container_name:
                error_msg = "No container name provided"
                self._send_message(error_msg)
                self.ws_client.send_message("save_configuration", {"error": error_msg})
                return
            
            # Find the container by name
            containers = manifest_data.get('containers', [])
            container_found = False
            
            for container in containers:
                if container.get('container_name') == container_name:
                    container_found = True
                    
                    # Get existing environment or initialize if not present
                    existing_env = container.get('env', {})
                    if existing_env is None:
                        existing_env = {}
                    
                    # Replace environment variables completely (not merge)
                    container['env'] = env_data.copy()
                    
                    # Count new, updated, and removed variables
                    new_vars = [key for key in env_data.keys() if key not in existing_env]
                    updated_vars = [key for key in env_data.keys() if key in existing_env and existing_env[key] != env_data[key]]
                    removed_vars = [key for key in existing_env.keys() if key not in env_data]
                    
                    self._send_message(f"✅ Updated environment variables for container: {container_name}")
                    
                    if new_vars:
                        self._send_message(f"📝 Added {len(new_vars)} new environment variables:")
                        for key in new_vars:
                            self._send_message(f"   ➕ {key}: {env_data[key]}")
                    
                    if updated_vars:
                        self._send_message(f"🔄 Updated {len(updated_vars)} existing environment variables:")
                        for key in updated_vars:
                            self._send_message(f"   🔄 {key}: {existing_env[key]} → {env_data[key]}")
                    
                    if removed_vars:
                        self._send_message(f"🗑️ Removed {len(removed_vars)} environment variables:")
                        for key in removed_vars:
                            self._send_message(f"   ➖ {key}: {existing_env[key]}")
                    
                    self._send_message(f"📊 Total environment variables in container: {len(env_data)}")
                    
                    break
            
            if not container_found:
                error_msg = f"Container '{container_name}' not found in manifest"
                self._send_message(error_msg)
                self.ws_client.send_message("save_configuration", {"error": error_msg})
                return
            
            # Write the updated manifest back to file
            try:
                with open(manifest_path, 'w', encoding='utf-8') as file:
                    yaml.dump(manifest_data, file, default_flow_style=False, allow_unicode=True, sort_keys=False)
                
                self._send_message(f"✅ Successfully saved configuration to manifest file")
                
                # Send success response
                response = {
                    "status": "success",
                    "message": f"Environment variables saved for container: {container_name}",
                    "container_name": container_name,
                    "env_variables_count": len(env_data),
                    "env_variables": env_data,
                    "changes": {
                        "added": len(new_vars),
                        "updated": len(updated_vars),
                        "removed": len(removed_vars)
                    }
                }
                
                self.ws_client.send_message("save_configuration", response)
                
            except Exception as write_error:
                error_msg = f"Error writing manifest file: {str(write_error)}"
                self._send_message(error_msg)
                self.ws_client.send_message("save_configuration", {"error": error_msg})
                return
            
        except yaml.YAMLError as e:
            error_msg = f"Error parsing YAML manifest: {str(e)}"
            self._send_message(error_msg)
            self.ws_client.send_message("save_configuration", {"error": error_msg})
        except Exception as e:
            error_msg = f"Error saving configuration: {str(e)}"
            self._send_message(error_msg)
            self.ws_client.send_message("save_configuration", {"error": error_msg})



    def _get_or_generate_subdomain(self, deployment_details: dict, data: dict) -> str:
        """Get existing subdomain or generate a new one"""
        # Check if we already have a subdomain from existing deployment
        existing_subdomain = deployment_details.get("subdomain")
        if existing_subdomain:
            self._send_message(f"🔄 Using existing subdomain: {existing_subdomain}")
            return existing_subdomain
        
        # Get project name from data or deployment details
        project_name = data.get("project_name", "") or deployment_details.get("project_name", "")
        
        # If no project name, try to get it from manifest
        if not project_name:
            try:
                root_dir = self._get_root_directory()
                manifest_path = os.path.join(root_dir, '.project_manifest.yaml')
                if os.path.exists(manifest_path):
                    with open(manifest_path, 'r', encoding='utf-8') as file:
                        manifest_data = yaml.safe_load(file)
                        project_name = manifest_data.get('overview', {}).get('project_name', '')
            except Exception:
                pass
        
        # Clean project name for subdomain
        if project_name:
            clean_project_name = project_name.lower()
            clean_project_name = clean_project_name.replace("_", "-").replace(" ", "-")
            clean_project_name = "".join(c for c in clean_project_name if c.isalnum() or c == "-")
            while "--" in clean_project_name:
                clean_project_name = clean_project_name.replace("--", "-")
            clean_project_name = clean_project_name.strip("-")
            
            # Add stage suffix for non-production environments
            if settings.STAGE in ["develop", "qa"]:
                stage_suffix = "dev1" if settings.STAGE == "develop" else "qa"
                clean_project_name = f"{clean_project_name}-{stage_suffix}"
        else:
            clean_project_name = "app"
        
        # Check for existing subdomains to avoid conflicts
        existing_subdomains = list(self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].find({
            "subdomain": {"$exists": True, "$ne": ""}
        }, {"subdomain": 1}))
        
        used_subdomains = {doc.get("subdomain") for doc in existing_subdomains if doc.get("subdomain")}
        
        # Find available subdomain
        if clean_project_name not in used_subdomains:
            final_subdomain = clean_project_name
        else:
            number = 1
            while f"{clean_project_name}-{number}" in used_subdomains:
                number += 1
            final_subdomain = f"{clean_project_name}-{number}"
        
        self._send_message(f"🆕 Generated new subdomain: {final_subdomain}")
        return final_subdomain
    
    def _get_content_type(self, file_path: str) -> str:
        """Get MIME content type for a file"""
        import mimetypes
        content_type, _ = mimetypes.guess_type(file_path)
        return content_type or 'application/octet-stream'
    
    def _is_static_asset(self, file_path: str) -> bool:
        """Check if file is a static asset that can be cached long-term"""
        static_extensions = {'.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.woff', '.woff2', '.ttf', '.ico'}
        _, ext = os.path.splitext(file_path.lower())
        return ext in static_extensions


    
    async def handle_deploy_artifact(self, build_path: str, ws_client: WebSocketClient, data: Dict[str, Any]):
        """Handle deploying an artifact
        
        Extracts files from Docker container build path and uploads directly to S3 at s3://kavia-apps/{subdomain}
        which automatically serves the app at https://{subdomain}.kavia.app
        
        Args:
            build_path: Path to the directory containing build artifacts inside Docker container
            ws_client: WebSocket client for sending status updates
        """
        
        self.ws_client = ws_client
        
        try:
            deployment_id = str(data.get("id", "")) or str(uuid.uuid4())[:8]
            docker_executor = self.agent.executor
            
            self._send_message(f"🚀 Starting deployment process for artifacts in Docker container at {build_path}")
            
            # List files in build directory inside Docker container
            self._send_message(f"🔍 Scanning build directory inside container: {build_path}")
            
            class FileListCallback(DockerShellCallback):
                def __init__(self):
                    self.output = ""
                    self.exit_code = None
                
                def on_output(self, output):
                    if output:
                        self.output += output.decode('utf-8') if isinstance(output, bytes) else str(output)
                
                def on_exit(self, return_code):
                    self.exit_code = return_code
            
            # Try to list files inside Docker container first
            list_cmd = f"if [ -d '{build_path}' ]; then find '{build_path}' -type f 2>/dev/null || echo 'FIND_FAILED'; else echo 'BUILD_DIR_NOT_FOUND'; fi"
            
            callback = FileListCallback()
            subprocess = await docker_executor.create_subprocess_shell(
                cmd=list_cmd,
                callback=callback,
                work_directory=os.path.dirname(build_path)  # Use parent directory as working directory
            )
            
            await subprocess.wait(timeout=60)
            
            # Check for error conditions in output
            output_lines = callback.output.strip().split('\n') if callback.output.strip() else []
            container_listing_failed = False
            
            if 'BUILD_DIR_NOT_FOUND' in callback.output or 'FIND_FAILED' in callback.output or not callback.output.strip():
                self._send_message(f"⚠️ Docker container listing failed for {build_path}")
                self._send_message(f"Container output: {callback.output}")
                container_listing_failed = True
            
            # If container listing failed, try to access files directly from host filesystem
            # since build files are created in mounted directory
            if container_listing_failed:
                self._send_message("🔄 Trying to access build files directly from host filesystem...")
                
                try:
                    if os.path.exists(build_path):
                        self._send_message(f"✅ Build directory exists on host: {build_path}")
                        
                        # List files using host filesystem
                        all_files = []
                        for root, dirs, files in os.walk(build_path):
                            for file in files:
                                file_path = os.path.join(root, file)
                                rel_path = os.path.relpath(file_path, build_path)
                                file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                                all_files.append((rel_path, file_size, file_path))
                        
                        self._send_message(f"📁 Found {len(all_files)} files on host filesystem")
                    else:
                        raise Exception(f"Build directory not found on both container and host: {build_path}")
                        
                except Exception as host_error:
                    raise Exception(f"Failed to access build files from both container and host: {str(host_error)}")
                    
            else:
                # Parse container output
                all_files = []
                for line in output_lines:
                    if line and line.strip():
                        line = line.strip()
                        # Skip error messages that might have been captured
                        if any(error_indicator in line.lower() for error_indicator in ['error', 'failed', 'oci runtime', 'chdir']):
                            self._send_message(f"⚠️ Skipping error line: {line}")
                            continue
                        
                        # For find command output, the line should be a file path
                        if line.startswith(build_path):
                            file_path = line
                            rel_path = os.path.relpath(file_path, build_path)
                            all_files.append((rel_path, 0, file_path))
            
            if not all_files:
                raise Exception(f"No files found in build directory: {build_path}")
            
            self._send_message(f"📁 Found {len(all_files)} files in container build directory:")
            for file_rel_path, file_size, _ in sorted(all_files)[:10]:  # Show first 10 files
                self._send_message(f"   📄 {file_rel_path} ({file_size} bytes)")
            if len(all_files) > 10:
                self._send_message(f"   ... and {len(all_files) - 10} more files")
            
            # Check specifically for index.html
            index_html_found = any(f[0].lower() == 'index.html' for f in all_files)
            
            # Enhanced validation for different framework types
            html_files = [f[0] for f in all_files if f[0].lower().endswith('.html')]
            js_files = [f[0] for f in all_files if f[0].endswith('.js')]
            css_files = [f[0] for f in all_files if f[0].endswith('.css')]
            
            # Special handling for Next.js static export
            nextjs_indicators = [
                f for f in all_files 
                if '_next/' in f[0] or 'next/' in f[0] or f[0].startswith('_next') or f[0] == '_app-[hash].js'
            ]
            is_nextjs_build = len(nextjs_indicators) > 0
            
            self._send_message(f"📊 Build Content Analysis:")
            self._send_message(f"   📄 HTML files: {len(html_files)} - {html_files[:3]}{'...' if len(html_files) > 3 else ''}")
            self._send_message(f"   📄 JS files: {len(js_files)} - {js_files[:3]}{'...' if len(js_files) > 3 else ''}")
            self._send_message(f"   📄 CSS files: {len(css_files)} - {css_files[:3]}{'...' if len(css_files) > 3 else ''}")
            if is_nextjs_build:
                self._send_message(f"   🔧 Next.js indicators: {len(nextjs_indicators)} - {nextjs_indicators[:2]}{'...' if len(nextjs_indicators) > 2 else ''}")
            
            if not index_html_found:
                if len(html_files) > 0:
                    self._send_message("⚠️ WARNING: index.html not found, but other HTML files exist")
                    self._send_message(f"   Available HTML files: {html_files}")
                    self._send_message("   Your app might still work if it uses a different entry point")
                elif is_nextjs_build:
                    self._send_message("ℹ️ INFO: This appears to be a Next.js build")
                    self._send_message("   Next.js static export may not always generate index.html in the root")
                    self._send_message("   Checking for Next.js-specific files and pages...")
                elif len(js_files) > 0:
                    self._send_message("ℹ️ INFO: No HTML files found, but JS files exist")
                    self._send_message("   This might be an API/backend build or SPA with dynamic HTML")
                else:
                    self._send_message("❌ CRITICAL: No index.html or other HTML files found!")
                    self._send_message("   This indicates your build process may have failed or is incomplete.")
                    self._send_message("   Common causes:")
                    self._send_message("   1. Build command was interrupted or failed")
                    self._send_message("   2. Wrong build directory (should be 'build' for React, 'out' for Next.js)")
                    self._send_message("   3. Package.json missing or incorrect build script")
                    self._send_message("   4. Dependencies not installed properly")
                    
                    # Check for common React build files
                    has_static_folder = any('static/' in f[0] for f in all_files)
                    has_manifest = any('manifest.json' in f[0] for f in all_files)
                    has_favicon = any('favicon.ico' in f[0] for f in all_files)
                    
                    if not has_static_folder:
                        self._send_message("   ⚠️ No 'static/' folder found - indicates incomplete React build")
                    if not has_manifest:
                        self._send_message("   ⚠️ No 'manifest.json' found - indicates incomplete React build")
                    if not has_favicon:
                        self._send_message("   ⚠️ No 'favicon.ico' found - indicates incomplete React build")
                    
                    # Still attempt deployment but mark as potentially problematic
                    self._send_message("   🔄 Attempting deployment despite missing files...")
            else:
                self._send_message("✅ index.html found in build output")
                # Check index.html size
                index_file = next((f for f in all_files if f[0].lower() == 'index.html'), None)
                if index_file and index_file[1] < 500:  # Less than 500 bytes might indicate empty/minimal file
                    self._send_message(f"   ⚠️ Warning: index.html is very small ({index_file[1]} bytes) - may be incomplete")
                else:
                    self._send_message(f"   ✅ index.html size looks good ({index_file[1]} bytes)")

            # Get deployment details for subdomain information
            deployment_details = self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].find_one({"_id": deployment_id})
            if not deployment_details:
                self._send_message("Warning: No deployment details found in database")
                deployment_details = {}
            
            # Get or generate subdomain
            subdomain_prefix = self._get_or_generate_subdomain(deployment_details, data)
            app_url = f"https://{subdomain_prefix}.kavia.app"
            
            self._send_message(f"📡 Deploying to subdomain: {subdomain_prefix}")
            self._send_message(f"🌐 App will be available at: {app_url}")
            
            # Initialize S3 client
            s3_client = boto3.client('s3', 
                region_name=settings.AWS_DEPLOYMENT_REGION,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
            )
            
            # Create temporary directory for extracted files
            import tempfile
            temp_dir = tempfile.mkdtemp()
            
            try:
                # Extract files from Docker container to temporary directory
                self._send_message("📦 Extracting build artifacts from Docker container...")
                extracted_count = 0
                uploaded_files = []
                
                for rel_path, file_size, full_path in all_files:
                    try:
                        # Create local temp file path
                        local_temp_path = os.path.join(temp_dir, rel_path)
                        local_temp_dir = os.path.dirname(local_temp_path)
                        os.makedirs(local_temp_dir, exist_ok=True)
                        
                        # Check if we can access the file directly from host (when container listing failed)
                        if container_listing_failed and os.path.exists(full_path):
                            # Direct host file access - just copy the file
                            self._send_message(f"📁 Copying {rel_path} directly from host filesystem")
                            
                            try:
                                import shutil
                                shutil.copy2(full_path, local_temp_path)
                                
                                if os.path.exists(local_temp_path) and os.path.getsize(local_temp_path) > 0:
                                    # Upload to S3
                                    s3_key = f"{subdomain_prefix}/{rel_path}"
                                    
                                    with open(local_temp_path, 'rb') as file_data:
                                        s3_client.upload_fileobj(
                                            file_data,
                                            "kavia-apps",
                                            s3_key,
                                            ExtraArgs={
                                                'ContentType': self._get_content_type(local_temp_path),
                                                'CacheControl': 'max-age=31536000' if self._is_static_asset(local_temp_path) else 'max-age=300'
                                            }
                                        )
                                    
                                    extracted_count += 1
                                    uploaded_files.append(rel_path)
                                    
                                    # Clean up temp file
                                    os.remove(local_temp_path)
                                else:
                                    self._send_message(f"⚠️ Host file copy failed or resulted in empty file: {rel_path}")
                                    
                            except Exception as host_copy_error:
                                self._send_message(f"❌ Host file copy failed for {rel_path}: {str(host_copy_error)}")
                                continue
                                
                        else:
                            # Container-based extraction (original logic)
                            container = docker_executor.client.containers.get(docker_executor.container_name)
                            
                            try:
                                # First verify the file exists in container
                                verify_cmd = f"test -f '{full_path}' && echo 'FILE_EXISTS' || echo 'FILE_NOT_FOUND'"
                                
                                class FileVerifyCallback(DockerShellCallback):
                                    def __init__(self):
                                        self.output = ""
                                        self.exit_code = None
                                    
                                    def on_output(self, output):
                                        if output:
                                            decoded = output.decode('utf-8') if isinstance(output, bytes) else str(output)
                                            self.output += decoded.strip()
                                    
                                    def on_exit(self, return_code):
                                        self.exit_code = return_code
                                
                                verify_callback = FileVerifyCallback()
                                verify_subprocess = await docker_executor.create_subprocess_shell(
                                    cmd=verify_cmd,
                                    callback=verify_callback,
                                    work_directory=os.path.dirname(full_path)
                                )
                                
                                await verify_subprocess.wait(timeout=10)
                                
                                if 'FILE_NOT_FOUND' in verify_callback.output:
                                    self._send_message(f"⚠️ File not found in container: {full_path}")
                                    continue
                                    
                                # Use docker cp to extract the file
                                import subprocess as sp
                                copy_result = sp.run([
                                    'docker', 'cp',
                                    f"{docker_executor.container_name}:{full_path}",
                                    local_temp_path
                                ], capture_output=True, text=True, timeout=30)
                                
                                if copy_result.returncode == 0 and os.path.exists(local_temp_path):
                                    # Upload to S3
                                    s3_key = f"{subdomain_prefix}/{rel_path}"
                                    
                                    with open(local_temp_path, 'rb') as file_data:
                                        s3_client.upload_fileobj(
                                            file_data,
                                            "kavia-apps",
                                            s3_key,
                                            ExtraArgs={
                                                'ContentType': self._get_content_type(local_temp_path),
                                                'CacheControl': 'max-age=31536000' if self._is_static_asset(local_temp_path) else 'max-age=300'
                                            }
                                        )
                                    
                                    extracted_count += 1
                                    uploaded_files.append(rel_path)
                                    
                                    # Clean up temp file
                                    os.remove(local_temp_path)
                                    
                                else:
                                    # Fallback to cat method with better error handling if docker cp fails
                                    self._send_message(f"⚠️ Docker cp failed for {rel_path}, trying cat fallback: {copy_result.stderr}")
                                    
                                    # Enhanced cat command with error handling
                                    extract_cmd = f"if [ -f '{full_path}' ]; then cat '{full_path}'; else echo 'CAT_FILE_NOT_FOUND'; fi"
                                    
                                    class FileExtractCallback(DockerShellCallback):
                                        def __init__(self, output_file):
                                            self.output_file = output_file
                                            self.exit_code = None
                                            self.has_error = False
                                            self.file_handle = open(output_file, 'wb')
                                        
                                        def on_output(self, output):
                                            if output:
                                                decoded_output = output.decode('utf-8') if isinstance(output, bytes) else str(output)
                                                if 'CAT_FILE_NOT_FOUND' in decoded_output:
                                                    self.has_error = True
                                                    return
                                                
                                                if isinstance(output, bytes):
                                                    self.file_handle.write(output)
                                                else:
                                                    self.file_handle.write(output.encode('utf-8'))
                                        
                                        def on_exit(self, return_code):
                                            self.exit_code = return_code
                                            self.file_handle.close()
                                    
                                    extract_callback = FileExtractCallback(local_temp_path)
                                    extract_subprocess = await docker_executor.create_subprocess_shell(
                                        cmd=extract_cmd,
                                        callback=extract_callback,
                                        work_directory=os.path.dirname(full_path)
                                    )
                                    
                                    await extract_subprocess.wait(timeout=30)
                                    
                                    if not extract_callback.has_error and extract_callback.exit_code == 0 and os.path.exists(local_temp_path) and os.path.getsize(local_temp_path) > 0:
                                        # Upload to S3
                                        s3_key = f"{subdomain_prefix}/{rel_path}"
                                        
                                        with open(local_temp_path, 'rb') as file_data:
                                            s3_client.upload_fileobj(
                                                file_data,
                                                "kavia-apps",
                                                s3_key,
                                                ExtraArgs={
                                                    'ContentType': self._get_content_type(local_temp_path),
                                                    'CacheControl': 'max-age=31536000' if self._is_static_asset(local_temp_path) else 'max-age=300'
                                                }
                                            )
                                        
                                        extracted_count += 1
                                        uploaded_files.append(rel_path)
                                        
                                        # Clean up temp file
                                        os.remove(local_temp_path)
                                    else:
                                        self._send_message(f"⚠️ Fallback extraction also failed for: {rel_path}")
                                        if os.path.exists(local_temp_path):
                                            os.remove(local_temp_path)  # Clean up failed file
                            
                            except sp.TimeoutExpired:
                                self._send_message(f"⚠️ Docker cp timeout for: {rel_path}")
                                continue
                            except Exception as docker_error:
                                self._send_message(f"⚠️ Docker operation failed for {rel_path}: {str(docker_error)}")
                                continue
                                
                    except Exception as file_error:
                        self._send_message(f"❌ Error extracting {rel_path}: {str(file_error)}")
                        # Clean up any partial temp file
                        if os.path.exists(local_temp_path):
                            try:
                                os.remove(local_temp_path)
                            except:
                                pass
                        continue
                
                file_count = extracted_count
                self._send_message(f"✅ Extracted and uploaded {file_count} files to s3://kavia-apps/{subdomain_prefix}/")
                
            finally:
                # Clean up temp directory
                import shutil
                try:
                    shutil.rmtree(temp_dir)
                except:
                    pass
            
            self._send_message(f"✅ Uploaded {file_count} files to s3://kavia-apps/{subdomain_prefix}/")
            
            # Log uploaded files for debugging
            if len(uploaded_files) <= 20:  # Only show if reasonable number
                self._send_message("📋 Uploaded files:")
                for uploaded_file in sorted(uploaded_files):
                    self._send_message(f"   ✅ {uploaded_file}")
            
            if index_html_found:
                self._send_message(f"🎉 Deployment complete! App is now live at: {app_url}")
            else:
                self._send_message(f"⚠️ Deployment complete but index.html missing! App may not work at: {app_url}")
                self._send_message("   Please check your build process and ensure it generates index.html")
            
            # Send success status immediately
            self.ws_client.send_message("deployment_status", {
                "id": deployment_id,
                "status": "success" if index_html_found else "warning",
                "message": f"✅ Deployment successful! App is live at {app_url}" if index_html_found else f"⚠️ Deployment completed with warnings at {app_url}",
                "app_url": app_url,
                "subdomain": subdomain_prefix,
                "deployment_type": "s3_direct",
                "files_uploaded": file_count,
                "index_html_found": index_html_found
            })
            
            # Update database with final status
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": deployment_id},
                {"$set": {
                    "status": "success",
                    "message": f"Deployment successful! App deployed to {app_url} at {current_time}",
                    "app_url": app_url,
                    "subdomain": subdomain_prefix,
                    "custom_domain": app_url,  # Same as app_url for consistency
                    "artifact_path": f"s3://kavia-apps/{subdomain_prefix}/",
                    "updated_at": datetime.now()
                }}
            )
            

            
            return {
                "status": "success",
                "type": "deployment",
                "message": f"App deployed successfully to {app_url}",
                "app_url": app_url,
                "subdomain": subdomain_prefix,
                "artifact_path": f"s3://kavia-apps/{subdomain_prefix}/"
            }
            
        except Exception as e:
            error_msg = f"Error during deployment: {str(e)}"
            self._send_message(f"❌ {error_msg}")
            
            # Send error status via WebSocket
            self.ws_client.send_message("deployment_status", {
                "status": "failed",
                "message": error_msg
            })
            
            # Update database with error status
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": data.get("id", "")},
                {"$set": {
                    "status": "failed",
                    "message": f"{error_msg} at {current_time}",
                    "updated_at": datetime.now()
                }}
            )
            
            return {
                "status": "failed",
                "message": error_msg
            }

    def _get_current_branch_subprocess(self, directory_path: str) -> str:
        """
        Get the current git branch using subprocess command.
        
        Args:
            directory_path (str): The directory path where the git repository is located
            
        Returns:
            str: The current branch name, or 'main' as fallback if failed
        """
        try:
            # Change to the directory and run git branch --show-current
            result = subprocess.run(
                ['git', 'branch', '--show-current'],
                cwd=directory_path,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0 and result.stdout.strip():
                branch_name = result.stdout.strip()
                return branch_name
            else:
                return 'main'  # Default fallback
                
        except subprocess.TimeoutExpired:
            return 'main'
        except subprocess.CalledProcessError:
            return 'main'
        except FileNotFoundError:
            return 'main'
        except Exception:
            return 'main'

    def _get_current_branch_name(self, root_path: str) -> str:
        """
        Get the current git branch name from the root path.
        
        Args:
            root_path (str): The root directory path of the project
            
        Returns:
            str: The current branch name or 'kavia-main' as fallback
        """
        try:
            # Use the subprocess method to get current branch
            branch_name = self._get_current_branch_subprocess(root_path)
            
            if branch_name and branch_name.strip() != 'main':
                self._send_message(f"Detected current git branch: {branch_name}")
                return branch_name
            
            # If still no branch found, return default
            self._send_message("No specific branch detected, using default: kavia-main")
            return 'kavia-main'
                
        except Exception as e:
            self._send_message(f"Error detecting git branch: {str(e)}, using default: kavia-main")
            return 'kavia-main'