{"namespace": "org.sample.project", "model": {"Project": {"description": "A software project capturing the comprehensive requirements and details of a system or component to be developed.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Project' for this entity"}, "Title": {"type": "string", "description": "The official name of the project. Should be concise and descriptive."}, "Description": {"type": "string", "description": "A detailed explanation of the project, including its context, main objectives, and expected outcomes. Provides enough information for team members or AI to understand the project's purpose and significance."}, "Objective": {"type": "string", "description": "Specific, measurable goals that the project aims to achieve. It should outline the tangible outcomes and deliverables expected from the project."}, "Scope": {"type": "string", "description": "A comprehensive outline of the project's boundaries, including main features, functionalities, and deliverables. Organized into logical categories and provides sufficient detail for initial planning and resource allocation."}, "ArchitecturePattern": {"type": "string", "enum": ["monolithic", "multi-container-single-component", "multi-container-service", "adaptive"], "description": "The architectural pattern for the system", "default": "adaptive"}, "ArchitectureStrategy": {"type": "string", "description": "High-level architectural guidance including decomposition strategy, key patterns, and component organization principles to be followed throughout the system."}, "AdditionalDetails": {"type": "string", "description": "Any additional project details such as platform and its framework and other user provided details which are not covered in other properties"}, "TestAutomationFramework": {"type": "string", "description": "The selected test automation framework for the project. This should be recommended based on project description, scope, and technologies used."}, "configuration_state": {"type": "string", "description": "Current state of project configuration"}}, "ui_metadata": {"Type": {"Label": "Type", "order": 1, "display_type": "text"}, "Title": {"Label": "Title", "order": 2, "display_type": "text"}, "Description": {"Label": "Description", "order": 3, "display_type": "rich_text"}, "Scope": {"Label": "<PERSON><PERSON>", "order": 4, "display_type": "rich_text"}, "Objective": {"Label": "Objective", "order": 5, "display_type": "rich_text"}, "ArchitecturePattern": {"Label": "Architecture Pattern", "order": 6, "display_type": "rich_text"}, "ArchitectureStrategy": {"Label": "Architecture Strategy", "order": 7, "display_type": "rich_text"}, "AdditionalDetails": {"Label": "Additional Details", "order": 8, "display_type": "rich_text"}, "TestAutomationFramework": {"Label": "Test Automation Framework", "order": 9, "display_type": "text"}, "configuration_state": {"Label": "Configuration State", "hidden": true}}, "relationships": {"hasChild": {"types": ["RequirementRoot", "ArchitectureRoot", "WorkItemRoot"], "description": "Child nodes of the Project"}}}, "RequirementRoot": {"description": "Root node for project requirements, serving as the central point for organizing and managing all project requirements.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'RequirementRoot' for this entity"}, "Title": {"type": "string", "description": "Concise title for the requirements root, typically reflecting the project or major feature set"}, "Description": {"type": "string", "description": "Detailed and Comprehensive overview of the requirements structure, including the types of requirements managed and their overall purpose in the project"}, "Scope": {"type": "string", "description": "Detailed outline of what is included and excluded in the requirements, defining the boundaries of the project's functionality and features"}, "change_log": {"type": "string", "description": "JSON string containing history of changes made to this requirement root"}, "user_inputs": {"type": "string", "description": "JSON string containing record of user inputs during configuration"}, "change_reason": {"type": "string", "description": "Reason for the last change"}, "changes_needed": {"type": "boolean", "description": "Flag indicating whether changes were needed during reconfiguration"}, "configuration_state": {"type": "string", "description": "Current state of requirement configuration"}}, "ui_metadata": {"Type": {"Label": "Type", "order": 1, "display_type": "text"}, "Title": {"Label": "Title", "order": 2, "display_type": "text"}, "Description": {"Label": "Description", "order": 3, "display_type": "rich_text"}, "Scope": {"Label": "<PERSON><PERSON>", "order": 4, "display_type": "rich_text"}, "configuration_state": {"Label": "Configuration State", "hidden": true}}, "relationships": {"hasChild": {"types": ["Epic", "Requirement"], "description": "Child nodes of the RequirementRoot, representing different levels and types of requirements"}}}, "Epic": {"description": "Requirements are specified as Epics and User stories. Epic is a top level requirement for a feature or functionality in the project.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Epic' for this entity"}, "Title": {"type": "string", "description": "Provide a descriptive title for the epic following the industry standard"}, "Description": {"type": "string", "description": "Detailed description of the epic"}, "Priority": {"type": "string", "enum": ["High", "Medium", "Low"], "description": "Priority of the epic considering the stage in software development life cycle. Do not use numbers to represent. Only use 'Low','Medium','High' based on the criticality"}, "change_log": {"type": "string", "description": "JSON string containing history of changes made to this requirement root"}, "user_inputs": {"type": "string", "description": "JSON string containing record of user inputs during configuration"}, "change_reason": {"type": "string", "description": "Reason for the last change"}, "changes_needed": {"type": "boolean", "description": "Flag indicating whether changes were needed during reconfiguration"}, "configuration_state": {"type": "string", "description": "Current state of requirement configuration"}}, "ui_metadata": {"Type": {"Label": "Type", "order": 1, "display_type": "text"}, "Title": {"Label": "Title", "order": 2, "display_type": "text"}, "Description": {"Label": "Description", "order": 3, "display_type": "rich_text"}, "Priority": {"Label": "Priority", "order": 4, "display_type": "number"}, "Definition_of_done": {"Label": "Definition of Done", "order": 5, "display_type": "rich_text"}, "configuration_state": {"Label": "Configuration State", "hidden": true}}, "relationships": {"hasChild": {"types": ["UserStory"], "description": "User stories associated with this epic"}}}, "UserStory": {"description": "Represents a specific, concise requirement or feature from an end user's perspective, typically part of an Epic.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'UserStory' for this entity"}, "Title": {"type": "string", "description": "Concise title of the user story, ideally in the format 'As a <type of user>, I want <some goal> so that <some reason>'"}, "Description": {"type": "string", "description": "Detailed explanation of the user story, including any additional context, constraints, or considerations"}, "Priority": {"type": "string", "description": "Relative importance of the user story, typically expressed as 'High', 'Medium', or 'Low'.Also consoder the priority of the parent epic when setting the priority"}, "StoryPoints": {"type": "integer", "description": "Estimated effort required to complete the user story, usually on a predefined scale (e.g., <PERSON><PERSON><PERSON><PERSON> sequence)"}, "AcceptanceCriteria": {"type": "array", "items": {"type": "string"}, "description": "List of specific conditions that must be satisfied for the user story to be considered complete"}, "change_log": {"type": "string", "description": "JSON string containing history of changes made to this requirement root"}, "user_inputs": {"type": "string", "description": "JSON string containing record of user inputs during configuration"}, "change_reason": {"type": "string", "description": "Reason for the last change"}, "changes_needed": {"type": "boolean", "description": "Flag indicating whether changes were needed during reconfiguration"}, "configuration_state": {"type": "string", "description": "Current state of the user story's configuration in the project management process"}}, "ui_metadata": {"Type": {"Label": "Type", "order": 1, "display_type": "text"}, "Title": {"Label": "Title", "order": 2, "display_type": "text"}, "Description": {"Label": "Description", "order": 3, "display_type": "rich_text"}, "Task": {"Label": "Task", "order": 4, "display_type": "multiselect"}, "Priority": {"Label": "Priority", "order": 5, "display_type": "number"}, "StoryPoints": {"Label": "Story Points", "order": 6, "display_type": "number"}, "AcceptanceCriteria": {"Label": "Acceptance Criteria", "order": 7, "display_type": "rich_text"}, "configuration_state": {"Label": "Configuration State", "hidden": true}}, "relationships": {"hasChild": {"types": ["Task"], "description": "Tasks associated with this user story, representing more granular work items for implementation"}}}, "Task": {"description": "Represents a specific, actionable item of work that contributes to the completion of a UserStory. Tasks are the most granular level of work in the project management hierarchy.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Task' for this entity"}, "Title": {"type": "string", "description": "Clear, concise title of the task, describing the specific action or work to be done"}, "Description": {"type": "string", "description": "Detailed explanation of the task, including any technical specifications, constraints, or additional context needed for completion"}, "Dependencies": {"type": "array", "items": {"type": "string"}, "description": "List of other tasks or conditions that must be completed or met before this task can be started or finished"}, "Priority": {"type": "string", "description": "Relative importance of the task, typically expressed as 'High', 'Medium', or 'Low'"}, "StoryPoints": {"type": "integer", "description": "Estimated effort required to complete the task, usually on a predefined scale (e.g., 1, 2, 3, 5, 8)"}, "Status": {"type": "string", "description": "Current state of the task in the development process (e.g., 'To Do', 'In Progress', 'Done')"}, "change_log": {"type": "string", "description": "JSON string containing history of changes made to this requirement root"}, "user_inputs": {"type": "string", "description": "JSON string containing record of user inputs during configuration"}, "change_reason": {"type": "string", "description": "Reason for the last change"}, "changes_needed": {"type": "boolean", "description": "Flag indicating whether changes were needed during reconfiguration"}}, "ui_metadata": {"Type": {"Label": "Type", "order": 1, "display_type": "text"}, "Title": {"Label": "Title", "order": 2, "display_type": "text"}, "Description": {"Label": "Description", "order": 3, "display_type": "rich_text"}, "Dependencies": {"Label": "Dependencies", "order": 4, "display_type": "text"}, "Priority": {"Label": "Priority", "order": 6, "display_type": "text"}, "StoryPoints": {"Label": "Story Points", "order": 7, "display_type": "number"}, "Status": {"Label": "Status", "order": 8, "display_type": "text"}}}, "TestCaseRoot": {"description": "Root node for all test cases in the project", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'TestCaseRoot' for this entity"}, "Title": {"type": "string", "description": "Title of the test case root"}, "Description": {"type": "string", "description": "Description of the test case root"}, "CommonTags": {"type": "array", "items": {"type": "string"}, "description": "List of common tags used across all test cases in the project"}, "FunctionalCategories": {"type": "array", "items": {"type": "string"}, "description": "Dynamic list of functional test categories for this project"}, "NonFunctionalCategories": {"type": "array", "items": {"type": "string"}, "description": "Dynamic list of non-functional test categories for this project"}, "AutomationCategories": {"type": "array", "items": {"type": "string"}, "description": "List of automation categories for test cases in this project"}, "existing_test_case_ids": {"type": "array", "items": {"type": "integer"}, "description": "Before creating new test cases, search through all project test cases to find and reuse relevant ones. IDs of existing test cases from the project that are relevant to this User Story."}, "configuration_state": {"type": "string", "description": "Current state of the test case root configuration (e.g., 'not_configured', 'configured')"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "existing_test_case_ids": {"Label": "Existing Test Case IDs", "display_type": "rich_text"}, "AutomationCategories": {"Label": "Automation Categories", "display_type": "rich_text"}, "configuration_state": {"Label": "Configuration State", "hidden": true}}, "relationships": {"hasChild": {"types": ["FunctionalTestCase", "NonFunctionalTestCase", "StressTest", "StabilityTest", "InteroperabilityTest"], "description": "Test cases associated with this root"}}}, "FunctionalTestCase": {"description": "Represents a functional test case for verifying specific functionality or behavior of the system", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'FunctionalTestCase' for this entity"}, "Title": {"type": "string", "description": "A brief, descriptive title for the functional test case"}, "Description": {"type": "string", "description": "Detailed description of the functional test case, including its purpose and scope"}, "Category": {"type": "string", "description": "Dynamic category of the functional test case"}, "Tags": {"type": "array", "items": {"type": "string"}, "description": "List of tags associated with this test case"}, "TestLevel": {"type": "string", "enum": ["Unit", "Component", "System"], "description": "The level at which this test case operates (Unit, Component, or System)", "default": "Component"}, "ComponentsInvolved": {"type": "array", "items": {"type": "integer"}, "description": "IDs of components involved in this test (particularly for System level tests)"}, "ExternalSystemsInvolved": {"type": "array", "items": {"type": "string"}, "description": "Names or identifiers of external systems involved in this test case"}, "InterfacesToTest": {"type": "array", "items": {"type": "integer"}, "description": "IDs of interfaces that need to be tested in this test case"}, "RelatedUserStories": {"type": "array", "items": {"type": "integer"}, "description": "IDs of user stories that this test case verifies"}, "PreConditions": {"type": "string", "description": "Any preconditions that must be met before executing the functional test"}, "Steps": {"type": "array", "items": {"type": "string"}, "description": "Ordered list of steps to execute the functional test"}, "ExpectedResult": {"type": "string", "description": "The expected outcome of the functional test when executed correctly"}, "ActualResult": {"type": "string", "description": "The actual outcome of the functional test after execution (to be filled later)"}, "CanBeAutomated": {"type": "boolean", "description": "Flag indicating whether this test case can be automated", "default": false}, "Priority": {"type": "string", "enum": ["Low", "Medium", "High", "Critical"], "description": "Priority level for this test case", "default": "Medium"}, "MethodsToTest": {"type": "array", "items": {"type": "string"}, "description": "List of specific methods or functions to be tested in this test case"}, "MockedDependencies": {"type": "array", "items": {"type": "string"}, "description": "List of dependencies that should be mocked during testing"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "PreConditions": {"Label": "Pre-conditions", "display_type": "rich_text"}, "Steps": {"Label": "Test Steps", "display_type": "rich_text"}, "ExpectedResult": {"Label": "Expected Result", "display_type": "rich_text"}, "ActualResult": {"Label": "Actual Result", "display_type": "rich_text"}, "TestLevel": {"Label": "Test Level", "display_type": "dropdown"}, "ComponentsInvolved": {"Label": "Components Involved", "display_type": "multiselect"}, "ExternalSystemsInvolved": {"Label": "External Systems Involved", "display_type": "multiselect"}, "InterfacesToTest": {"Label": "Interfaces to Test", "display_type": "multiselect"}, "RelatedUserStories": {"Label": "Related User Stories", "display_type": "multiselect"}, "CanBeAutomated": {"Label": "Can Be Automated", "display_type": "boolean"}, "Priority": {"Label": "Priority", "display_type": "select"}, "MethodsToTest": {"Label": "Methods To Test", "display_type": "multiselect"}, "MockedDependencies": {"Label": "Mocked Dependencies", "display_type": "multiselect"}}}, "NonFunctionalTestCase": {"description": "Represents a non-functional test case for verifying system qualities such as performance, scalability, usability, etc.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'NonFunctionalTestCase' for this entity"}, "Title": {"type": "string", "description": "A brief, descriptive title for the non-functional test case"}, "Description": {"type": "string", "description": "Detailed description of the non-functional test case, including its purpose and scope"}, "Category": {"type": "string", "description": "Category of non-functional requirement (e.g., Performance, Scalability, Security, Usability)"}, "Tags": {"type": "array", "items": {"type": "string"}, "description": "List of tags associated with this test case"}, "TestLevel": {"type": "string", "enum": ["Unit", "Component", "System"], "description": "The level at which this test case operates (Unit, Component, or System)", "default": "Component"}, "ComponentsInvolved": {"type": "array", "items": {"type": "integer"}, "description": "IDs of components involved in this test (particularly for System level tests)"}, "ExternalSystemsInvolved": {"type": "array", "items": {"type": "string"}, "description": "Names or identifiers of external systems involved in this test case"}, "InterfacesToTest": {"type": "array", "items": {"type": "integer"}, "description": "IDs of interfaces that need to be tested in this test case"}, "RelatedUserStories": {"type": "array", "items": {"type": "integer"}, "description": "IDs of user stories that this test case verifies"}, "TestEnvironment": {"type": "string", "description": "Description of the environment required for this non-functional test"}, "TestProcedure": {"type": "string", "description": "Detailed procedure for executing the non-functional test"}, "AcceptanceCriteria": {"type": "string", "description": "Specific criteria that must be met for the non-functional test to pass"}, "MeasurementMetrics": {"type": "array", "items": {"type": "string"}, "description": "List of metrics to be measured during the non-functional test"}, "ExpectedResults": {"type": "string", "description": "The expected outcome or performance levels for the non-functional test"}, "ActualResults": {"type": "string", "description": "The actual outcome or performance levels after non-functional test execution (to be filled later)"}, "CanBeAutomated": {"type": "boolean", "description": "Flag indicating whether this test case can be automated", "default": false}, "Priority": {"type": "string", "enum": ["Low", "Medium", "High", "Critical"], "description": "Priority level for this test case", "default": "Medium"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "TestEnvironment": {"Label": "Test Environment", "display_type": "rich_text"}, "TestProcedure": {"Label": "Test Procedure", "display_type": "rich_text"}, "AcceptanceCriteria": {"Label": "Acceptance Criteria", "display_type": "rich_text"}, "ExpectedResults": {"Label": "Expected Results", "display_type": "rich_text"}, "TestLevel": {"Label": "Test Level", "display_type": "dropdown"}, "ComponentsInvolved": {"Label": "Components Involved", "display_type": "multiselect"}, "ExternalSystemsInvolved": {"Label": "External Systems Involved", "display_type": "multiselect"}, "InterfacesToTest": {"Label": "Interfaces to Test", "display_type": "multiselect"}, "RelatedUserStories": {"Label": "Related User Stories", "display_type": "multiselect"}, "CanBeAutomated": {"Label": "Can Be Automated", "display_type": "boolean"}, "Priority": {"Label": "Priority", "display_type": "select"}}}, "StressTest": {"description": "Represents a stress test case for evaluating system behavior under extreme conditions", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'StressTest' for this entity"}, "Title": {"type": "string", "description": "A brief, descriptive title for the stress test"}, "Description": {"type": "string", "description": "Detailed description of the stress test, including its purpose and scope"}, "Tags": {"type": "array", "items": {"type": "string"}, "description": "List of tags associated with this test case"}, "TestLevel": {"type": "string", "enum": ["Unit", "Component", "System"], "description": "The level at which this test case operates (Unit, Component, or System)", "default": "System"}, "ComponentsInvolved": {"type": "array", "items": {"type": "integer"}, "description": "IDs of components involved in this test (particularly for System level tests)"}, "ExternalSystemsInvolved": {"type": "array", "items": {"type": "string"}, "description": "Names or identifiers of external systems involved in this test case"}, "InterfacesToTest": {"type": "array", "items": {"type": "integer"}, "description": "IDs of interfaces that need to be tested in this test case"}, "RelatedUserStories": {"type": "array", "items": {"type": "integer"}, "description": "IDs of user stories that this test case verifies"}, "StressConditions": {"type": "string", "description": "Description of the stress conditions to be applied during the test"}, "TestProcedure": {"type": "string", "description": "Detailed procedure for executing the stress test"}, "ExpectedBehavior": {"type": "string", "description": "The expected system behavior under stress conditions"}, "MeasurementMetrics": {"type": "array", "items": {"type": "string"}, "description": "List of metrics to be measured during the stress test"}, "ActualResults": {"type": "string", "description": "The actual system behavior observed after stress test execution (to be filled later)"}, "CanBeAutomated": {"type": "boolean", "description": "Flag indicating whether this test case can be automated", "default": false}, "Priority": {"type": "string", "enum": ["Low", "Medium", "High", "Critical"], "description": "Priority level for this test case", "default": "Medium"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "StressConditions": {"Label": "Stress Conditions", "display_type": "rich_text"}, "TestProcedure": {"Label": "Test Procedure", "display_type": "rich_text"}, "ExpectedBehavior": {"Label": "Expected Behavior", "display_type": "rich_text"}, "MeasurementMetrics": {"Label": "Measurement Metrics", "display_type": "rich_text"}, "ActualResults": {"Label": "Actual Results", "display_type": "rich_text"}, "CanBeAutomated": {"Label": "Can Be Automated", "display_type": "boolean"}, "TestLevel": {"Label": "Test Level", "display_type": "dropdown"}, "ComponentsInvolved": {"Label": "Components Involved", "display_type": "multiselect"}, "ExternalSystemsInvolved": {"Label": "External Systems Involved", "display_type": "multiselect"}, "InterfacesToTest": {"Label": "Interfaces to Test", "display_type": "multiselect"}, "RelatedUserStories": {"Label": "Related User Stories", "display_type": "multiselect"}, "Priority": {"Label": "Priority", "display_type": "select"}}}, "StabilityTest": {"description": "Represents a stability test case for evaluating system reliability over an extended period", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'StabilityTest' for this entity"}, "Title": {"type": "string", "description": "A brief, descriptive title for the stability test"}, "Description": {"type": "string", "description": "Detailed description of the stability test, including its purpose and scope"}, "Tags": {"type": "array", "items": {"type": "string"}, "description": "List of tags associated with this test case"}, "TestLevel": {"type": "string", "enum": ["Unit", "Component", "System"], "description": "The level at which this test case operates (Unit, Component, or System)", "default": "System"}, "ComponentsInvolved": {"type": "array", "items": {"type": "integer"}, "description": "IDs of components involved in this test (particularly for System level tests)"}, "ExternalSystemsInvolved": {"type": "array", "items": {"type": "string"}, "description": "Names or identifiers of external systems involved in this test case"}, "InterfacesToTest": {"type": "array", "items": {"type": "integer"}, "description": "IDs of interfaces that need to be tested in this test case"}, "RelatedUserStories": {"type": "array", "items": {"type": "integer"}, "description": "IDs of user stories that this test case verifies"}, "Duration": {"type": "string", "description": "The duration for which the stability test should run"}, "TestConditions": {"type": "string", "description": "Description of the conditions under which the stability test should be performed"}, "TestProcedure": {"type": "string", "description": "Detailed procedure for executing the stability test"}, "ExpectedBehavior": {"type": "string", "description": "The expected system behavior over the duration of the stability test"}, "MeasurementMetrics": {"type": "array", "items": {"type": "string"}, "description": "List of metrics to be measured during the stability test"}, "ActualResults": {"type": "string", "description": "The actual system behavior observed after stability test execution (to be filled later)"}, "CanBeAutomated": {"type": "boolean", "description": "Flag indicating whether this test case can be automated", "default": false}, "Priority": {"type": "string", "enum": ["Low", "Medium", "High", "Critical"], "description": "Priority level for this test case", "default": "Medium"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "Duration": {"Label": "Duration"}, "TestConditions": {"Label": "Test Conditions", "display_type": "rich_text"}, "TestProcedure": {"Label": "Test Procedure", "display_type": "rich_text"}, "ExpectedBehavior": {"Label": "Expected Behavior", "display_type": "rich_text"}, "MeasurementMetrics": {"Label": "Measurement Metrics", "display_type": "rich_text"}, "ActualResults": {"Label": "Actual Results", "display_type": "rich_text"}, "CanBeAutomated": {"Label": "Can Be Automated", "display_type": "boolean"}, "TestLevel": {"Label": "Test Level", "display_type": "dropdown"}, "ComponentsInvolved": {"Label": "Components Involved", "display_type": "multiselect"}, "ExternalSystemsInvolved": {"Label": "External Systems Involved", "display_type": "multiselect"}, "InterfacesToTest": {"Label": "Interfaces to Test", "display_type": "multiselect"}, "RelatedUserStories": {"Label": "Related User Stories", "display_type": "multiselect"}, "Priority": {"Label": "Priority", "display_type": "select"}}}, "InteroperabilityTest": {"description": "Represents an interoperability test case for evaluating system compatibility with other systems or components", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'InteroperabilityTest' for this entity"}, "Title": {"type": "string", "description": "A brief, descriptive title for the interoperability test"}, "Description": {"type": "string", "description": "Detailed description of the interoperability test, including its purpose and scope"}, "Tags": {"type": "array", "items": {"type": "string"}, "description": "List of tags associated with this test case"}, "SystemsInvolved": {"type": "array", "items": {"type": "string"}, "description": "List of systems involved in the interoperability test"}, "TestProcedure": {"type": "string", "description": "Detailed procedure for executing the interoperability test"}, "ExpectedInteraction": {"type": "string", "description": "Expected interaction between systems during the test"}, "Standards": {"type": "array", "items": {"type": "string"}, "description": "List of standards or protocols relevant to this interoperability test"}, "TestLevel": {"type": "string", "enum": ["Unit", "Component", "System"], "description": "The level at which this test case operates (Unit, Component, or System)", "default": "System"}, "ComponentsInvolved": {"type": "array", "items": {"type": "integer"}, "description": "IDs of components involved in this test (particularly for System level tests)"}, "InterfacesToTest": {"type": "array", "items": {"type": "integer"}, "description": "IDs of interfaces that need to be tested in this test case"}, "ExternalSystemsInvolved": {"type": "array", "items": {"type": "string"}, "description": "Names or identifiers of external systems involved in this test case"}, "CanBeAutomated": {"type": "boolean", "description": "Flag indicating whether this test case can be automated", "default": false}, "Priority": {"type": "string", "enum": ["Low", "Medium", "High", "Critical"], "description": "Priority level for this test case", "default": "Medium"}}, "ui_metadata": {"Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "Type": {"Label": "Type"}, "Tags": {"Label": "Tags", "display_type": "multiselect"}, "SystemsInvolved": {"Label": "Systems Involved", "display_type": "multiselect"}, "TestProcedure": {"Label": "Test Procedure", "display_type": "rich_text"}, "ExpectedInteraction": {"Label": "Expected Interaction", "display_type": "rich_text"}, "Standards": {"Label": "Standards", "display_type": "multiselect"}, "CanBeAutomated": {"Label": "Can Be Automated", "display_type": "boolean"}, "TestLevel": {"Label": "Test Level", "display_type": "dropdown"}, "ComponentsInvolved": {"Label": "Components Involved", "display_type": "multiselect"}, "InterfacesToTest": {"Label": "Interfaces to Test", "display_type": "multiselect"}, "ExternalSystemsInvolved": {"Label": "External Systems Involved", "display_type": "multiselect"}, "Priority": {"Label": "Priority", "display_type": "select"}}}, "ArchitecturalRequirement": {"description": "Represents high-level system requirements that influence the overall architecture and design of the software. It captures both functional and non-functional aspects that have significant impact on the system's structure and behavior.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'ArchitecturalRequirement' for this entity"}, "Title": {"type": "string", "description": "Provide a concise title relavant to the Project"}, "Description": {"type": "string", "description": "Brief Description about the project and its Architectural considerations."}, "change_log": {"type": "string", "description": "JSON string containing history of changes made to this requirement root"}, "user_inputs": {"type": "string", "description": "JSON string containing record of user inputs during configuration"}, "change_reason": {"type": "string", "description": "Reason for the last change"}, "changes_needed": {"type": "boolean", "description": "Flag indicating whether changes were needed during reconfiguration"}, "configuration_state": {"type": "string", "description": "Current state of the architectural requirement's configuration in the project management process"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "functional_requirements": {"Label": "Functional Requirements", "display_type": "rich_text"}, "architectural_requirements": {"Label": "Non-Functional Requirements", "display_type": "rich_text"}, "configuration_state": {"Label": "Configuration State", "hidden": true}}, "relationships": {"HAS_CHILD": {"types": ["FunctionalRequirement", "ArchitecturalRequirementNode"], "description": "Child requirements (both functional and architectural) of this collection"}}}, "FunctionalRequirement": {"description": "Represents a specific functional requirement derived from the architectural requirements.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'FunctionalRequirement' for this entity"}, "Title": {"type": "string", "description": "Concise, descriptive title of the functional requirement"}, "Description": {"type": "string", "description": "Detailed description of the functional requirement"}, "RelatedUserStoryIDs": {"type": "array", "items": {"type": "integer"}, "description": "Array of user story IDs (integers) related to this requirement"}}, "ui_metadata": {"Type": {"Label": "Type"}, "REQ_ID": {"Label": "Requirement ID"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "RelatedUserStoryIDs": {"Label": "Related User Story IDs", "display_type": "array"}}}, "NonFunctionalRequirement": {"description": "Represents a specific Non Fucntional requirement derived from the overall architectural requirements.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'NonFunctionalRequirement' for this entity"}, "Title": {"type": "string", "description": "Concise, descriptive title of the architectural requirement"}, "Description": {"type": "string", "description": "Detailed description of the architectural requirement"}}, "ui_metadata": {"Type": {"Label": "Type"}, "REQ_ID": {"Label": "Requirement ID"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}}}, "RELATES_TO": {"description": "Represents a relationship between a requirement (functional or architectural) and one or more user stories.", "properties": {"requirement_id": {"type": "string", "description": "The ID of the requirement (REQ-F001 or REQ-NF001 format)"}, "user_story_ids": {"type": "array", "items": {"type": "string"}, "description": "Array of user story IDs related to this requirement"}, "type": {"type": "string", "description": "Type of the relationship, always 'relatesTo' for this entity"}, "description": {"type": "string", "description": "Description of how the requirement relates to the user stories"}}}, "Container": {"description": "A container represents a deployable unit of software that encapsulates a set of components and their relationships.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Container' for this entity"}, "Title": {"type": "string", "description": "A descriptive name for the container"}, "Description": {"type": "string", "description": "Detailed description of the container's purpose and functionality"}, "ContainerType": {"type": "string", "description": "The type of container (e.g. : `internal` or `external` based of the conclusion from prompt provided)"}, "ContainerCategory": {"type": "string", "description": "The category of the container (e.g., frontend, backend, middleware)"}, "Selected_Platform": {"type": "string", "enum": ["backend", "web", "mobile", "database"], "description": "Choose the appropriate value for the container. It must be exact and case-sensitive. (Hint: 'web' refers to the frontend.)"}, "Selected_Tech_Stack": {"type": "string", "enum": ["flask", "<PERSON><PERSON><PERSON>", "django", "express", "flutter", "android", "kotlin", "PostgreSQL", "MySQL", "MongoDB", "SQLite", "react", "angular", "astro", "nextjs", "qwik", "nuxt", "remix", "remotion", "slidev", "svelte", "vite", "vue"], "description": "Choose appropriately based of the Selected_Platform  Choose only one from the list with exact value it's case-sensitive"}, "UserInteractions": {"type": "string", "description": "Description of how users interact with this container"}, "ExternalSystemInteractions": {"type": "string", "description": "Description of interactions with external systems"}, "ImplementedRequirementIDs": {"type": "string", "description": "List of requirement IDs implemented by this container"}, "Repository_Name": {"type": "string", "description": "Name of the repository containing the container's code"}, "ContainerDiagram": {"type": "string", "description": "Diagram or visualization of the container's structure"}, "HasDatabase": {"type": "boolean", "description": "Indicates if this container connects to a database"}, "change_log": {"type": "string", "description": "JSON string containing history of changes made to this container"}, "user_inputs": {"type": "string", "description": "JSON string containing record of user inputs during configuration"}, "change_reason": {"type": "string", "description": "Reason for the last change"}, "changes_needed": {"type": "boolean", "description": "Flag indicating whether changes were needed during reconfiguration"}}, "ui_metadata": {"Type": {"Label": "Type", "order": 1, "display_type": "text"}, "Title": {"Label": "Title", "order": 2, "display_type": "text"}, "Description": {"Label": "Description", "order": 3, "display_type": "rich_text"}, "ContainerType": {"Label": "Container Type", "order": 4, "display_type": "text"}, "ContainerCategory": {"Label": "Container Category", "order": 5, "display_type": "text"}, "Selected_Tech_Stack": {"Label": "Technology Stack", "order": 6, "display_type": "text"}, "Selected_Platform": {"Label": "Platform", "order": 7, "display_type": "text"}, "UserInteractions": {"Label": "User Interactions", "order": 8, "display_type": "rich_text"}, "ExternalSystemInteractions": {"Label": "External System Interactions", "order": 9, "display_type": "rich_text"}, "ImplementedRequirementIDs": {"Label": "Implemented Requirements", "order": 10, "display_type": "text"}, "Repository_Name": {"Label": "Repository Name", "order": 11, "display_type": "text"}, "ContainerDiagram": {"Label": "Container <PERSON><PERSON><PERSON>", "order": 12, "display_type": "mermaid_chart"}, "HasDatabase": {"Label": "Has Database", "order": 13, "display_type": "boolean", "description": "Indicates if this container connects to a database"}}, "relationships": {"hasChild": {"types": ["Architecture", "Component", "Interface", "Database"], "description": "Child nodes of the Container"}}}, "Database": {"description": "A database node represents a database system used within a container, including its configuration and schema details.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Database' for this entity"}, "Title": {"type": "string", "description": "A descriptive name for the database"}, "Description": {"type": "string", "description": "Detailed description of the database's purpose and functionality"}, "DatabaseType": {"type": "string", "enum": ["postgresql", "mysql", "mongodb", "redis", "cassandra", "elasticsearch"], "description": "The type of database system"}, "DatabaseVersion": {"type": "string", "description": "Version of the database system"}, "ConnectionString": {"type": "string", "description": "Database connection string"}, "Host": {"type": "string", "description": "Database host address"}, "Port": {"type": "string", "description": "Database port number"}, "DatabaseName": {"type": "string", "description": "Name of the database"}, "SchemaName": {"type": "string", "description": "Name of the database schema"}, "AuthenticationType": {"type": "string", "enum": ["password", "certificate", "token", "o<PERSON>h"], "description": "Type of authentication used"}, "Username": {"type": "string", "description": "Database username"}, "PasswordRef": {"type": "string", "description": "Reference to stored password"}, "SSLEnabled": {"type": "boolean", "description": "Whether SSL is enabled for the connection"}, "ConnectionPoolSize": {"type": "string", "description": "Size of the connection pool"}, "QueryTimeout": {"type": "string", "description": "Query timeout in seconds"}, "EntityDefinitions": {"type": "string", "description": "JSON schema defining database entities"}, "RelationshipMappings": {"type": "string", "description": "JSON schema defining entity relationships"}, "EntityRelationshipDiagram": {"type": "string", "description": "Diagram showing entity relationships"}, "DatabaseSchema": {"type": "string", "description": "SQL schema definition"}, "MigrationScripts": {"type": "string", "description": "Database migration scripts"}, "Indexes": {"type": "string", "description": "Database indexes configuration"}, "Constraints": {"type": "string", "description": "Database constraints"}, "ExternalDatabaseContainerRef": {"type": "string", "description": "Reference to external database container"}, "UsagePattern": {"type": "string", "enum": ["read-only", "write-only", "read-write", "cache", "queue"], "description": "Pattern of database usage"}, "AccessLevel": {"type": "string", "enum": ["basic", "standard", "advanced", "admin"], "description": "Level of database access"}}, "ui_metadata": {"Type": {"Label": "Type", "order": 1, "display_type": "text"}, "Title": {"Label": "Title", "order": 2, "display_type": "text"}, "Description": {"Label": "Description", "order": 3, "display_type": "rich_text"}, "DatabaseType": {"Label": "Database Type", "order": 4, "display_type": "select", "options": ["postgresql", "mysql", "mongodb", "redis", "cassandra", "elasticsearch"]}, "DatabaseVersion": {"Label": "Database Version", "order": 5, "display_type": "text"}, "ConnectionString": {"Label": "Connection String", "order": 6, "display_type": "text"}, "Host": {"Label": "Host", "order": 7, "display_type": "text"}, "Port": {"Label": "Port", "order": 8, "display_type": "text"}, "DatabaseName": {"Label": "Database Name", "order": 9, "display_type": "text"}, "SchemaName": {"Label": "Schema Name", "order": 10, "display_type": "text"}, "AuthenticationType": {"Label": "Authentication Type", "order": 11, "display_type": "select", "options": ["password", "certificate", "token", "o<PERSON>h"]}, "Username": {"Label": "Username", "order": 12, "display_type": "text"}, "PasswordRef": {"Label": "Password Reference", "order": 13, "display_type": "text"}, "SSLEnabled": {"Label": "SSL Enabled", "order": 14, "display_type": "boolean"}, "ConnectionPoolSize": {"Label": "Connection Pool Size", "order": 15, "display_type": "text"}, "QueryTimeout": {"Label": "Query Timeout", "order": 16, "display_type": "text"}, "EntityDefinitions": {"Label": "Entity Definitions", "order": 17, "display_type": "json_schema", "schema_type": "entity_definitions"}, "RelationshipMappings": {"Label": "Relationship Mappings", "order": 18, "display_type": "json_schema", "schema_type": "relationship_mappings"}, "EntityRelationshipDiagram": {"Label": "Entity Relationship Diagram", "order": 19, "display_type": "mermaid_chart"}, "DatabaseSchema": {"Label": "Database Schema", "order": 20, "display_type": "code", "language": "sql"}, "MigrationScripts": {"Label": "Mi<PERSON> Scripts", "order": 21, "display_type": "code", "language": "sql"}, "Indexes": {"Label": "Indexes", "order": 22, "display_type": "code", "language": "sql"}, "Constraints": {"Label": "Constraints", "order": 23, "display_type": "code", "language": "sql"}, "ExternalDatabaseContainerRef": {"Label": "External Database Container Reference", "order": 24, "display_type": "text"}, "UsagePattern": {"Label": "Usage Pattern", "order": 25, "display_type": "select", "options": ["read-only", "write-only", "read-write", "cache", "queue"]}, "AccessLevel": {"Label": "Access Level", "order": 26, "display_type": "select", "options": ["basic", "standard", "advanced", "admin"]}}, "relationships": {"hasParent": {"types": ["Container"], "description": "Parent container of the database"}}}, "USES": {"description": "Represents a dependency relationship where one container uses services provided by another container", "attributes": {"type": {"type": "string", "description": "Type of the relationship, always 'USES' for this entity"}, "name": {"type": "string", "description": "Descriptive name of the service usage"}, "description": {"type": "string", "description": "Detailed description of how the Source uses the target's service"}, "source": {"type": "integer", "description": "ID of the node initiating the USES relationship"}, "target": {"type": "integer", "description": "ID of the external container being used"}, "technology": {"type": "string", "description": "The technology or protocol used for the interaction (e.g., 'HTTP/HTTPS', 'SQL', 'gRPC')"}}, "ui_metadata": {"name": {"Label": "Service Usage Name", "display_type": "text"}, "description": {"Label": "Usage Description", "display_type": "rich_text"}, "consumer": {"Label": "Consumer Container", "display_type": "reference"}, "provider": {"Label": "Provider Container", "display_type": "reference"}}}, "C4_Component": {"description": "Represents a component in the C4 model, typically part of a container.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Component' for this entity"}, "Title": {"type": "string", "description": "Name of the component"}, "Description": {"type": "string", "description": "Description of the component's responsibilities"}, "Technology": {"type": "string", "description": "Technology used in this component"}, "ComponentPath": {"type": "string", "description": "Components's root path within Container"}, "ComponentDiagram": {"type": "string", "description": "Mermaid representation of the C4 Component diagram"}, "change_log": {"type": "string", "description": "JSON string containing history of changes made to this requirement root"}, "user_inputs": {"type": "string", "description": "JSON string containing record of user inputs during configuration"}, "change_reason": {"type": "string", "description": "Reason for the last change"}, "changes_needed": {"type": "boolean", "description": "Flag indicating whether changes were needed during reconfiguration"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "Technology": {"Label": "Technology"}, "C4Diagram": {"Label": "C4 Component Diagram", "display_type": "plantuml_chart"}}, "relationships": {"USES": {"types": ["Component"], "description": "Other components this component interacts with"}, "CONTAINS": {"types": ["Class", "Interface"], "description": "Classes and interfaces within this component"}}}, "Class": {"description": "Represents a class within a component in the C4 model.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Class' for this entity"}, "Title": {"type": "string", "description": "Name of the class"}, "Description": {"type": "string", "description": "Description of the class's purpose and responsibilities"}, "Attributes": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "visibility": {"type": "string"}}}, "description": "List of attributes (fields) in the class"}, "Methods": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "returnType": {"type": "string"}, "parameters": {"type": "array", "items": {"type": "string"}}, "visibility": {"type": "string"}}}, "description": "List of methods in the class"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Class Name"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "Attributes": {"Label": "Attributes", "display_type": "table"}, "Methods": {"Label": "Methods", "display_type": "table"}}, "relationships": {"IMPLEMENTS": {"types": ["Interface"], "description": "Interfaces implemented by this class"}, "EXTENDS": {"types": ["Class"], "description": "Parent class that this class extends"}, "USES": {"types": ["Class", "Interface"], "description": "Other classes or interfaces used by this class"}}}, "C4_Interface": {"description": "Represents an interface within a component in the C4 model.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Interface' for this entity"}, "Title": {"type": "string", "description": "Name of the interface"}, "Description": {"type": "string", "description": "Description of the interface's purpose and contract"}, "Methods": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "returnType": {"type": "string"}, "parameters": {"type": "array", "items": {"type": "string"}}}}, "description": "List of methods defined in the interface"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Interface Name"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "Methods": {"Label": "Methods", "display_type": "table"}}, "relationships": {"EXTENDS": {"types": ["Interface"], "description": "Parent interfaces that this interface extends"}, "IMPLEMENTED_BY": {"types": ["Class"], "description": "Classes that implement this interface"}}}, "CONTAINS": {"description": "Represents a containment relationship in the C4 model, typically between a Container and its Components.", "attributes": {"type": {"type": "string", "description": "Type of the relationship, always 'CONTAINS' for this entity"}, "target": {"type": "string", "description": "The title of the target Component contained within the Container"}, "description": {"type": "string", "description": "Description of the containment relationship"}}}, "INTERACTS_WITH": {"description": "Represents an interaction between the system and an external entity (user or external system) in the C4 model.", "attributes": {"type": {"type": "string", "description": "Type of the relationship, always 'Interacts_with' for this entity"}, "description": {"type": "string", "description": "Description of the interaction"}, "direction": {"type": "string", "description": "Direction of the interaction (e.g., 'inbound', 'outbound', 'bidirectional')"}, "protocol": {"type": "string", "description": "Protocol used for the interaction (e.g., 'HTTP', 'HTTPS', 'TCP', 'UDP')"}, "frequency": {"type": "string", "description": "Frequency of the interaction (e.g., 'real-time', 'daily', 'on-demand')"}}}, "ArchitectureRoot": {"description": "Represents the top-level node of the system's architectural design, serving as the entry point for all architectural components and decisions.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'ArchitectureRoot' for this entity"}, "Title": {"type": "string", "description": "Concise title for the overall system architecture, typically reflecting the project name or main system function"}, "Description": {"type": "string", "description": "Comprehensive overview of the system architecture, including its main components, design principles, and high-level structure"}, "Design_Details": {"type": "string", "description": "Detailed explanation of key architectural decisions, patterns, and strategies employed in the system design"}, "MermaidChart": {"type": "string", "description": "Mermaid chart representation of the high-level system architecture, visualizing main components and their relationships"}, "configuration_state": {"type": "string", "description": "Current state of the architecture configuration process (e.g., 'Draft', 'In Review', 'Approved')"}, "autoconfig_state": {"type": "string", "description": "State of automatic configuration processes for the architecture, if applicable"}, "autoconfig_design_details_state": {"type": "string", "description": "State of automatic configuration for design details, tracking progress of AI-assisted design processes"}, "autoconfig_startdesign_state": {"type": "string", "description": "State of initial design auto-configuration, indicating progress of AI-initiated architectural design"}, "change_log": {"type": "string", "description": "JSON string containing history of changes made to this requirement root"}, "user_inputs": {"type": "string", "description": "JSON string containing record of user inputs during configuration"}, "change_reason": {"type": "string", "description": "Reason for the last change"}, "changes_needed": {"type": "boolean", "description": "Flag indicating whether changes were needed during reconfiguration"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "Design_Details": {"Label": "Design Details", "display_type": "rich_text"}, "MermaidChart": {"Label": "Architecture Diagram", "display_type": "mermaid_chart"}, "configuration_state": {"Label": "Configuration State", "hidden": true}, "autoconfig_state": {"Label": "Auto-configuration State", "hidden": true}, "autoconfig_design_details_state": {"Label": "Design Details Auto-configuration State", "hidden": true}, "autoconfig_startdesign_state": {"Label": "Initial Design Auto-configuration State", "hidden": true}}, "relationships": {"hasChild": {"types": ["Architecture", "ArchitecturalRequirement"], "description": "Child architecture nodes, representing major subsystems or components of the overall system"}}}, "Architecture": {"description": "Represents a specific component or subsystem within the overall system architecture, detailing its structure, behavior, and interactions with other components. Each Architecture node must have a unique ID.", "attributes": {"Type": {"type": "string", "description": "Can be 'Component','SubComponent' or 'ArchitectureRoot' based on the type of the node"}, "ID": {"type": "string", "description": "Unique identifier for the component. Must be in the format 'NEW-ARCH-n' for new components"}, "Title": {"type": "string", "description": "Concise, descriptive title of the architectural component or subsystem"}, "Description": {"type": "string", "description": "Comprehensive explanation of the component's purpose, functionality, and role within the larger system"}, "Design_Details": {"type": "string", "description": "Detailed description of the component's internal structure, key algorithms, data flow, and design patterns used"}, "Root_Folder": {"type": "string", "description": "Root folder name based on component functionality"}, "Functionality": {"type": "string", "description": "Detailed description of the component's core functions, capabilities, and services it provides to other parts of the system"}, "ComponentDiagram": {"type": "string", "description": "Mermaid chart representation of component and its relationship with other components and external systems"}, "ExternalSystemInteractions": {"type": "string", "description": "Documents external systems this component interacts with and the nature of those interactions."}, "ImplementedRequirementIDs": {"type": "array", "items": {"type": "integer"}, "description": "Array of requirement node IDs implemented by this Component"}, "IsArchitecturalLeaf": {"type": "string", "description": "Indicates if this is a leaf node in the architectural hierarchy ('yes' or 'no'), determining if further decomposition is needed"}, "configuration_state": {"Label": "Configuration State", "hidden": true}, "design_detail_state": {"Label": "Design Details State", "hidden": true}, "autoconfig_state": {"Label": "Auto-configuration State", "hidden": true}, "autoconfig_design_details_state": {"Label": "Design Details Auto-configuration State", "hidden": true}, "autoconfig_startdesign_state": {"Label": "Initial Design Auto-configuration State", "hidden": true}, "change_log": {"type": "string", "description": "JSON string containing history of changes made to this requirement root"}, "user_inputs": {"type": "string", "description": "JSON string containing record of user inputs during configuration"}, "change_reason": {"type": "string", "description": "Reason for the last change"}, "changes_needed": {"type": "boolean", "description": "Flag indicating whether changes were needed during reconfiguration"}}, "ui_metadata": {"Title": {"Label": "Title", "display_type": "title"}, "Type": {"Label": "Type", "display_type": "label"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "Design_Details": {"Label": "Design Details", "display_type": "rich_text"}, "Functionality": {"Label": "Functionality", "display_type": "rich_text"}, "ComponentDiagram": {"Label": "Component Structure", "display_type": "mermaid_chart"}, "ExternalSystemInteractions": {"Label": "External System Interactions", "display_type": "rich_text"}, "ImplementedRequirementIDs": {"Label": "Implemented Requirements ID's", "display_type": "array"}, "Root_Folder": {"Label": "Root Folder", "display_type": "text"}, "IsArchitecturalLeaf": {"Label": "Is Architectural Leaf", "display_type": "text"}}, "relationships": {"hasChild": {"types": ["Architecture", "Interface"], "description": "Child architectural components or interfaces provided by this component"}, "interfacesWith": {"types": ["Component", "Sub-Component"], "description": "Other architectural components this component interacts with, detailing system integrations and data flow"}}}, "interfacesWith": {"description": "Represents an interface relationship between two architectural components, defining how they interact and exchange information.", "attributes": {"source": {"type": "string", "description": "An identifier representing the source component for this interface. Use NEW-ARCH-* for new components using the ID field of the new component {Architecture.ID}; Source component is the component that provides services, functionalities, or data."}, "target": {"type": "string", "description": "An identifier representing the target component for this interface. Use NEW-ARCH-* for new components using the ID field of the new component {Architecture.ID}; Target component is the component that consumes services, functionalities, or data."}, "name": {"type": "string", "description": "Name of the interface, clearly indicating its purpose or the nature of the interaction"}, "interfaceType": {"type": "string", "description": "The type of interface communication pattern being used"}, "type": {"type": "string", "description": "Type of the relationship, always 'interfacesWith' for this entity"}, "description": {"type": "string", "description": "Detailed description of the interface, including its purpose, the type of data or control flow, and any constraints or protocols used"}}}, "ArchitectureLeaf": {"description": "Represents a distinct, modular part of the system that encapsulates a set of related functionality or data, designed to interact with other components through well-defined interfaces.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Component' for this entity"}, "Title": {"type": "string", "description": "Concise, descriptive title of the component, clearly indicating its main function or purpose within the system"}, "ID": {"type": "string", "description": "Unique identifier for the component. Must be in the format 'NEW-ARCH-n' for new components", "required": true}, "Tech_Stack_Choices": {"type": "string", "description": "List of technology stack options being considered for this component"}, "MermaidChart": {"type": "string", "description": "A Mermaid chart representation of the component's relationships and structure"}, "Description": {"type": "string", "description": "Comprehensive explanation of the component's role, responsibilities, and how it fits into the overall system architecture"}, "Design_Details": {"type": "string", "description": "Detailed description of the component's internal structure, key algorithms, data flow, and design patterns used"}, "Recommended_Tech_Stack": {"type": "string", "description": "The preferred or chosen technology stack for implementing this component, with rationale for the selection"}, "Functionality": {"type": "string", "description": "Detailed description of the component's core functions, capabilities, and services it provides to other parts of the system"}, "IsArchitecturalLeaf": {"type": "string", "description": "Indicates if this is a leaf node in the architectural hierarchy ('yes' or 'no'), determining if further decomposition is needed"}, "Root_Folder": {"type": "string", "description": "Root folder in the repository where the component's source code is stored, if applicable"}, "configuration_state": {"type": "string", "description": "Current state of the component's configuration in the architectural design process"}, "autoconfig_state": {"type": "string", "description": "State of automatic configuration processes for the component, if applicable"}, "autoconfig_design_details_state": {"type": "string", "description": "State of automatic configuration for design details of the component"}, "autoconfig_startdesign_state": {"type": "string", "description": "State of initial design auto-configuration for the component"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "Design_Details": {"type": "string", "description": "Detailed description of the component's internal structure, key algorithms, data flow, and design patterns used"}, "Tech_Stack_Choices": {"Label": "Technology Stack Options", "display_type": "rich_text"}, "MermaidChart": {"Label": "Component Structure", "display_type": "mermaid_chart"}, "Recommended_Tech_Stack": {"Label": "Recommended Technology Stack", "display_type": "rich_text"}, "Functionality": {"Label": "Functionality", "display_type": "rich_text"}, "Root_Folder": {"Label": "Root Folder"}, "IsArchitecturalLeaf": {"Label": "Is Architectural Leaf", "display_type": "text"}, "configuration_state": {"Label": "Configuration State", "hidden": true}, "autoconfig_state": {"Label": "Auto-configuration State", "hidden": true}, "autoconfig_design_details_state": {"Label": "Design Details Auto-configuration State", "hidden": true}, "autoconfig_startdesign_state": {"Label": "Initial Design Auto-configuration State", "hidden": true}}, "relationships": {"interfacesWith": {"types": ["Component"], "description": "Other components this component interacts with, defining the system's internal communication and data flow"}, "hasChild": {"types": ["Design", "Interface"], "description": "Design elements associated with this component, providing more detailed specifications for implementation"}}}, "Component": {"description": "Represents a distinct, modular part of the system that encapsulates a set of related functionality or data, designed to interact with other components through well-defined interfaces.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Component' for this entity"}, "Title": {"type": "string", "description": "Concise, descriptive title of the component, clearly indicating its main function or purpose within the system"}, "ID": {"type": "string", "description": "Unique identifier for the component. Must be in the format 'NEW-ARCH-n' for new components", "required": true}, "Tech_Stack_Choices": {"type": "string", "description": "List of technology stack options being considered for this component"}, "MermaidChart": {"type": "string", "description": "A Mermaid chart representation of the component's relationships and structure"}, "Description": {"type": "string", "description": "Comprehensive explanation of the component's role, responsibilities, and how it fits into the overall system architecture"}, "Design_Details": {"type": "string", "description": "Detailed description of the component's internal structure, key algorithms, data flow, and design patterns used"}, "Recommended_Tech_Stack": {"type": "string", "description": "The preferred or chosen technology stack for implementing this component, with rationale for the selection"}, "Functionality": {"type": "string", "description": "Detailed description of the component's core functions, capabilities, and services it provides to other parts of the system"}, "IsArchitecturalLeaf": {"type": "string", "description": "Indicates if this is a leaf node in the architectural hierarchy ('yes' or 'no'), determining if further decomposition is needed"}, "Root_Folder": {"type": "string", "description": "Root folder in the repository where the component's source code is stored, if applicable"}, "configuration_state": {"type": "string", "description": "Current state of the component's configuration in the architectural design process"}, "autoconfig_state": {"type": "string", "description": "State of automatic configuration processes for the component, if applicable"}, "autoconfig_design_details_state": {"type": "string", "description": "State of automatic configuration for design details of the component"}, "autoconfig_startdesign_state": {"type": "string", "description": "State of initial design auto-configuration for the component"}, "change_log": {"type": "string", "description": "JSON string containing history of changes made to this requirement root"}, "user_inputs": {"type": "string", "description": "JSON string containing record of user inputs during configuration"}, "change_reason": {"type": "string", "description": "Reason for the last change"}, "changes_needed": {"type": "boolean", "description": "Flag indicating whether changes were needed during reconfiguration"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "Design_Details": {"type": "string", "description": "Detailed description of the component's internal structure, key algorithms, data flow, and design patterns used"}, "Tech_Stack_Choices": {"Label": "Technology Stack Options", "display_type": "rich_text"}, "MermaidChart": {"Label": "Component Structure", "display_type": "mermaid_chart"}, "Recommended_Tech_Stack": {"Label": "Recommended Technology Stack", "display_type": "rich_text"}, "Functionality": {"Label": "Functionality", "display_type": "rich_text"}, "Root_Folder": {"Label": "Root Folder"}, "IsArchitecturalLeaf": {"Label": "Is Architectural Leaf", "display_type": "text"}, "configuration_state": {"Label": "Configuration State", "hidden": true}, "autoconfig_state": {"Label": "Auto-configuration State", "hidden": true}, "autoconfig_design_details_state": {"Label": "Design Details Auto-configuration State", "hidden": true}, "autoconfig_startdesign_state": {"Label": "Initial Design Auto-configuration State", "hidden": true}}, "relationships": {"interfacesWith": {"types": ["Component"], "description": "Other components this component interacts with, defining the system's internal communication and data flow"}, "hasChild": {"types": ["Design", "Interface"], "description": "Design elements associated with this component, providing more detailed specifications for implementation"}}}, "SubComponent": {"description": "Represents a distinct, modular part of the system that encapsulates a set of related functionality or data, designed to interact with other components or subcomponents through well-defined interfaces.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'SubComponent' for this entity"}, "Title": {"type": "string", "description": "Concise, descriptive title of the SubComponent, clearly indicating its main function or purpose within the system"}, "Description": {"type": "string", "description": "Comprehensive explanation of the SubComponent's role, responsibilities, and how it fits into the overall system architecture"}, "Technology": {"type": "array", "items": {"type": "string"}, "description": "List of potential technologies, frameworks, or platforms suitable for implementing this component"}, "Recommended_Tech_Stack": {"type": "string", "description": "The preferred or chosen technology stack for implementing this component, with rationale for the selection"}, "Functionality": {"type": "string", "description": "Detailed description of the component's core functions, capabilities, and services it provides to other parts of the system"}, "RepositoryStrategy": {"type": "string", "description": "Defines the overall repository strategy for the project. Can be 'Monorepo', 'MultiRepo', or 'HybridRepo'."}, "Repository_Name": {"type": "string", "description": "Name of the repository where the component's source code is stored, if applicable. Should be in the format 'org/repo' with the org being 'kavia' and repo being the name of the repository"}, "Root_Folder": {"type": "string", "description": "Root folder in the repository where the component's source code is stored, if applicable"}, "configuration_state": {"type": "string", "description": "Current state of the component's configuration in the architectural design process"}, "autoconfig_state": {"type": "string", "description": "State of automatic configuration processes for the component, if applicable"}, "autoconfig_design_details_state": {"type": "string", "description": "State of automatic configuration for design details of the component"}, "autoconfig_startdesign_state": {"type": "string", "description": "State of initial design auto-configuration for the component"}, "change_log": {"type": "string", "description": "JSON string containing history of changes made to this requirement root"}, "user_inputs": {"type": "string", "description": "JSON string containing record of user inputs during configuration"}, "change_reason": {"type": "string", "description": "Reason for the last change"}, "changes_needed": {"type": "boolean", "description": "Flag indicating whether changes were needed during reconfiguration"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "Tech_Stack_Choices": {"Label": "Technology Stack Options"}, "Recommended_Tech_Stack": {"Label": "Recommended Technology Stack"}, "Functionality": {"Label": "Functionality", "display_type": "rich_text"}, "RepositoryStrategy": {"Label": "Repository Strategy", "display_type": "rich_text"}, "Repository_Name": {"Label": "Repository Name"}, "Root_Folder": {"Label": "Root Folder"}, "configuration_state": {"Label": "Configuration State", "hidden": true}, "autoconfig_state": {"Label": "Auto-configuration State", "hidden": true}, "autoconfig_design_details_state": {"Label": "Design Details Auto-configuration State", "hidden": true}, "autoconfig_startdesign_state": {"Label": "Initial Design Auto-configuration State", "hidden": true}}, "relationships": {"interfacesWith": {"types": ["Component"], "description": "Other components this component interacts with, defining the system's internal communication and data flow"}, "hasChild": {"types": ["Design"], "description": "Design elements associated with this component, providing more detailed specifications for implementation"}}}, "Design": {"description": "Represents the detailed design specifications for a component or architectural element, including structural and behavioral aspects.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Design' for this entity"}, "Title": {"type": "string", "description": "Concise, descriptive title of the design specification, clearly indicating its focus and scope"}, "Description": {"type": "string", "description": "Comprehensive explanation of the design, including its purpose, key features, and how it addresses the requirements of the associated component or architectural element"}, "PurposeAndResponsibilities": {"type": "string", "description": "Detailed explanation of the component's purpose within the system and its primary responsibilities or functions"}, "InputsAndOutputs": {"type": "string", "description": "Detailed description of the inputs the component expects and the outputs it produces, including data types and formats"}, "FunctionalRequirements": {"type": "string", "description": "Detailed list and explanation of the functional requirements that the component must fulfill"}, "NonFunctionalRequirements": {"type": "string", "description": "Detailed list and explanation of the non-functional requirements (e.g., performance, scalability, security) that the component must meet"}, "Dependencies": {"type": "string", "description": "Comprehensive list of dependencies on other components or external systems, including the nature of each dependency"}, "SpecificationDescription": {"type": "string", "description": "Comprehensive design specification including purpose, inputs/outputs, requirements, and dependencies"}, "ComponentInteractionsDescription": {"type": "string", "description": "Description of component interaction patterns and communication flows"}, "BehaviorDescription": {"type": "string", "description": "Description of component's behaviors, algorithms, and state management"}, "ClassDiagramDescription": {"type": "string", "description": "Description of the class structure and relationships"}, "TestCasesDescription": {"type": "string", "description": "Description of test strategy and key test scenarios"}, "configuration_state": {"type": "string", "description": "Current state of the design's configuration in the development process"}, "behavior_config_state": {"type": "string", "description": "Current state of the design's behavior configuration in the development process"}, "component_interactions_config_state": {"type": "string", "description": "Current state of the design's component interactions configuration in the development process"}, "test_cases_config_state": {"type": "string", "description": "Current state of the design's test cases configuration in the development process"}, "class_diagrams_config_state": {"type": "string", "description": "Current state of the design's class diagram configuration in the development process"}, "classdiagram_state": {"type": "string", "description": "State of the class diagram configuration"}, "testcases_state": {"type": "string", "description": "State of test cases configuration"}, "specification_state": {"type": "string", "description": "State of the specification configuration"}, "component_interactions_state": {"type": "string", "description": "State of component interactions configuration"}, "change_log": {"type": "string", "description": "JSON string containing history of changes made to this requirement root"}, "user_inputs": {"type": "string", "description": "JSON string containing record of user inputs during configuration"}, "change_reason": {"type": "string", "description": "Reason for the last change"}, "changes_needed": {"type": "boolean", "description": "Flag indicating whether changes were needed during reconfiguration"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "PurposeAndResponsibilities": {"Label": "PurposeAndResponsibilities", "display_type": "rich_text"}, "InputsAndOutputs": {"Label": "InputsAndOutputs", "display_type": "rich_text"}, "FunctionalRequirements": {"Label": "FunctionalRequirements", "display_type": "rich_text"}, "NonFunctionalRequirements": {"Label": "NonFunctionalRequirements", "display_type": "rich_text"}, "Dependencies": {"Label": "Dependencies", "display_type": "rich_text"}, "configuration_state": {"Label": "Configuration State", "hidden": true}, "behavior_config_state": {"Label": "Behavior configuration State", "hidden": true}, "component_interactions_config_state": {"Label": "Component interactions configuration State", "hidden": true}, "test_cases_config_state": {"Label": "Test cases configuration State", "hidden": true}, "class_diagrams_config_state": {"Label": "Class diagrams configuration State", "hidden": true}, "classdiagram_state": {"Label": "Class Diagram State", "hidden": true}, "testcases_state": {"Label": "Test Cases State", "hidden": true}, "specification_state": {"Label": "Specification State", "hidden": true}, "component_interactions_state": {"Label": "Component Interactions State", "hidden": true}}, "relationships": {"hasChild": {"types": ["ClassDiagram", "SequenceDiagram", "StateMachineDiagram", "APIDoc", "UnitTest", "IntegrationTest", "PerformanceTest", "RobustnessTest", "Algorithm", "StateLogic"], "description": "Different types of diagrams and documentation associated with this design, providing visual and detailed representations of various aspects"}}}, "Interface": {"description": "Defines the public or private APIs for a component to achieve functionality and integration, specifying how different parts of the system interact.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Interface' for this entity"}, "Title": {"type": "string", "description": "Concise, descriptive title of the interface, clearly indicating its purpose and the components it connects"}, "Description": {"type": "string", "description": "Comprehensive explanation of the interface's role, including the services it provides and how it facilitates component interaction"}, "interfaceType": {"type": "string", "description": "The type of interface communication pattern being used"}, "FunctionalRequirements": {"type": "string", "description": "Detailed list of functional requirements that the interface must fulfill, specifying expected behaviors and capabilities"}, "TechnicalRequirements": {"type": "string", "description": "Specific technical requirements for the interface, including protocols, data formats, and any constraints"}, "DesignDetails": {"type": "string", "description": "Detailed Metadata for API design specifications for the interface, including method signatures, data structures, and communication patterns"}, "PublicAPIDetails": {"type": "string", "description": "Comprehensive Unified API design detail specifications that fulfil all the incoming interface requests. potentially using a standard format like OpenAPI for REST interfaces or Protocol Buffers for gRPC"}, "InterfaceDescription": {"type": "string", "description": "Comprehensive Formal description of the interface"}, "incoming_interfaces": {"type": "string", "description": "Specific incoming interface that including protocols, data formats, and any constraints"}, "source_node_id": {"type": "integer", "description": "ID of the source component that exposes this interface"}, "target_node_id": {"type": "integer", "description": "ID of the target component that consumes this interface"}, "design_details_state": {"type": "string", "description": "Current state of the interface's design details in the development process"}, "definition_state": {"type": "string", "description": "Current state of the interface's formal definition in the development process"}, "providesInterface": {"types": ["Interface"], "description": "Interfaces provided by this component"}, "Attributes": {"type": "string", "description": "List of attributes/properties of the interface with their types"}, "Methods": {"type": "string", "description": "List of methods/operations provided by the interface with their signatures"}, "change_log": {"type": "string", "description": "JSON string containing history of changes made to this requirement root"}, "user_inputs": {"type": "string", "description": "JSON string containing record of user inputs during configuration"}, "change_reason": {"type": "string", "description": "Reason for the last change"}, "changes_needed": {"type": "boolean", "description": "Flag indicating whether changes were needed during reconfiguration"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "FunctionalRequirements": {"Label": "Functional Requirements", "display_type": "rich_text"}, "TechnicalRequirements": {"Label": "Technical Requirements", "display_type": "rich_text"}, "PublicAPIDetails": {"Label": "Public Interfaces", "display_type": "api_doc"}, "DesignDetails": {"Label": "Design Details", "display_type": "rich_text"}, "InterfaceDescription": {"Label": "Interface Description", "display_type": "rich_text"}, "IncomingInterfaces": {"Label": "Incoming Interfaces", "display_type": "rich_text"}, "source_node_id": {"Label": "Source Component ID", "hidden": true}, "target_node_id": {"Label": "Target Component ID", "hidden": true}, "design_details_state": {"Label": "Design Details State", "hidden": true}, "definition_state": {"Label": "Definition State", "hidden": true}, "Attributes": {"Label": "Attributes", "display_type": "rich_text"}, "Methods": {"Label": "Methods", "display_type": "rich_text"}}, "relationships": {"hasChild": {"types": ["Route", "Method", "DataContract", "Protocol"], "description": "Elements that make up the interface, providing detailed specifications for each aspect of the interface"}}}, "Method": {"description": "Represents a specific function or operation within an interface, defining a particular capability or service provided by a component.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Method' for this entity"}, "Title": {"type": "string", "description": "Concise, descriptive name of the method, clearly indicating its purpose"}, "Signature": {"type": "string", "description": "Formal definition of the method, including its name, parameters, return type, and any exceptions"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Signature": {"Label": "Method Signature", "display_type": "code"}}}, "HttpRoute": {"description": "Represents a specific HTTP endpoint within a web API, defining the path, method, and behavior for handling a particular type of request.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'HttpRoute' for this entity"}, "Title": {"type": "string", "description": "Concise, descriptive name of the route, clearly indicating its purpose"}, "Route": {"type": "string", "description": "The URL path for this endpoint, including any path parameters"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Route": {"Label": "Route Path"}}}, "DataContract": {"description": "Defines the structure and constraints of data exchanged between components or services in the system, ensuring consistent data representation and interpretation.", "attributes": {"Title": {"type": "string", "description": "Concise, descriptive name of the data contract, clearly indicating its purpose or the entity it represents"}, "Type": {"type": "string", "description": "Type of the node, always 'DataContract' for this entity"}, "Description": {"type": "string", "description": "Comprehensive explanation of the data contract, including its role in the system and any important usage considerations"}, "Schema": {"type": "string", "description": "Formal definition of the data structure, typically in a standard format like JSON Schema, XML Schema, or Protocol Buffers"}, "DataContract": {"type": "string", "description": "Comprehensive definition and explanation of Data contract for data element in plain text"}}, "ui_metadata": {"Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "Schema": {"Label": "<PERSON><PERSON><PERSON>", "display_type": "code"}, "DataContract": {"Label": "Data Contract", "display_type": "rich_text"}}}, "CommunicationProtocol": {"description": "Defines the rules, formats, and procedures for communication between different components or services in the system, ensuring interoperability and efficient data exchange.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'CommunicationProtocol' for this entity"}, "Title": {"type": "string", "description": "Concise, descriptive name of the communication protocol"}, "ProtocolDescription": {"type": "string", "description": "Comprehensive explanation of the protocol, including its purpose, key features, and how it facilitates communication in the system"}, "ProtocolType": {"type": "string", "description": "The type or category of the protocol (e.g., RESTful, gRPC, WebSocket, MQTT)"}, "DataFormat": {"type": "string", "description": "The format used for data serialization (e.g., JSON, XML, Protocol Buffers)"}, "SecurityMeasures": {"type": "string", "description": "Description of security measures implemented in the protocol, such as encryption or authentication methods"}, "ErrorHandling": {"type": "string", "description": "Explanation of how errors and exceptions are handled and communicated within the protocol"}, "VersioningStrategy": {"type": "string", "description": "Description of how protocol versions are managed and backward compatibility is maintained"}, "PerformanceConsiderations": {"type": "string", "description": "Notes on performance characteristics, optimizations, or limitations of the protocol"}, "UsageContext": {"type": "string", "description": "Description of where and how this protocol is used within the system"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "ProtocolDescription": {"Label": "Protocol Description", "display_type": "rich_text"}, "ProtocolType": {"Label": "Protocol Type"}, "DataFormat": {"Label": "Data Format"}, "SecurityMeasures": {"Label": "Security Measures", "display_type": "rich_text"}, "ErrorHandling": {"Label": "Erro<PERSON>", "display_type": "rich_text"}, "VersioningStrategy": {"Label": "Versioning Strategy", "display_type": "rich_text"}, "PerformanceConsiderations": {"Label": "Performance Considerations", "display_type": "rich_text"}, "UsageContext": {"Label": "Usage Context", "display_type": "rich_text"}}}, "DesignSpecification": {"description": "Represents a detailed design specification for a component in the system architecture.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'DesignSpecification' for this entity"}, "Title": {"type": "string", "description": "Title of the current node"}, "Description": {"type": "string", "description": "Comprehensive description of the design for the component, detailing its structure, behavior, and key characteristics"}, "PurposeAndResponsibilities": {"type": "string", "description": "Detailed explanation of the component's purpose within the system and its primary responsibilities or functions"}, "InputsAndOutputs": {"type": "string", "description": "Detailed description of the inputs the component expects and the outputs it produces, including data types and formats"}, "FunctionalRequirements": {"type": "string", "description": "Detailed list and explanation of the functional requirements that the component must fulfill"}, "NonFunctionalRequirements": {"type": "string", "description": "Detailed list and explanation of the non-functional requirements (e.g., performance, scalability, security) that the component must meet"}, "Dependencies": {"type": "string", "description": "Comprehensive list of dependencies on other components or external systems, including the nature of each dependency"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "PurposeAndResponsibilities": {"Label": "Purpose and Responsibilities", "display_type": "rich_text"}, "InputsAndOutputs": {"Label": "Inputs and Outputs", "display_type": "rich_text"}, "FunctionalRequirements": {"Label": "Functional Requirements", "display_type": "rich_text"}, "NonFunctionalRequirements": {"Label": "Non-Functional Requirements", "display_type": "rich_text"}, "Dependencies": {"Label": "Dependencies", "display_type": "rich_text"}}, "relationships": {"specifiedFor": {"types": ["Architecture", "Component"], "description": "The architectural element or component that this design specification describes"}, "hasChild": {"types": ["ClassDiagram", "SequenceDiagram", "APIDoc"], "description": "Child nodes representing various aspects of the design specification"}}}, "Algorithm": {"description": "Represents an algorithmic detail of a component's behavior.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Algorithm' for this entity"}, "Title": {"type": "string", "description": "Title or name of the functionality implemented by this algorithm"}, "Details": {"type": "string", "description": "Algorithmic logic or pseudo-code for the component in plain text"}, "Description": {"type": "string", "description": "Description of what the sequence diagram represents"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Details": {"Label": "Algorithm Details", "display_type": "code"}, "Description": {"Label": "Description", "display_type": "rich_text"}}}, "WorkItemRoot": {"description": "Root node for work items in the project", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'WorkItemRoot' for this entity"}, "Title": {"type": "string", "description": "Title of the work item root"}, "Description": {"type": "string", "description": "Description of the work item root"}, "Details": {"type": "string", "description": "Additional details of the work item root"}, "Dependencies_state": {"type": "string", "description": "State of dependencies for work items"}, "change_log": {"type": "string", "description": "JSON string containing history of changes made to this requirement root"}, "user_inputs": {"type": "string", "description": "JSON string containing record of user inputs during configuration"}, "change_reason": {"type": "string", "description": "Reason for the last change"}, "changes_needed": {"type": "boolean", "description": "Flag indicating whether changes were needed during reconfiguration"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "Details": {"Label": "Additional Details", "display_type": "rich_text"}, "Dependencies_state": {"Label": "Dependencies State", "hidden": true}}, "relationships": {"hasChild": {"types": ["WorkItem"], "description": "Work items associated with this root"}}}, "WorkItem": {"description": "Represents a high-level unit of work in the project", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'WorkItem' for this entity"}, "Title": {"type": "string", "description": "Title of the work item"}, "Description": {"type": "string", "description": "Detailed description of the work item"}, "Priority": {"type": "string", "description": "Priority of the work item (e.g., 'High', 'Medium', 'Low')"}, "Status": {"type": "string", "description": "Current status of the work item"}, "Assignee": {"type": "array", "description": "Team or role assigned to the work item"}, "EstimatedDuration": {"type": "string", "description": "Estimated duration of the work item (e.g., '2 weeks', '1 month')"}, "Dependencies": {"type": "array", "items": {"type": "string"}, "description": "Titles of other WorkItems that this WorkItem depends on"}, "change_log": {"type": "string", "description": "JSON string containing history of changes made to this requirement root"}, "user_inputs": {"type": "string", "description": "JSON string containing record of user inputs during configuration"}, "change_reason": {"type": "string", "description": "Reason for the last change"}, "changes_needed": {"type": "boolean", "description": "Flag indicating whether changes were needed during reconfiguration"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "Priority": {"Label": "Priority"}, "Status": {"Label": "Status"}, "Assignee": {"Label": "Assignee"}, "EstimatedDuration": {"Label": "Estimated Duration"}, "Dependencies": {"Label": "Dependencies"}}, "relationships": {"dependsOn": {"types": ["WorkItem"], "description": "Other work items this work item depends on"}, "hasChild": {"types": ["WorkItem"], "description": "Child work items of this work item"}}}, "Dependency": {"description": "Represents a dependency between work items", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Dependency' for this entity"}, "Title": {"type": "string", "description": "Title of the dependency"}, "Description": {"type": "string", "description": "Description of the dependency"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}}}, "Protocol": {"description": "Represents a protocol in an interface", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Protocol' for this entity"}, "Title": {"type": "string", "description": "Title of the protocol"}, "ProtocolDescription": {"type": "string", "description": "Description of the protocol"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "ProtocolDescription": {"Label": "Protocol Description", "display_type": "rich_text"}}}, "ClassDiagram": {"description": "Represents a class diagram in the design, illustrating the structure of the system including classes, their attributes, methods, and relationships between classes.", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'ClassDiagram' for this entity"}, "Title": {"type": "string", "description": "Title of the current node"}, "Diagram": {"type": "string", "description": "Content of the class diagram in Mermaid chart format"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Diagram": {"Label": "Class Diagram", "display_type": "mermaid_chart"}}, "relationships": {"partOf": {"types": ["DesignSpecification", "Architecture"], "description": "The design specification or architecture component that this class diagram is a part of"}}}, "SequenceDiagram": {"description": "Represents a sequence diagram illustrating interactions between objects", "attributes": {"Title": {"type": "string", "description": "Title of the current node"}, "Type": {"type": "string", "description": "Type of the diagram, always 'Sequence' for sequence diagrams"}, "Description": {"type": "string", "description": "Description of what the sequence diagram represents"}, "Diagram": {"type": "string", "description": "Sequence diagram using Mermaid chart syntax in plain text"}}, "ui_metadata": {"Title": {"Label": "Title"}, "Type": {"Label": "Type"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "Diagram": {"Label": "Sequence Diagram", "display_type": "mermaid_chart"}}}, "StateMachineDiagram": {"description": "Represents a state machine diagram illustrating different states and transitions", "attributes": {"Title": {"type": "string", "description": "Name of the state variable or aspect being diagrammed"}, "Type": {"type": "string", "description": "Type of the diagram, always 'StateDiagram' for state machine diagrams"}, "Diagram": {"type": "string", "description": "State machine diagram using Mermaid chart syntax.Use actual line breaks in Diagram field, not escape characters"}}, "ui_metadata": {"Title": {"Label": "Title"}, "Type": {"Label": "Type"}, "Diagram": {"Label": "State Machine Diagram", "display_type": "mermaid_chart"}}}, "UnitTest": {"description": "Verifies the correctness of individual functions or methods within the component. Focus on testing isolated pieces of code, mock dependencies, and cover various input scenarios and edge cases.", "attributes": {"Title": {"type": "string", "description": "Title of the current node"}, "Type": {"type": "string", "description": "Type of the test case, always 'UnitTest' for unit tests"}, "Description": {"type": "string", "description": "Detailed explanation of the test scenario, including input conditions, test steps, and any mocked dependencies"}, "ExpectedResult": {"type": "string", "description": "Precise description of the expected outcome, including specific values or behaviors"}}, "ui_metadata": {"Title": {"Label": "Title"}, "Type": {"Label": "Type"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "ExpectedResult": {"Label": "Expected Result", "display_type": "rich_text"}}}, "IntegrationTest": {"description": "Tests the interaction between different components or services within the system. Focus on data flow, API contracts, and ensuring components work together as expected.", "attributes": {"Title": {"type": "string", "description": "Title of the current node"}, "Type": {"type": "string", "description": "Type of the test case, always 'IntegrationTest' for integration tests"}, "Description": {"type": "string", "description": "Detailed explanation of the integration scenario, including involved components, data flow, and test environment setup"}, "ExpectedResult": {"type": "string", "description": "Specific outcomes expected from the integration, including data consistency and system behavior"}}, "ui_metadata": {"Title": {"Label": "Title"}, "Type": {"Label": "Type"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "ExpectedResult": {"Label": "Expected Result", "display_type": "rich_text"}}}, "PerformanceTest": {"description": "Evaluates the system's performance under various conditions, including load and stress testing. Focus on response times, resource usage, and system stability under different loads.", "attributes": {"Title": {"type": "string", "description": "Title of the current node"}, "Type": {"type": "string", "description": "Type of the test case, always 'PerformanceTest' for performance tests"}, "Description": {"type": "string", "description": "Detailed explanation of the performance scenario, including load conditions, test duration, and any specific system configurations"}, "ExpectedResult": {"type": "string", "description": "Specific performance metrics and thresholds that the system should meet, such as response time, throughput, or resource utilization"}}, "ui_metadata": {"Title": {"Label": "Title"}, "Type": {"Label": "Type"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "ExpectedResult": {"Label": "Expected Result", "display_type": "rich_text"}}}, "DocumentationRoot": {"description": "Root node for documentation hierarchy, organizing different types of documentation for a component or system", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'DocumentationRoot' for this entity"}, "Title": {"type": "string", "description": "Primary title for the complete documentation set, clearly identifying the system, component, or feature being documented"}, "Description": {"type": "string", "description": "Comprehensive overview of the documentation's purpose, scope, and intended audience, including any key information about the documentation structure and organization"}, "DocumentationType": {"type": "string", "description": "Type of documentation"}, "Version": {"type": "string", "description": "Current version of the documentation, tracking major and minor revisions to maintain document history and versioning"}, "LastUpdated": {"type": "string", "description": "Timestamp of last update"}, "Context": {"type": "string", "description": "Comprehensive contextual information needed for documentation generation"}, "configuration_state": {"type": "string", "description": "Current state of configuration"}}, "ui_metadata": {"Type": {"Label": "Type", "display_type": "text"}, "Title": {"Label": "Title", "display_type": "text"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "DocumentationType": {"Label": "Documentation Type", "display_type": "text"}, "Version": {"Label": "Version", "display_type": "text"}, "LastUpdated": {"Label": "Last Updated", "display_type": "text"}, "configuration_state": {"Label": "Configuration State", "hidden": true}}, "relationships": {"hasChild": {"types": ["Sub_Section"], "description": "Documentation sections contained within this documentation root"}}}, "Documentation": {"description": "Represents API or component documentation, containing structured information about interfaces, endpoints, and usage", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Documentation' for this entity"}, "Title": {"type": "string", "description": "Title of the documentation"}, "Description": {"type": "string", "description": "Overview and purpose of the documentation"}, "Content": {"type": "string", "description": "Detailed content of the section of the Documentation"}, "Version": {"type": "string", "description": "Version of the documentation"}, "LastUpdated": {"type": "string", "description": "Timestamp of the last update to the documentation"}, "configuration_state": {"type": "string", "description": "Current state of documentation configuration"}}, "ui_metadata": {"Type": {"Label": "Type", "display_type": "text"}, "Title": {"Label": "Title", "display_type": "text"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "Content": {"Label": "Content", "display_type": "rich_text"}, "Version": {"Label": "Version", "display_type": "text"}, "LastUpdated": {"Label": "Last Updated", "display_type": "text"}, "configuration_state": {"Label": "Configuration State", "hidden": true}}}, "Sub_Section": {"description": "A section within a documentation document, containing specific content", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Sub_Section' for this entity"}, "Title": {"type": "string", "description": "Clear, descriptive title that accurately reflects the section's content within the documentation"}, "Description": {"type": "string", "description": "Description of the section"}, "SectionType": {"type": "string", "description": "Specific type of the section"}, "Order": {"type": "integer", "description": "Order of the section in the document"}, "Content": {"type": "string", "description": "Comprehensive and Detailed main content of the section, formatted appropriately for the section type and containing all relevant information"}, "configuration_state": {"type": "string", "description": "Current state of configuration"}}, "ui_metadata": {"Type": {"Label": "Type", "display_type": "text"}, "Title": {"Label": "Title", "display_type": "text"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "SectionType": {"Label": "Section Type", "display_type": "text"}, "Order": {"Label": "Order", "display_type": "number"}, "Content": {"Label": "Content", "display_type": "rich_text"}, "configuration_state": {"Label": "Configuration State", "hidden": true}}}, "User": {"description": "Represents a user in the system", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'User' for this entity"}, "Username": {"type": "string", "description": "Username of the user"}, "Email": {"type": "string", "description": "Email address of the user"}, "Name": {"type": "string", "description": "Full name of the user"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Username": {"Label": "Username"}, "Email": {"Label": "Email"}, "Name": {"Label": "Name"}}, "relationships": {"worksOn": {"types": ["Project"], "description": "Projects the user works on"}, "assignedTo": {"types": ["Epic", "UserStory", "WorkItem"], "description": "Work items assigned to the user"}}}, "SystemContext": {"description": "Represents the high-level system context view showing the system's interactions with users and external systems", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'SystemContext' for this entity"}, "Title": {"type": "string", "description": "Name of the system being described"}, "Description": {"type": "string", "description": "Detailed description of the system's purpose and main functionalities"}, "Users": {"type": "string", "description": "Comma-separated list of user types who interact with the system"}, "ExternalSystems": {"type": "string", "description": "Description of external systems that interact with this system"}, "SystemContextDiagram": {"type": "string", "description": "Mermaid diagram showing system context level interactions"}, "ContainerDiagram": {"type": "string", "description": "Mermaid diagram showing container level architecture"}, "ArchitectureDiagram": {"type": "string", "description": "D2 diagram showing internal architecture level interactions"}, "IntegrationDiagram": {"type": "string", "description": "D2 diagram showing integration level interactions"}, "DataArchitectureDiagram": {"type": "string", "description": "D2 diagram showing data architecture level interactions"}, "change_log": {"type": "string", "description": "JSON string containing history of changes made to this requirement root"}, "user_inputs": {"type": "string", "description": "JSON string containing record of user inputs during configuration"}, "change_reason": {"type": "string", "description": "Reason for the last change"}, "changes_needed": {"type": "boolean", "description": "Flag indicating whether changes were needed during reconfiguration"}, "configuration_state": {"type": "string", "description": "Current state of the system context configuration"}}, "ui_metadata": {"Type": {"Label": "Type", "display_type": "text"}, "Title": {"Label": "Title", "display_type": "text"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "Users": {"Label": "Users", "display_type": "text"}, "ExternalSystems": {"Label": "External Systems", "display_type": "rich_text"}, "SystemContextDiagram": {"Label": "System Context Diagram", "display_type": "mermaid_chart"}, "ArchitectureDiagram": {"Label": "Architecture Diagram", "display_type": "d2_diagram"}, "IntegrationDiagram": {"Label": "Integration Diagram", "display_type": "d2_diagram"}, "DataArchitectureDiagram": {"Label": "Data Architecture Diagram", "display_type": "d2_diagram"}, "ContainerDiagram": {"Label": "Container <PERSON><PERSON><PERSON>", "display_type": "mermaid_chart"}, "configuration_state": {"Label": "Configuration State", "hidden": true}}, "relationships": {"Contains": {"types": ["Container"], "description": "Child architectural Container provided by this SystemContext"}, "interfacesWith": {"types": ["Container"], "description": "Other containers this container interacts with"}}}, "Diagram": {"description": "Base type for different kinds of diagrams", "attributes": {"Type": {"type": "string", "description": "Type of the diagram"}, "Title": {"type": "string", "description": "Title of the diagram"}, "Description": {"type": "string", "description": "Description of what the diagram represents"}, "Diagram": {"type": "string", "description": "The actual diagram content"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "Diagram": {"Label": "Diagram", "display_type": "mermaid_chart"}}}, "DesignElement": {"description": "Represents design elements like algorithms and state logic", "attributes": {"Type": {"type": "string", "description": "Type of design element"}, "Title": {"type": "string", "description": "Title of the design element"}, "Details": {"type": "string", "description": "Implementation details"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Details": {"Label": "Details", "display_type": "rich_text"}}}, "StateDiagram": {"description": "Represents state transitions and behaviors", "attributes": {"Type": {"type": "string", "description": "Type of state diagram"}, "Title": {"type": "string", "description": "Title of the state diagram"}, "Diagram": {"type": "string", "description": "The actual state diagram in mermaid notation"}, "Description": {"type": "string", "description": "Description of what the sequence diagram represents"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Diagram": {"Label": "State Diagram", "display_type": "mermaid_chart"}, "Description": {"Label": "Description", "display_type": "rich_text"}}}, "Sequence": {"description": "Represents state transitions and behaviors", "attributes": {"Type": {"type": "string", "description": "Type of state diagram"}, "Title": {"type": "string", "description": "Title of the state diagram"}, "Diagram": {"type": "string", "description": "The actual state diagram in mermaid notation"}, "Description": {"type": "string", "description": "Description of what the sequence diagram represents"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Diagram": {"Label": "State Diagram", "display_type": "mermaid_chart"}, "Description": {"Label": "Description", "display_type": "rich_text"}}}, "RobustnessTest": {"description": "Tests for system behavior under adverse conditions", "attributes": {"Type": {"type": "string", "description": "Type, always 'RobustnessTest'"}, "Title": {"type": "string", "description": "Title of the robustness test"}, "Description": {"type": "string", "description": "Description of what is being tested"}, "ExpectedResult": {"type": "string", "description": "Expected outcome of the test"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "ExpectedResult": {"Label": "Expected Result", "display_type": "rich_text"}}}, "Test": {"description": "Base type for different kinds of tests", "attributes": {"Type": {"type": "string", "description": "Type of test"}, "Title": {"type": "string", "description": "Title of the test"}, "Description": {"type": "string", "description": "What the test verifies"}, "ExpectedResult": {"type": "string", "description": "Expected outcome"}}, "ui_metadata": {"Type": {"Label": "Type"}, "Title": {"Label": "Title"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "ExpectedResult": {"Label": "Expected Result", "display_type": "rich_text"}}}, "Deployment": {"description": "Represents the deployment configuration for a component, including infrastructure as code, container configurations, and CI/CD workflows", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'Deployment' for this entity"}, "Title": {"type": "string", "description": "Title of the deployment configuration"}, "Description": {"type": "string", "description": "Detailed description of the deployment setup and its purpose"}, "deployment_type": {"type": "string", "enum": ["frontend", "backend", "database"], "description": "Type of deployment based on the component (frontend, backend, or database)"}, "main_tf": {"type": "string", "description": "Content of main.tf containing the primary Terraform resource definitions"}, "variables_tf": {"type": "string", "description": "Content of variables.tf defining all input variables"}, "outputs_tf": {"type": "string", "description": "Content of outputs.tf defining all output values"}, "providers_tf": {"type": "string", "description": "Content of providers.tf configuring required providers"}, "workflow_file": {"type": "string", "description": "GitHub Actions workflow configuration for CI/CD"}, "docker_file": {"type": "string", "description": "Dockerfile for container configuration"}, "docker_compose": {"type": "string", "description": "Docker Compose configuration for local development"}, "configuration_state": {"type": "string", "description": "Current state of the deployment configuration", "enum": ["not_configured", "configured"]}}, "ui_metadata": {"Type": {"Label": "Type", "order": 1, "display_type": "text"}, "Title": {"Label": "Title", "order": 2, "display_type": "text"}, "Description": {"Label": "Description", "order": 3, "display_type": "rich_text"}, "deployment_type": {"Label": "Deployment Type", "order": 4, "display_type": "select"}, "main_tf": {"Label": "Main Terraform Configuration", "order": 5, "display_type": "code"}, "variables_tf": {"Label": "Variables Configuration", "order": 6, "display_type": "code"}, "outputs_tf": {"Label": "Outputs Configuration", "order": 7, "display_type": "code"}, "providers_tf": {"Label": "Providers Configuration", "order": 8, "display_type": "code"}, "workflow_file": {"Label": "CI/CD Workflow", "order": 9, "display_type": "code"}, "docker_file": {"Label": "Dockerfile", "order": 10, "display_type": "code"}, "docker_compose": {"Label": "Docker Compose Configuration", "order": 11, "display_type": "code"}, "configuration_state": {"Label": "Configuration State", "order": 12, "hidden": true}}}, "DatabaseModel": {"description": "Represents the data model for a container, defining its data structures, relationships, and storage strategy", "attributes": {"Type": {"type": "string", "description": "Type of the node, always 'DatabaseModel' for this entity"}, "Title": {"type": "string", "description": "Title for this database model"}, "Description": {"type": "string", "description": "Comprehensive description of the database model"}, "EntityDefinitions": {"type": "string", "description": "JSON array of entity definitions including attributes, types, and constraints"}, "RelationshipMappings": {"type": "string", "description": "JSON array of relationships between entities"}, "EntityRelationshipDiagram": {"type": "string", "description": "Mermaid diagram showing the entity relationships"}, "DatabaseSchema": {"type": "string", "description": "Complete SQL schema for creating the database"}, "DataStorageStrategy": {"type": "string", "description": "Strategy for data storage and organization"}, "DatabaseEngine": {"type": "string", "description": "Selected database engine and version"}, "SchemaName": {"type": "string", "description": "Name of the database schema"}, "Indices": {"type": "string", "description": "JSON array of index definitions"}, "PrimaryKeys": {"type": "string", "description": "JSON array of primary key definitions"}, "ForeignKeys": {"type": "string", "description": "JSON array of foreign key definitions"}, "Constraints": {"type": "string", "description": "JSON array of constraint definitions"}}, "ui_metadata": {"Type": {"Label": "Type", "display_type": "text"}, "Title": {"Label": "Title", "display_type": "text"}, "Description": {"Label": "Description", "display_type": "rich_text"}, "EntityDefinitions": {"Label": "Entity Definitions", "display_type": "json"}, "RelationshipMappings": {"Label": "Entity Relationships", "display_type": "json"}, "EntityRelationshipDiagram": {"Label": "ER Diagram", "display_type": "mermaid_chart"}, "DatabaseSchema": {"Label": "Database Schema", "display_type": "code"}, "DataStorageStrategy": {"Label": "Storage Strategy", "display_type": "rich_text"}, "DatabaseEngine": {"Label": "Database Engine", "display_type": "text"}, "SchemaName": {"Label": "Schema Name", "display_type": "text"}, "Indices": {"Label": "Index Definitions", "display_type": "json"}, "PrimaryKeys": {"Label": "Primary Keys", "display_type": "json"}, "ForeignKeys": {"Label": "Foreign Keys", "display_type": "json"}, "Constraints": {"Label": "Constraints", "display_type": "json"}}}}}