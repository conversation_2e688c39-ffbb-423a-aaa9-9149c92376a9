import sys
import os
import time
import logging

from app.utils.kg_build.knowledge import Knowledge, KnowledgeReporter, KnowledgeHelpers

# Minimal CLI Reporter
class CLIReporter(KnowledgeReporter):
    def send_agent_message(self, message):
        print(f"[Knowledge] {message}")

    def cost_update_callback(self, all_costs, total_cost):
        print(f"[Cost] Total: {total_cost} | Details: {all_costs}")

    def send_message(self, event, data):
        print(f"[{event}] {data}")

# Minimal CLI Helpers
class CLIHelpers(KnowledgeHelpers):
    def execute_cmd(self, cmd):
        import subprocess
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            return result.stdout, result.returncode
        except Exception as e:
            return str(e), 1

    def check_file_exists(self, filename):
        return os.path.exists(filename)

    def read_file(self, filename):
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception:
            return None

    def write_file(self, filename, content):
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)

    def list_directory(self, directory):
        try:
            return os.listdir(directory)
        except Exception:
            return []

def main():
    if len(sys.argv) < 2:
        print("Usage: python kg_build_cli.py <local_repo_path> [user_id] [project_id]")
        sys.exit(1)

    repo_path = sys.argv[1]
    user_id = sys.argv[2] if len(sys.argv) > 2 else "cli-user"
    project_id = sys.argv[3] if len(sys.argv) > 3 else "cli-project"

    if not os.path.isdir(repo_path):
        print(f"Error: {repo_path} is not a valid directory.")
        sys.exit(1)

    # Set up configuration for Knowledge
    config = {
        "base_path": repo_path,
        "model": "claude-3-5-sonnet-20241022",  # or any supported model
        "timeout": 180,
        "cost_tracer": None,  # Set to a cost tracer if available
        "reporter": CLIReporter(),
        "helpers": CLIHelpers(),
        "service": "localFiles",
        "user_id": user_id,
        "project_id": project_id,
        # Optionally add: "num_ingest_threads": 8, "enable_parallel_chunks": True, etc.
    }

    print(f"Initializing knowledge ingestion for repository: {repo_path}")
    knowledge = Knowledge.getKnowledge(config=config, id=f"cli-session-{user_id}-{project_id}")
    knowledge.start(synchronous=True)

    print("\nKnowledge ingestion complete.")
    print("Summary of source files indexed:")
    for file_info in knowledge.getSourceFileList():
        print(f"  - {file_info['name']}: {file_info['description']}")

    print("\nAvailable knowledge keys and their values:")
    keys = knowledge.getKeys()
    for key, desc in zip(keys["keys"], keys["descriptions"]):
        value = knowledge.getKeyValue(key)
        print(f"  {key}: {desc}\n    Value: {value}")

    print("\nYou can now inspect the .knowledge directory in your repository for persisted knowledge artifacts.")

if __name__ == "__main__":
    main()