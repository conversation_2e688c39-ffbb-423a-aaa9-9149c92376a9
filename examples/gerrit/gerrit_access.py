#!/usr/bin/env python3
"""
Gerrit Code Review REST API Client
Handles repository creation and management via HTTP methods
"""

import requests
import json
import base64
from typing import Optional, Dict, Any
from urllib.parse import urljoin


class GerritClient:
    """Efficient Gerrit REST API client with authentication and error handling"""
    
    def __init__(self, base_url: str, username: str, password: str):
        """
        Initialize Gerrit client
        
        Args:
            base_url: Gerrit server URL (e.g., 'http://localhost:8080')
            username: Gerrit username
            password: Gerrit HTTP password (from Settings > HTTP Credentials)
        """
        self.base_url = base_url.rstrip('/')
        self.api_url = f"{self.base_url}/a"  # Authenticated API endpoint
        self.session = requests.Session()
        
        # Set up authentication
        auth_string = base64.b64encode(f"{username}:{password}".encode()).decode()
        self.session.headers.update({
            'Authorization': f'Basic {auth_string}',
            'Content-Type': 'application/json'
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make authenticated HTTP request with error handling"""
        url = urljoin(self.api_url + '/', endpoint.lstrip('/'))
        
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            if hasattr(e.response, 'text'):
                print(f"Response: {e.response.text}")
            raise
    
    def _parse_gerrit_response(self, response: requests.Response) -> Dict[Any, Any]:
        """Parse Gerrit JSON response (removes )]}' prefix)"""
        text = response.text
        if text.startswith(")]}'"):
            text = text[4:]
        return json.loads(text) if text.strip() else {}
    
    def create_repository(self, repo_name: str, description: str = "", 
                         parent: str = "All-Projects", branches: Optional[list] = None) -> Dict[str, Any]:
        """
        Create a new repository in Gerrit
        
        Args:
            repo_name: Repository name
            description: Repository description
            parent: Parent project (default: All-Projects)
            branches: Initial branches to create
            
        Returns:
            Dict with repository creation response
        """
        if branches is None:
            branches = ["refs/heads/main"]
            
        payload = {
            "description": description,
            "parent": parent,
            "create_empty_commit": True,
            "branches": branches
        }
        
        response = self._make_request('PUT', f'/projects/{repo_name}', 
                                    data=json.dumps(payload))
        return self._parse_gerrit_response(response)
    
    def get_repository(self, repo_name: str) -> Dict[str, Any]:
        """Get repository information"""
        response = self._make_request('GET', f'/projects/{repo_name}')
        return self._parse_gerrit_response(response)
    
    def list_repositories(self, limit: int = 25) -> Dict[str, Any]:
        """List all repositories"""
        response = self._make_request('GET', f'/projects/?n={limit}')
        return self._parse_gerrit_response(response)
    
    def delete_repository(self, repo_name: str, force: bool = False) -> bool:
        """Delete repository (requires delete-project plugin)"""
        endpoint = f'/projects/{repo_name}/delete-project~delete'
        payload = {"force": force}
        
        try:
            self._make_request('POST', endpoint, data=json.dumps(payload))
            return True
        except requests.exceptions.RequestException:
            return False
    
    def get_branches(self, repo_name: str) -> Dict[str, Any]:
        """Get repository branches"""
        response = self._make_request('GET', f'/projects/{repo_name}/branches/')
        return self._parse_gerrit_response(response)
    
    def create_branch(self, repo_name: str, branch_name: str, 
                     revision: str = "HEAD") -> Dict[str, Any]:
        """Create a new branch"""
        payload = {"revision": revision}
        response = self._make_request('PUT', f'/projects/{repo_name}/branches/{branch_name}',
                                    data=json.dumps(payload))
        return self._parse_gerrit_response(response)
    
    def get_repository_config(self, repo_name: str) -> Dict[str, Any]:
        """Get repository configuration"""
        response = self._make_request('GET', f'/projects/{repo_name}/config')
        return self._parse_gerrit_response(response)
    
    def clone_url(self, repo_name: str, protocol: str = "http") -> str:
        """Get clone URL for repository"""
        if protocol.lower() == "ssh":
            return f"ssh://{self.base_url.split('://')[-1]}/{repo_name}"
        return f"{self.base_url}/{repo_name}"


def main():
    """Example usage of GerritClient"""
    
    # Initialize client
    client = GerritClient(
        base_url="http://localhost:8080",
        username="admin", 
        password="a2tnm5PsgP+5xtJuWtAE9sEDjhjBks/JlhBmpFgGQw"
    )
    
    try:
        # Create a new repository
        print("Creating repository...")
        repo_info = client.create_repository(
            repo_name="my-test-repo",
            description="Test repository created via Python",
            branches=["refs/heads/main", "refs/heads/develop"]
        )
        print(f"Repository created: {repo_info}")
        
        # # Get repository details
        # print("\nFetching repository info...")
        # repo_details = client.get_repository("my-test-repo")
        # print(f"Repository details: {repo_details}")
        
        # # List all repositories
        # print("\nListing repositories...")
        # repos = client.list_repositories(limit=10)
        # for name, info in repos.items():
        #     print(f"  - {name}: {info.get('description', 'No description')}")
        
        # # Get branches
        # print("\nRepository branches...")
        # branches = client.get_branches("my-test-repo")
        # for branch in branches:
        #     print(f"  - {branch['ref']}")
        
        # # Get clone URL
        # clone_url = client.clone_url("my-test-repo")
        # print(f"\nClone URL: {clone_url}")
        
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()