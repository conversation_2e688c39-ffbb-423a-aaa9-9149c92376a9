#!/usr/bin/env python3
"""
Create Gerrit repository with proper push permissions
"""

import requests
import json
import base64

def create_repo_with_permissions():
    """Create repository and grant permissions in one go"""
    
    base_url = "http://localhost:8080"
    api_url = f"{base_url}/a"
    repo_name = "my-test-repo-2"
    
    # Setup authenticated session
    session = requests.Session()
    auth_string = base64.b64encode("admin:a2tnm5PsgP+5xtJuWtAE9sEDjhjBks/JlhBmpFgGQw".encode()).decode()
    session.headers.update({
        'Content-Type': 'application/json',
        'Authorization': f'Basic {auth_string}'
    })
    
    try:
        # First, try to delete existing repository
        print("Deleting existing repository...")
        try:
            delete_url = f"{api_url}/projects/{repo_name}/delete-project~delete"
            session.post(delete_url, data=json.dumps({"force": True}))
            print("Repository deleted")
        except:
            print("Repository didn't exist or delete failed")
        
        # Create repository with proper setup
        print("Creating repository...")
        create_payload = {
            "description": "Filetrace repository from GitHub",
            "parent": "All-Projects", 
            "create_empty_commit": False,
            "branches": []
        }
        
        response = session.put(f"{api_url}/projects/{repo_name}", 
                              data=json.dumps(create_payload))
        response.raise_for_status()
        print("Repository created!")
        
        # Set up access permissions immediately
        print("Setting up permissions...")
        
        # Get All-Projects permissions as template
        all_projects_response = session.get(f"{api_url}/projects/All-Projects/access")
        if all_projects_response.text.startswith(")]}'"):
            all_projects_access = json.loads(all_projects_response.text[4:])
        else:
            all_projects_access = all_projects_response.json()
        
        # Create access rules for our repository
        access_rules = {
            "add": {
                "refs/heads/*": {
                    "permissions": {
                        "push": {
                            "rules": {
                                "group Administrators": {
                                    "action": "ALLOW",
                                    "force": True
                                }
                            }
                        },
                        "create": {
                            "rules": {
                                "group Administrators": {
                                    "action": "ALLOW"
                                }
                            }
                        },
                        "pushMerge": {
                            "rules": {
                                "group Administrators": {
                                    "action": "ALLOW"
                                }
                            }
                        }
                    }
                }
            }
        }
        
        # Apply permissions
        perm_response = session.post(f"{api_url}/projects/{repo_name}/access", 
                                   data=json.dumps(access_rules))
        perm_response.raise_for_status()
        
        print("Permissions granted successfully!")
        print(f"Repository ready at: {base_url}/admin/repos/{repo_name}")
        
    except Exception as e:
        print(f"Error: {e}")
        if hasattr(e, 'response') and hasattr(e.response, 'text'):
            print(f"Response: {e.response.text}")

if __name__ == "__main__":
    create_repo_with_permissions()