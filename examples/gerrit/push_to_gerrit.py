#!/usr/bin/env python3
"""
Complete Gerrit repository setup and GitHub migration script.
Creates Gerrit repository with proper permissions and migrates GitHub repository.
"""

import json
import base64
import subprocess
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Optional, Tuple
from urllib.parse import urlparse, quote
import requests
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class GerritRepositoryManager:
    """Manages Gerrit repository operations with proper error handling."""
    
    def __init__(self, hostname: str, username: str, password: str):
        """
        Initialize Gerrit manager with connection details.
        
        Args:
            hostname: Gerrit server hostname or full URL (e.g., 'gerrit.example.com' or 'https://gerrit.example.com')
            username: Gerrit username
            password: Gerrit password/token
        """
        # Store password for clone URLs
        self.password = password
        
        # Parse hostname to handle both full URLs and bare hostnames
        if hostname.startswith(('http://', 'https://')):
            self.base_url = hostname.rstrip('/')
            # Extract just the hostname part for clone URLs
            parsed = urlparse(hostname)
            self.hostname = parsed.netloc
        else:
            # Assume HTTPS for bare hostnames (more secure default)
            self.base_url = f"https://{hostname}"
            self.hostname = hostname
            
        self.api_url = f"{self.base_url}/a"
        self.session = self._create_session(username, password)
        self.username = username
        
    def _create_session(self, username: str, password: str) -> requests.Session:
        """Create authenticated session for Gerrit API."""
        session = requests.Session()
        auth_string = base64.b64encode(f"{username}:{password}".encode()).decode()
        session.headers.update({
            'Content-Type': 'application/json',
            'Authorization': f'Basic {auth_string}'
        })
        return session
    
    def delete_repository_if_exists(self, repo_name: str) -> bool:
        """
        Delete repository if it exists.
        
        Args:
            repo_name: Name of the repository to delete
            
        Returns:
            True if repository was deleted or didn't exist, False on error
        """
        try:
            delete_url = f"{self.api_url}/projects/{repo_name}/delete-project~delete"
            response = self.session.post(delete_url, data=json.dumps({"force": True}))
            if response.status_code in [200, 204, 404]:
                logger.info(f"Repository '{repo_name}' deleted or didn't exist")
                return True
            else:
                logger.warning(f"Delete response: {response.status_code}")
                return True  # Continue anyway
        except Exception as e:
            logger.warning(f"Delete operation failed: {e}")
            return True  # Continue anyway
    
    def create_repository(self, repo_name: str, description: str = "") -> bool:
        """
        Create new repository in Gerrit.
        
        Args:
            repo_name: Name of the repository to create
            description: Repository description
            
        Returns:
            True if successful, False otherwise
        """
        try:
            create_payload = {
                "description": description,
                "parent": "All-Projects",
                "create_empty_commit": False,
                "branches": []
            }
            
            response = self.session.put(
                f"{self.api_url}/projects/{repo_name}",
                data=json.dumps(create_payload)
            )
            response.raise_for_status()
            logger.info(f"Repository '{repo_name}' created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create repository: {e}")
            if hasattr(e, 'response') and hasattr(e.response, 'text'):
                logger.error(f"Response: {e.response.text}")
            return False
    
    def get_available_groups(self) -> Dict[str, Dict]:
        """
        Get available groups from Gerrit with their metadata.
        
        Returns:
            Dictionary mapping group names to group metadata (including UUIDs)
        """
        try:
            response = self.session.get(f"{self.api_url}/groups/")
            if response.text.startswith(")]}'"):
                groups_data = json.loads(response.text[4:])
            else:
                groups_data = response.json()
            
            logger.info(f"Available groups: {list(groups_data.keys())}")
            return groups_data
            
        except Exception as e:
            logger.warning(f"Could not fetch groups: {e}")
            return {}
    
    def find_admin_group(self) -> Optional[Tuple[str, str]]:
        """
        Find an appropriate admin group name and UUID.
        
        Returns:
            Tuple of (group_name, group_uuid) or None if not found
        """
        groups = self.get_available_groups()
        
        # Try common admin group names in order of preference
        admin_candidates = [
            "Administrators", 
            "Project Owners", 
            "administrators", 
            "project-owners",
            "admin", 
            "owners"
        ]
        
        for candidate in admin_candidates:
            if candidate in groups:
                group_uuid = groups[candidate].get('id', candidate)
                logger.info(f"Using admin group: {candidate} (UUID: {group_uuid})")
                return candidate, group_uuid
        
        # If no standard admin group found, use the first available group
        if groups:
            first_group = list(groups.keys())[0]
            group_uuid = groups[first_group].get('id', first_group)
            logger.warning(f"No standard admin group found, using: {first_group} (UUID: {group_uuid})")
            return first_group, group_uuid
            
        logger.error("No groups available!")
        return None
    def add_user_to_group(self, group_name: str, group_uuid: str) -> bool:
        """
        Add the current user to a group to ensure push permissions.
        
        Args:
            group_name: Name of the group
            group_uuid: UUID of the group
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Add user to group
            add_user_data = {
                "members": [self.username]
            }
            
            response = self.session.post(
                f"{self.api_url}/groups/{group_uuid}/members",
                data=json.dumps(add_user_data)
            )
            
            if response.status_code in [200, 201, 409]:  # 409 means already member
                logger.info(f"User '{self.username}' added to group '{group_name}'")
                return True
            else:
                logger.warning(f"Could not add user to group: {response.status_code}")
                return False
                
        except Exception as e:
            logger.warning(f"Failed to add user to group: {e}")
            return False
    
    def set_repository_permissions(self, repo_name: str) -> bool:
        """
        Set comprehensive permissions for the repository using UUIDs and ensure user access.
        
        Args:
            repo_name: Name of the repository
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Find appropriate admin group and get UUID
            admin_result = self.find_admin_group()
            if not admin_result:
                logger.error("No suitable admin group found, skipping permissions setup")
                return False
            
            admin_group, admin_uuid = admin_result
            
            # Add current user to admin group to ensure permissions
            self.add_user_to_group(admin_group, admin_uuid)
            
            # Build permissions using UUID
            access_rules = {
                "add": {
                    "refs/*": {
                        "permissions": {
                            "read": {
                                "rules": {
                                    admin_uuid: {"action": "ALLOW"}
                                }
                            }
                        }
                    },
                    "refs/heads/*": {
                        "permissions": {
                            "push": {
                                "rules": {
                                    admin_uuid: {
                                        "action": "ALLOW",
                                        "force": True
                                    }
                                }
                            },
                            "create": {
                                "rules": {
                                    admin_uuid: {"action": "ALLOW"}
                                }
                            },
                            "pushMerge": {
                                "rules": {
                                    admin_uuid: {"action": "ALLOW"}
                                }
                            },
                            "forgeAuthor": {
                                "rules": {
                                    admin_uuid: {"action": "ALLOW"}
                                }
                            },
                            "forgeCommitter": {
                                "rules": {
                                    admin_uuid: {"action": "ALLOW"}
                                }
                            }
                        }
                    },
                    "refs/tags/*": {
                        "permissions": {
                            "push": {
                                "rules": {
                                    admin_uuid: {"action": "ALLOW"}
                                }
                            },
                            "create": {
                                "rules": {
                                    admin_uuid: {"action": "ALLOW"}
                                }
                            }
                        }
                    }
                }
            }
            
            logger.info(f"Setting permissions with admin group: {admin_group} (UUID: {admin_uuid})")
            response = self.session.post(
                f"{self.api_url}/projects/{repo_name}/access",
                data=json.dumps(access_rules)
            )
            
            if response.status_code in [200, 201]:
                logger.info(f"Permissions set successfully for '{repo_name}'")
                return True
            else:
                logger.warning(f"Permissions API returned: {response.status_code}")
                logger.warning(f"Response: {response.text}")
                # Try alternative approach - inherit from All-Projects
                return self.inherit_all_projects_permissions(repo_name)
            
        except Exception as e:
            logger.error(f"Failed to set permissions: {e}")
            if hasattr(e, 'response') and hasattr(e.response, 'text'):
                logger.error(f"Response: {e.response.text}")
            
            # Try alternative approach
            return self.inherit_all_projects_permissions(repo_name)
    
    def inherit_all_projects_permissions(self, repo_name: str) -> bool:
        """
        Fallback: Ensure repository inherits from All-Projects properly.
        
        Args:
            repo_name: Name of the repository
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("Trying alternative permissions approach...")
            
            # Get current project config
            response = self.session.get(f"{self.api_url}/projects/{repo_name}/config")
            if response.text.startswith(")]}'"):
                config = json.loads(response.text[4:])
            else:
                config = response.json()
            
            # Ensure inheritance from All-Projects
            config_update = {
                "parent": "All-Projects",
                "description": config.get("description", "Filetrace repository migrated from GitHub")
            }
            
            response = self.session.put(
                f"{self.api_url}/projects/{repo_name}/config",
                data=json.dumps(config_update)
            )
            
            if response.status_code in [200, 201]:
                logger.info("Repository configured to inherit All-Projects permissions")
                return True
            else:
                logger.warning("Could not update repository configuration")
                return True  # Continue anyway
                
        except Exception as e:
            logger.warning(f"Inheritance setup failed: {e}")
            return True  # Continue migration anyway
    
    def get_clone_url(self, repo_name: str) -> str:
        """Get the clone URL for the repository with properly encoded credentials."""
        protocol = "https" if self.base_url.startswith("https") else "http"
        # URL-encode username and password to handle special characters
        encoded_username = quote(self.username, safe='')
        encoded_password = quote(self.password, safe='')
        return f"{protocol}://{encoded_username}:{encoded_password}@{self.hostname}/a/{repo_name}"


class GitRepositoryMigrator:
    """Handles Git repository cloning and pushing operations."""
    
    @staticmethod
    def run_git_command(command: list, cwd: Optional[Path] = None) -> Tuple[bool, str]:
        """
        Run git command and return result.
        
        Args:
            command: Git command as list of strings
            cwd: Working directory for the command
            
        Returns:
            Tuple of (success: bool, output: str)
        """
        try:
            result = subprocess.run(
                command,
                cwd=cwd,
                capture_output=True,
                text=True,
                check=True
            )
            return True, result.stdout.strip()
        except subprocess.CalledProcessError as e:
            logger.error(f"Git command failed: {' '.join(command)}")
            logger.error(f"Error: {e.stderr}")
            return False, e.stderr
    
    @staticmethod
    def clone_repository(github_url: str, temp_dir: Path) -> Tuple[bool, Optional[Path]]:
        """
        Clone GitHub repository to temporary directory.
        
        Args:
            github_url: GitHub repository URL
            temp_dir: Temporary directory path
            
        Returns:
            Tuple of (success: bool, repo_path: Optional[Path])
        """
        repo_name = github_url.split('/')[-1].replace('.git', '')
        repo_path = temp_dir / repo_name
        
        logger.info(f"Cloning repository from {github_url}")
        success, output = GitRepositoryMigrator.run_git_command([
            'git', 'clone', '--bare', github_url, str(repo_path)
        ])
        
        if success:
            logger.info(f"Repository cloned to {repo_path}")
            return True, repo_path
        else:
            logger.error(f"Failed to clone repository: {output}")
            return False, None
    
    @staticmethod
    def push_to_gerrit(repo_path: Path, gerrit_url: str) -> bool:
        """
        Push repository to Gerrit.
        
        Args:
            repo_path: Path to the cloned repository
            gerrit_url: Gerrit repository URL
            
        Returns:
            True if successful, False otherwise
        """
        logger.info(f"Pushing to Gerrit: {gerrit_url}")
        
        # Push all branches and tags
        commands = [
            ['git', 'push', gerrit_url, '--all'],
            ['git', 'push', gerrit_url, '--tags']
        ]
        
        for command in commands:
            success, output = GitRepositoryMigrator.run_git_command(command, repo_path)
            if not success:
                logger.error(f"Failed to push: {output}")
                return False
            logger.info(f"Successfully executed: {' '.join(command)}")
        
        return True


def migrate_github_to_gerrit(
    hostname: str,
    username: str, 
    password: str,
    github_url: str = "https://github.com/Kavia-ai/filetrace.git",
    repo_name: str = "filetrace"
) -> bool:
    """
    Complete migration workflow from GitHub to Gerrit.
    
    Args:
        hostname: Gerrit hostname
        username: Gerrit username
        password: Gerrit password
        github_url: GitHub repository URL
        repo_name: Name for the Gerrit repository
        
    Returns:
        True if successful, False otherwise
    """
    # Initialize managers
    gerrit_manager = GerritRepositoryManager(hostname, username, password)
    migrator = GitRepositoryMigrator()
    
    # Create temporary directory
    temp_dir = Path(tempfile.mkdtemp(prefix="gerrit_migration_"))
    
    try:
        logger.info("Starting GitHub to Gerrit migration")
        
        # Step 1: Clean up existing repository
        logger.info("Step 1: Cleaning up existing repository")
        if not gerrit_manager.delete_repository_if_exists(repo_name):
            return False
        
        # Step 2: Create new repository
        logger.info("Step 2: Creating new repository")
        if not gerrit_manager.create_repository(
            repo_name, 
            "Filetrace repository migrated from GitHub"
        ):
            return False
        
        # Step 3: Set permissions
        logger.info("Step 3: Setting repository permissions")
        if not gerrit_manager.set_repository_permissions(repo_name):
            return False
        
        # Step 4: Clone GitHub repository
        logger.info("Step 4: Cloning GitHub repository")
        success, repo_path = migrator.clone_repository(github_url, temp_dir)
        if not success or repo_path is None:
            return False
        
        # Step 5: Push to Gerrit
        logger.info("Step 5: Pushing to Gerrit")
        gerrit_url = gerrit_manager.get_clone_url(repo_name)
        if not migrator.push_to_gerrit(repo_path, gerrit_url):
            return False
        
        logger.info("Migration completed successfully!")
        logger.info(f"Repository available at: {gerrit_manager.base_url}/admin/repos/{repo_name}")
        logger.info(f"Clone URL: {gerrit_url}")
        
        return True
        
    except Exception as e:
        logger.error(f"Migration failed with unexpected error: {e}")
        return False
        
    finally:
        # Cleanup temporary directory
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
            logger.info(f"Cleaned up temporary directory: {temp_dir}")


def main():
    """Main function - configure your credentials here."""
    # Configuration - UPDATE THESE VALUES
    HOSTNAME = "https://gerrit-dev-test.cloud.kavia.ai"  # Replace with your Gerrit hostname
    USERNAME = "Esakkiraja"           # Replace with your Gerrit username
    PASSWORD = "gk/vqe/vOfmzm17i2Q5SNGXF7BHhqGkB/1upd0yE3Q"   # Replace with your Gerrit password
    
    GITHUB_URL = "https://github.com/Kavia-ai/filetrace.git"
    REPO_NAME = "filetrace-2"
    
    # Validate git is available
    try:
        subprocess.run(['git', '--version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.error("Git is not installed or not available in PATH")
        return False
    
    # Run migration
    success = migrate_github_to_gerrit(
        hostname=HOSTNAME,
        username=USERNAME,
        password=PASSWORD,
        github_url=GITHUB_URL,
        repo_name=REPO_NAME
    )
    
    if success:
        logger.info("🎉 Migration completed successfully!")
    else:
        logger.error("❌ Migration failed!")
    
    return success


if __name__ == "__main__":
    main()