#!/usr/bin/env python3
"""
Recreate Gerrit repository without initial commit
"""

import requests
import json
import base64

def recreate_repository():
    """Delete and recreate repository without initial commit"""
    
    base_url = "http://localhost:8080"
    api_url = f"{base_url}/a"
    
    # Setup session with proper authentication
    session = requests.Session()
    auth_string = base64.b64encode("admin:a2tnm5PsgP+5xtJuWtAE9sEDjhjBks/JlhBmpFgGQw".encode()).decode()
    session.headers.update({
        'Content-Type': 'application/json',
        'Authorization': f'Basic {auth_string}'
    })
    
    repo_name = "my-test-repo-1"
    
    try:
        # Delete existing repository
        print("Deleting existing repository...")
        delete_url = f"{api_url}/projects/{repo_name}/delete-project~delete"
        delete_payload = {"force": True}
        
        response = session.post(delete_url, data=json.dumps(delete_payload))
        if response.status_code in [200, 204, 404]:
            print("Repository deleted successfully")
        else:
            print(f"Delete response: {response.status_code}")
    
    except Exception as e:
        print(f"Delete failed (might not exist): {e}")
    
    try:
        # Create new repository WITHOUT initial commit
        print("Creating new repository...")
        create_url = f"{api_url}/projects/{repo_name}"
        create_payload = {
            "description": "Filetrace repository from GitHub",
            "parent": "All-Projects",
            "create_empty_commit": False,  # Key: don't create initial commit
            "branches": []  # No initial branches
        }
        
        response = session.put(create_url, data=json.dumps(create_payload))
        response.raise_for_status()
        
        print("Repository recreated successfully!")
        print("Now you can push without conflicts")
        
    except Exception as e:
        print(f"Recreation failed: {e}")
        if hasattr(e, 'response'):
            print(f"Response: {e.response.text}")

if __name__ == "__main__":
    recreate_repository()