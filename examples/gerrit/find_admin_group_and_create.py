#!/usr/bin/env python3
"""
Find correct admin group and create repository with permissions
"""

import requests
import json
import base64

def create_repo_with_correct_permissions():
    """Create repository with correct group permissions"""
    
    base_url = "http://localhost:8080"
    api_url = f"{base_url}/a"
    repo_name = "my-test-repo-3"
    
    # Setup authenticated session
    session = requests.Session()
    auth_string = base64.b64encode("admin:a2tnm5PsgP+5xtJuWtAE9sEDjhjBks/JlhBmpFgGQw".encode()).decode()
    session.headers.update({
        'Content-Type': 'application/json',
        'Authorization': f'Basic {auth_string}'
    })
    
    try:
        # First, find the correct administrators group
        print("Finding administrator groups...")
        groups_response = session.get(f"{api_url}/groups/")
        if groups_response.text.startswith(")]}'"):
            groups = json.loads(groups_response.text[4:])
        else:
            groups = groups_response.json()
        
        # Look for admin-like groups
        admin_groups = []
        for group_id, group_info in groups.items():
            group_name = group_info.get('name', '').lower()
            if any(word in group_name for word in ['admin', 'service', 'registered']):
                admin_groups.append((group_id, group_info['name']))
                print(f"Found group: {group_info['name']} (ID: {group_id})")
        
        # Use the first admin group found, or fallback to a common one
        if admin_groups:
            admin_group_id = admin_groups[0][0]
            admin_group_name = admin_groups[0][1]
            print(f"Using group: {admin_group_name}")
        else:
            # Common fallback group IDs in Gerrit
            admin_group_id = "global:Registered-Users"
            print("Using fallback group: Registered-Users")
        
        # Delete existing repository if it exists
        try:
            delete_url = f"{api_url}/projects/{repo_name}/delete-project~delete"
            session.post(delete_url, data=json.dumps({"force": True}))
            print("Existing repository deleted")
        except:
            print("No existing repository to delete")
        
        # Create repository
        print("Creating repository...")
        create_payload = {
            "description": "Filetrace repository from GitHub",
            "parent": "All-Projects",
            "create_empty_commit": False,
            "branches": []
        }
        
        create_response = session.put(f"{api_url}/projects/{repo_name}", 
                                    data=json.dumps(create_payload))
        create_response.raise_for_status()
        print("Repository created!")
        
        # Set up access permissions with correct group ID
        print("Setting up permissions...")
        access_rules = {
            "add": {
                "refs/heads/*": {
                    "permissions": {
                        "push": {
                            "rules": {
                                admin_group_id: {
                                    "action": "ALLOW",
                                    "force": True
                                }
                            }
                        },
                        "create": {
                            "rules": {
                                admin_group_id: {
                                    "action": "ALLOW"
                                }
                            }
                        },
                        "pushMerge": {
                            "rules": {
                                admin_group_id: {
                                    "action": "ALLOW"
                                }
                            }
                        }
                    }
                }
            }
        }
        
        perm_response = session.post(f"{api_url}/projects/{repo_name}/access", 
                                   data=json.dumps(access_rules))
        perm_response.raise_for_status()
        
        print("Permissions granted successfully!")
        print(f"Repository ready at: {base_url}/admin/repos/{repo_name}")
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        if hasattr(e, 'response') and hasattr(e.response, 'text'):
            print(f"Response: {e.response.text}")
        return False

if __name__ == "__main__":
    create_repo_with_correct_permissions()