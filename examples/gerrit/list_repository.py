#!/usr/bin/env python3
"""
Gerrit repository listing utility.
Lists all repositories from a Gerrit instance with detailed information.
"""

import json
import base64
from typing import Dict, List, Optional
from urllib.parse import urlparse
import requests
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class GerritRepositoryLister:
    """Lists and manages Gerrit repository information."""
    
    def __init__(self, hostname: str, username: str, password: str):
        """
        Initialize Gerrit repository lister.
        
        Args:
            hostname: Gerrit server hostname or full URL
            username: Gerrit username
            password: Gerrit password/token
        """
        # Parse hostname to handle both full URLs and bare hostnames
        if hostname.startswith(('http://', 'https://')):
            self.base_url = hostname.rstrip('/')
            parsed = urlparse(hostname)
            self.hostname = parsed.netloc
        else:
            self.base_url = f"https://{hostname}"
            self.hostname = hostname
            
        self.api_url = f"{self.base_url}/a"
        self.session = self._create_session(username, password)
        self.username = username
    
    def _create_session(self, username: str, password: str) -> requests.Session:
        """Create authenticated session for Gerrit API."""
        session = requests.Session()
        auth_string = base64.b64encode(f"{username}:{password}".encode()).decode()
        session.headers.update({
            'Content-Type': 'application/json',
            'Authorization': f'Basic {auth_string}'
        })
        return session
    
    def get_repositories(self, include_description: bool = True) -> Optional[Dict]:
        """
        Get all repositories from Gerrit.
        
        Args:
            include_description: Whether to include repository descriptions
            
        Returns:
            Dictionary of repository information or None on error
        """
        try:
            params = {}
            if include_description:
                params['d'] = ''  # Include description
            
            response = self.session.get(f"{self.api_url}/projects/", params=params)
            response.raise_for_status()
            
            # Gerrit responses often start with )]}' for security
            if response.text.startswith(")]}'"):
                repos_data = json.loads(response.text[4:])
            else:
                repos_data = response.json()
            
            logger.info(f"Retrieved {len(repos_data)} repositories")
            return repos_data
            
        except Exception as e:
            logger.error(f"Failed to retrieve repositories: {e}")
            if hasattr(e, 'response') and hasattr(e.response, 'text'):
                logger.error(f"Response: {e.response.text}")
            return None
    
    def get_repository_details(self, repo_name: str) -> Optional[Dict]:
        """
        Get detailed information about a specific repository.
        
        Args:
            repo_name: Name of the repository
            
        Returns:
            Repository details or None on error
        """
        try:
            response = self.session.get(f"{self.api_url}/projects/{repo_name}")
            response.raise_for_status()
            
            if response.text.startswith(")]}'"):
                repo_data = json.loads(response.text[4:])
            else:
                repo_data = response.json()
            
            return repo_data
            
        except Exception as e:
            logger.error(f"Failed to get details for repository '{repo_name}': {e}")
            return None
    
    def get_repository_branches(self, repo_name: str) -> Optional[List[Dict]]:
        """
        Get branches for a specific repository.
        
        Args:
            repo_name: Name of the repository
            
        Returns:
            List of branch information or None on error
        """
        try:
            response = self.session.get(f"{self.api_url}/projects/{repo_name}/branches/")
            response.raise_for_status()
            
            if response.text.startswith(")]}'"):
                branches_data = json.loads(response.text[4:])
            else:
                branches_data = response.json()
            
            return branches_data
            
        except Exception as e:
            logger.error(f"Failed to get branches for repository '{repo_name}': {e}")
            return None
    
    def list_repositories(self, detailed: bool = False, show_branches: bool = False) -> bool:
        """
        List all repositories with optional detailed information.
        
        Args:
            detailed: Whether to show detailed information for each repository
            show_branches: Whether to show branches for each repository
            
        Returns:
            True if successful, False otherwise
        """
        repos = self.get_repositories(include_description=True)
        if not repos:
            return False
        
        print(f"\n{'='*80}")
        print(f"GERRIT REPOSITORIES ({len(repos)} found)")
        print(f"Server: {self.base_url}")
        print(f"{'='*80}\n")
        
        for repo_name, repo_info in sorted(repos.items()):
            print(f"📁 Repository: {repo_name}")
            
            # Basic information
            if 'description' in repo_info and repo_info['description']:
                print(f"   Description: {repo_info['description']}")
            
            if 'state' in repo_info:
                print(f"   State: {repo_info['state']}")
            
            # Show clone URL
            protocol = "https" if self.base_url.startswith("https") else "http"
            clone_url = f"{protocol}://{self.hostname}/a/{repo_name}"
            print(f"   Clone URL: {clone_url}")
            
            # Detailed information
            if detailed:
                details = self.get_repository_details(repo_name)
                if details:
                    if 'parent' in details:
                        print(f"   Parent: {details['parent']}")
                    if 'web_links' in details:
                        for link in details['web_links']:
                            print(f"   Web Link: {link.get('url', 'N/A')}")
            
            # Branch information
            if show_branches:
                branches = self.get_repository_branches(repo_name)
                if branches:
                    print(f"   Branches ({len(branches)}):")
                    for branch in branches[:5]:  # Show first 5 branches
                        branch_name = branch.get('ref', '').replace('refs/heads/', '')
                        revision = branch.get('revision', '')[:8]  # Short hash
                        print(f"     • {branch_name} ({revision})")
                    
                    if len(branches) > 5:
                        print(f"     ... and {len(branches) - 5} more branches")
            
            print()  # Empty line between repositories
        
        return True
    
    def search_repositories(self, search_term: str) -> bool:
        """
        Search for repositories matching a term.
        
        Args:
            search_term: Term to search for in repository names or descriptions
            
        Returns:
            True if successful, False otherwise
        """
        repos = self.get_repositories(include_description=True)
        if not repos:
            return False
        
        # Filter repositories
        matching_repos = {}
        for repo_name, repo_info in repos.items():
            # Check name
            if search_term.lower() in repo_name.lower():
                matching_repos[repo_name] = repo_info
                continue
            
            # Check description
            description = repo_info.get('description', '')
            if search_term.lower() in description.lower():
                matching_repos[repo_name] = repo_info
        
        if not matching_repos:
            print(f"\n❌ No repositories found matching '{search_term}'")
            return True
        
        print(f"\n🔍 SEARCH RESULTS for '{search_term}' ({len(matching_repos)} found)")
        print(f"{'='*60}\n")
        
        for repo_name, repo_info in sorted(matching_repos.items()):
            print(f"📁 {repo_name}")
            if 'description' in repo_info and repo_info['description']:
                print(f"   Description: {repo_info['description']}")
            print()
        
        return True
    
    def get_repository_statistics(self) -> bool:
        """
        Show statistics about repositories.
        
        Returns:
            True if successful, False otherwise
        """
        repos = self.get_repositories(include_description=True)
        if not repos:
            return False
        
        # Calculate statistics
        total_repos = len(repos)
        repos_with_description = sum(1 for repo in repos.values() if repo.get('description'))
        
        # Group by state
        state_counts = {}
        for repo_info in repos.values():
            state = repo_info.get('state', 'ACTIVE')
            state_counts[state] = state_counts.get(state, 0) + 1
        
        print(f"\n📊 REPOSITORY STATISTICS")
        print(f"{'='*40}")
        print(f"Total repositories: {total_repos}")
        print(f"With descriptions: {repos_with_description}")
        print(f"Without descriptions: {total_repos - repos_with_description}")
        
        print(f"\nBy State:")
        for state, count in sorted(state_counts.items()):
            print(f"  {state}: {count}")
        
        return True


def main():
    """Main function with command-line interface."""
    import argparse
    
    # Configuration
    HOSTNAME = "https://gerrit-dev-test.cloud.kavia.ai"
    USERNAME = "Esakkiraja"
    PASSWORD = "gk/vqe/vOfmzm17i2Q5SNGXF7BHhqGkB/1upd0yE3Q"
    
    parser = argparse.ArgumentParser(description='List Gerrit repositories')
    parser.add_argument('--detailed', '-d', action='store_true',
                       help='Show detailed repository information')
    parser.add_argument('--branches', '-b', action='store_true',
                       help='Show branch information for each repository')
    parser.add_argument('--search', '-s', type=str,
                       help='Search for repositories containing the specified term')
    parser.add_argument('--stats', action='store_true',
                       help='Show repository statistics')
    
    args = parser.parse_args()
    
    # Initialize lister
    lister = GerritRepositoryLister(HOSTNAME, USERNAME, PASSWORD)
    
    try:
        if args.search:
            success = lister.search_repositories(args.search)
        elif args.stats:
            success = lister.get_repository_statistics()
        else:
            success = lister.list_repositories(
                detailed=args.detailed,
                show_branches=args.branches
            )
        
        if success:
            logger.info("Operation completed successfully")
        else:
            logger.error("Operation failed")
            return False
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Operation cancelled by user")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return False
    
    return True


if __name__ == "__main__":
    main()