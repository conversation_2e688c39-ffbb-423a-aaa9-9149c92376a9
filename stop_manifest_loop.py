#!/usr/bin/env python3
"""
Script to monitor and stop manifest generation loops
"""

import subprocess
import time
import signal
import os
import sys
from datetime import datetime

def get_running_processes():
    """Get all running Python processes"""
    try:
        result = subprocess.run(['wsl', 'bash', '-c', 'ps aux | grep python'], 
                              capture_output=True, text=True, shell=True)
        return result.stdout
    except Exception as e:
        print(f"Error getting processes: {e}")
        return ""

def kill_process(pid):
    """Kill a process by PID"""
    try:
        subprocess.run(['wsl', 'bash', '-c', f'kill -9 {pid}'], shell=True)
        print(f"Killed process {pid}")
        return True
    except Exception as e:
        print(f"Error killing process {pid}: {e}")
        return False

def monitor_manifest_requests():
    """Monitor for repeated manifest requests and stop them"""
    print("Monitoring for manifest generation loops...")
    print("Press Ctrl+C to stop monitoring")
    
    while True:
        try:
            # Check for uvicorn processes
            processes = get_running_processes()
            lines = processes.split('\n')
            
            for line in lines:
                if 'uvicorn' in line and 'app.main:app' in line:
                    parts = line.split()
                    if len(parts) >= 2:
                        pid = parts[1]
                        print(f"Found uvicorn process: {pid}")
                        
                        # Check if this process is consuming high CPU (indicating a loop)
                        try:
                            cpu_check = subprocess.run(
                                ['wsl', 'bash', '-c', f'ps -p {pid} -o %cpu --no-headers'], 
                                capture_output=True, text=True, shell=True
                            )
                            cpu_usage = float(cpu_check.stdout.strip())
                            if cpu_usage > 50:  # High CPU usage
                                print(f"High CPU usage detected ({cpu_usage}%) for process {pid}")
                                response = input(f"Kill process {pid}? (y/n): ")
                                if response.lower() == 'y':
                                    kill_process(pid)
                        except:
                            pass
            
            time.sleep(5)  # Check every 5 seconds
            
        except KeyboardInterrupt:
            print("\nStopping monitoring...")
            break
        except Exception as e:
            print(f"Error in monitoring: {e}")
            time.sleep(5)

def stop_all_manifest_processes():
    """Stop all processes related to manifest generation"""
    print("Stopping all manifest-related processes...")
    
    try:
        # Kill uvicorn processes
        subprocess.run(['wsl', 'bash', '-c', 'pkill -f "uvicorn.*app.main:app"'], shell=True)
        print("Killed uvicorn processes")
        
        # Kill any Python processes with high CPU usage
        subprocess.run(['wsl', 'bash', '-c', 'ps aux | grep python | grep -v grep | awk \'$3 > 50 {print $2}\' | xargs -r kill -9'], shell=True)
        print("Killed high CPU Python processes")
        
    except Exception as e:
        print(f"Error stopping processes: {e}")

def main():
    if len(sys.argv) > 1:
        if sys.argv[1] == "stop":
            stop_all_manifest_processes()
        elif sys.argv[1] == "monitor":
            monitor_manifest_requests()
        else:
            print("Usage: python stop_manifest_loop.py [stop|monitor]")
    else:
        print("Usage: python stop_manifest_loop.py [stop|monitor]")
        print("  stop   - Stop all manifest-related processes")
        print("  monitor - Monitor for loops and stop them interactively")

if __name__ == "__main__":
    main()
