FROM public.ecr.aws/docker/library/python:3.11-slim-bullseye

# LAYER 1: System packages (HEAVY CACHE - no build args)
RUN apt-get update && apt-get install -y \
    git \
    curl \
    unzip \
    screen \
    iproute2 \
    tar \
    rsync \
    # WeasyPrint dependencies
    libpango-1.0-0 \
    libpangoft2-1.0-0 \
    libcairo2 \
    libpangocairo-1.0-0 \
    libgdk-pixbuf2.0-0 \
    libffi-dev \
    shared-mime-info \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# LAYER 2: Install AWS CLI (HEAVY CACHE)
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" && \
    unzip awscliv2.zip && \
    ./aws/install && \
    rm -rf aws awscliv2.zip

# LAYER 3: Git configuration (CACHE)
RUN git config --global credential.helper '!aws codecommit credential-helper $@' && \
    git config --global credential.UseHttpPath true && \
    git config --global user.email "<EMAIL>" && \
    git config --global user.name "CodeGen Bot"

# LAYER 4: Working directory
WORKDIR /app

# LAYER 5: Copy requirements FIRST (cache when requirements unchanged)
COPY requirements.txt /app/requirements.txt

# LAYER 6: Install standard pip packages WITHOUT CodeArtifact first
RUN --mount=type=cache,target=/root/.cache/pip \
    pip install --upgrade pip setuptools wheel && \
    pip install html2text pylatexenc pyperclip --no-build-isolation

# LAYER 7: AWS configuration and CodeArtifact dependencies
ARG AWS_ACCESS_KEY_ID
ARG AWS_SECRET_ACCESS_KEY
ARG AWS_DEFAULT_REGION=us-east-1

# Configure AWS and install private packages
RUN if [ -n "$AWS_ACCESS_KEY_ID" ] && [ -n "$AWS_SECRET_ACCESS_KEY" ]; then \
      export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID && \
      export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY && \
      export AWS_DEFAULT_REGION=$AWS_DEFAULT_REGION && \
      aws codeartifact login --tool pip --domain kavia --domain-owner 058264095463 --repository kavia && \
      pip install --no-cache-dir -r requirements.txt; \
    else \
      echo "No AWS credentials, installing requirements without CodeArtifact" && \
      pip install --no-cache-dir -r requirements.txt; \
    fi

# LAYER 8: SSH key setup
ARG SSH_KEY
RUN if [ -n "$SSH_KEY" ]; then \
      mkdir -p $HOME/.ssh/ && \
      echo "$SSH_KEY" > $HOME/.ssh/id_rsa && \
      chmod 600 $HOME/.ssh/id_rsa && \
      touch $HOME/.ssh/known_hosts && \
      ssh-keyscan github.com >> $HOME/.ssh/known_hosts; \
    fi

# LAYER 9: DataDog environment variables
ARG DD_GIT_REPOSITORY_URL
ARG DD_GIT_COMMIT_SHA
ENV DD_GIT_REPOSITORY_URL=${DD_GIT_REPOSITORY_URL}
ENV DD_GIT_COMMIT_SHA=${DD_GIT_COMMIT_SHA}

# LAYER 10: Python environment
ENV PYTHONPATH=/app

# LAYER 11: Copy application code
COPY . /app

EXPOSE 8000

CMD ["ddtrace-run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4", "--loop", "uvloop", "--timeout-keep-alive", "900"]