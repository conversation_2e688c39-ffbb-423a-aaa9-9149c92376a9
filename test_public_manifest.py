#!/usr/bin/env python3
"""
Test script for the public manifest endpoint
"""

import requests
import json
import sys

def test_public_manifest_endpoint(project_id=640149):
    """Test the public manifest endpoint"""
    
    url = f"http://localhost:8000/api/manifest/generate_manifest_json/public/{project_id}/"
    
    params = {
        "regenerate": False,
        "include_form_config": True,
        "stream": False
    }
    
    print(f"Testing public manifest endpoint for project {project_id}")
    print(f"URL: {url}")
    print(f"Params: {params}")
    print("-" * 50)
    
    try:
        response = requests.post(url, params=params, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Success! Response:")
            print(f"  Success: {data.get('success')}")
            print(f"  Project ID: {data.get('project_id')}")
            print(f"  Source: {data.get('source')}")
            print(f"  Generation Time: {data.get('generation_time_ms', 'N/A')}ms")
            
            if 'formData' in data:
                print(f"  Form Data Keys: {list(data['formData'].keys())}")
            
            if 'manifest' in data:
                print(f"  Manifest Keys: {list(data['manifest'].keys())}")
                
        else:
            print("❌ Error Response:")
            print(f"  {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure the server is running on localhost:8000")
    except requests.exceptions.Timeout:
        print("❌ Timeout: Request took too long")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_auth_status():
    """Test the auth status endpoint"""
    
    url = "http://localhost:8000/api/manifest/auth/status/"
    
    print(f"\nTesting auth status endpoint")
    print(f"URL: {url}")
    print("-" * 50)
    
    try:
        response = requests.get(url, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Auth Status Response:")
            print(f"  Authenticated: {data.get('authenticated')}")
            if data.get('user'):
                print(f"  User: {data.get('user')}")
            if data.get('is_admin'):
                print(f"  Is Admin: {data.get('is_admin')}")
        else:
            print("❌ Error Response:")
            print(f"  {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure the server is running on localhost:8000")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def main():
    if len(sys.argv) > 1:
        project_id = int(sys.argv[1])
    else:
        project_id = 640149  # Default project ID
    
    print("Testing Public Manifest Endpoint")
    print("=" * 50)
    
    # Test auth status first
    test_auth_status()
    
    # Test public manifest endpoint
    test_public_manifest_endpoint(project_id)
    
    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    main()
