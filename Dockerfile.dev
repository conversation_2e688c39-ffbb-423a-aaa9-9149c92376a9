# Use Ubuntu 22.04 as the base image
FROM ubuntu:22.04

# Set environment variables for non-interactive installation
ENV container=docker
ENV DEBIAN_FRONTEND=noninteractive
ENV AWS_REGION=us-east-1
ENV CODEARTIFACT_DOMAIN=kavia
ENV CODEARTIFACT_OWNER=058264095463
ENV CODEARTIFACT_REPO=kavia

# LAYER 1: System packages (changes rarely - cache heavily)
RUN apt-get update -y && apt-get upgrade -y && apt-get install -y \
    iproute2 \
    docker.io\
    software-properties-common \
    build-essential \
    zlib1g-dev \
    libncurses5-dev \
    libgdbm-dev \
    screen \
    rsync \
    git\
    nfs-common \
    libnss3-dev \
    libssl-dev \
    libreadline-dev \
    libffi-dev \
    libsqlite3-dev \
    libbz2-dev \
    liblzma-dev \
    libexpat1-dev \
    tk-dev \
    curl \
    wget \
    systemd \
    tzdata \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    unzip \
    nano \
    moreutils \
    && rm -rf /var/lib/apt/lists/* 

# LAYER 2: Python compilation (HEAVY CACHE)
RUN wget https://www.python.org/ftp/python/3.11.6/Python-3.11.6.tgz && \
    tar -xvf Python-3.11.6.tgz && \
    cd Python-3.11.6 && \
    ./configure --enable-optimizations && \
    make -j$(nproc) && \
    make altinstall && \
    make clean && \
    cd .. && \
    rm -rf Python-3.11.6 Python-3.11.6.tgz

# LAYER 3: Python setup (HEAVY CACHE)
RUN python3.11 -m ensurepip && python3.11 -m pip install --upgrade pip && \
    ln -sf /usr/local/bin/python3.11 /usr/bin/python3 && \
    ln -sf /usr/local/bin/python3.11 /usr/bin/python && \
    ln -s /usr/local/bin/pip3.11 /usr/bin/pip

# LAYER 4: Git config (CACHE)
RUN git config --global user.name "CodeGen Bot" && \
    git config --global user.email "<EMAIL>"

# LAYER 5: AWS CLI (HEAVY CACHE)
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" && \
    unzip awscliv2.zip && \
    ./aws/install && \
    rm -rf awscliv2.zip aws && \
    aws --version

# LAYER 6: Terraform installation (cache)
RUN wget -O- https://apt.releases.hashicorp.com/gpg | gpg --dearmor | tee /usr/share/keyrings/hashicorp-archive-keyring.gpg > /dev/null && \
    echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com jammy main" | tee /etc/apt/sources.list.d/hashicorp.list && \
    apt update && \
    apt install -y terraform && \
    rm -rf /var/lib/apt/lists/* && \
    terraform --version

# LAYER 7: Timezone (CACHE)
RUN ln -fs /usr/share/zoneinfo/UTC /etc/localtime && \
    dpkg-reconfigure -f noninteractive tzdata

# LAYER 8: Working directory
WORKDIR /app

# LAYER 9: Copy requirements FIRST (for dependency caching)
COPY requirements.txt /app/requirements.txt

# LAYER 10: Install standard dependencies WITHOUT CodeArtifact first
RUN --mount=type=cache,target=/root/.cache/pip \
    pip install --no-cache-dir $(grep -v "^#" /app/requirements.txt | grep -v "^\s*$" | head -20) || \
    pip install --no-cache-dir -r /app/requirements.txt || echo "Initial pip install failed, will retry with CodeArtifact"

# AWS config (INVALIDATES BUT NEEDED FOR PIP)
ARG AWS_ACCESS_KEY_ID
ARG AWS_SECRET_ACCESS_KEY

# AAWS config and CodeArtifact dependencies (minimal invalidation)
ARG AWS_ACCESS_KEY_ID
ARG AWS_SECRET_ACCESS_KEY

RUN if [ -n "$AWS_ACCESS_KEY_ID" ] && [ -n "$AWS_SECRET_ACCESS_KEY" ]; then \
      aws configure set aws_access_key_id "$AWS_ACCESS_KEY_ID" && \
      aws configure set aws_secret_access_key "$AWS_SECRET_ACCESS_KEY" && \
      aws configure set region "$AWS_REGION" && \
      aws codeartifact login --tool pip --domain $CODEARTIFACT_DOMAIN \
        --domain-owner $CODEARTIFACT_OWNER --repository $CODEARTIFACT_REPO && \
      pip install --no-cache-dir -r /app/requirements.txt; \
    else \
      echo "No AWS credentials provided, skipping CodeArtifact"; \
    fi

# LAYER 10: Install Playwright (cache with mount)
RUN --mount=type=cache,target=/root/.cache/pip \
    pip install playwright && \
    playwright install chromium

# LAYER 11: Copy application code LAST (changes most frequently)
COPY . /app

# Expose necessary ports
EXPOSE 8765

# Start the application
CMD ["python3.11", "-m", "uvicorn", "app.code_gen_app:app", "--port", "8003", "--reload"]