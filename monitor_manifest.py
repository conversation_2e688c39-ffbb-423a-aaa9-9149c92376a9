#!/usr/bin/env python3
"""
Simple monitoring script for manifest generation loops
"""

import subprocess
import time
import sys
from datetime import datetime

def check_manifest_logs():
    """Check manifest route logs for repeated requests"""
    try:
        result = subprocess.run(['wsl', 'bash', '-c', 'tail -n 50 logs/manifest_route.log'], 
                              capture_output=True, text=True, shell=True)
        return result.stdout
    except Exception as e:
        print(f"Error reading logs: {e}")
        return ""

def check_public_manifest_requests():
    """Check for public manifest requests"""
    try:
        result = subprocess.run(['wsl', 'bash', '-c', 'grep "Public manifest generation" logs/manifest_route.log | tail -n 10'], 
                              capture_output=True, text=True, shell=True)
        return result.stdout
    except Exception as e:
        print(f"Error reading public manifest logs: {e}")
        return ""

def check_running_processes():
    """Check for running uvicorn processes"""
    try:
        result = subprocess.run(['wsl', 'bash', '-c', 'ps aux | grep "uvicorn.*app.main:app" | grep -v grep'], 
                              capture_output=True, text=True, shell=True)
        return result.stdout
    except Exception as e:
        print(f"Error checking processes: {e}")
        return ""

def kill_high_cpu_processes():
    """Kill processes with high CPU usage"""
    try:
        print("Checking for high CPU processes...")
        result = subprocess.run(['wsl', 'bash', '-c', 'ps aux | grep python | grep -v grep | awk \'$3 > 80 {print $2}\''], 
                              capture_output=True, text=True, shell=True)
        
        if result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            print(f"Found {len(pids)} high CPU processes: {pids}")
            
            for pid in pids:
                if pid.strip():
                    print(f"Killing process {pid}")
                    subprocess.run(['wsl', 'bash', '-c', f'kill -9 {pid}'], shell=True)
        else:
            print("No high CPU processes found")
            
    except Exception as e:
        print(f"Error killing processes: {e}")

def monitor_loop():
    """Main monitoring loop"""
    print("Monitoring manifest generation for loops...")
    print("Press Ctrl+C to stop")
    
    while True:
        try:
            print(f"\n=== {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===")
            
            # Check logs for repeated requests
            logs = check_manifest_logs()
            if "Multiple requests detected" in logs:
                print("⚠️  WARNING: Multiple requests detected in logs!")
                print("Recent log entries:")
                for line in logs.split('\n')[-10:]:
                    if "Multiple requests detected" in line or "Manifest generation request" in line:
                        print(f"  {line}")
            
            # Check for public manifest requests
            public_logs = check_public_manifest_requests()
            if public_logs:
                print("📋 Public manifest requests:")
                for line in public_logs.split('\n'):
                    if line.strip():
                        print(f"  {line}")
            
            # Check running processes
            processes = check_running_processes()
            if processes:
                print(f"Running uvicorn processes: {len(processes.split(chr(10)))}")
                for line in processes.split('\n'):
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 3:
                            pid = parts[1]
                            cpu = parts[2]
                            print(f"  PID: {pid}, CPU: {cpu}%")
                            
                            # Auto-kill if CPU > 80%
                            try:
                                if float(cpu) > 80:
                                    print(f"  ⚠️  High CPU detected ({cpu}%), killing process {pid}")
                                    subprocess.run(['wsl', 'bash', '-c', f'kill -9 {pid}'], shell=True)
                            except:
                                pass
            
            time.sleep(10)  # Check every 10 seconds
            
        except KeyboardInterrupt:
            print("\nStopping monitoring...")
            break
        except Exception as e:
            print(f"Error in monitoring: {e}")
            time.sleep(10)

def main():
    if len(sys.argv) > 1:
        if sys.argv[1] == "kill":
            kill_high_cpu_processes()
        elif sys.argv[1] == "monitor":
            monitor_loop()
        else:
            print("Usage: python monitor_manifest.py [kill|monitor]")
    else:
        print("Usage: python monitor_manifest.py [kill|monitor]")
        print("  kill   - Kill high CPU processes")
        print("  monitor - Monitor for loops")

if __name__ == "__main__":
    main()
