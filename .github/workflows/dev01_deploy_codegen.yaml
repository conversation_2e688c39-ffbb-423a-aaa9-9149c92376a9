name: Codegenservice Deployment

on:
  push:
    branches:
      - develop
  workflow_dispatch:

env:
  DUPLO_HOST: https://duplo.cloud.kavia.ai
  DUPLO_TOKEN: ${{ secrets.DUPLO_TOKEN }}
  DUPLO_TENANT: k-dev01
  REPO_NAME: codegenservice
  AWS_REGION: us-west-2
  ECR_REGISTRY: ************.dkr.ecr.us-west-2.amazonaws.com

jobs:
  build:
    if: contains(github.event.head_commit.message, 'codegendeploy')
    runs-on: ubuntu-latest
    outputs:
      backend_image: ************.dkr.ecr.us-west-2.amazonaws.com/${{ env.REPO_NAME }}:${{ github.sha }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      # BUILDX: Setup Docker Buildx for advanced caching
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: |
            image=moby/buildkit:buildx-stable-1
            network=host

      # CACHE: GitHub Actions cache for Docker layers
      - name: Cache Docker layers
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ hashFiles('Dockerfile.dev') }}-${{ hashFiles('requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-buildx-${{ hashFiles('Dockerfile.dev') }}-
            ${{ runner.os }}-buildx-

      # Configure initial AWS credentials (source account)
      - name: Configure AWS credentials (Source Account)
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      # Verify source account identity
      - name: Verify source AWS identity
        run: |
          echo "Source AWS Identity:"
          aws sts get-caller-identity

      # Assume cross-account role for ECR access
      - name: Assume cross-account ECR role
        run: |
          echo "Assuming cross-account role for ECR access..."
          ROLE_ARN="arn:aws:iam::************:role/GitHubActionsECRAccess"
          SESSION_NAME="github-actions-$(date +%s)"
          EXTERNAL_ID="github-actions-ecr-access"
          
          echo "Role ARN: $ROLE_ARN"
          echo "Session Name: $SESSION_NAME"
          echo "External ID: $EXTERNAL_ID"

          # Assume the cross-account role
          echo "Calling assume-role..."
          ASSUME_ROLE_OUTPUT=$(aws sts assume-role \
            --role-arn $ROLE_ARN \
            --role-session-name $SESSION_NAME \
            --external-id $EXTERNAL_ID \
            --duration-seconds 3600 \
            --output json)
          
          if [ $? -eq 0 ]; then
            echo "Role assumption successful"
            
            # Extract credentials from JSON output
            AWS_ACCESS_KEY_ID=$(echo $ASSUME_ROLE_OUTPUT | jq -r '.Credentials.AccessKeyId')
            AWS_SECRET_ACCESS_KEY=$(echo $ASSUME_ROLE_OUTPUT | jq -r '.Credentials.SecretAccessKey')
            AWS_SESSION_TOKEN=$(echo $ASSUME_ROLE_OUTPUT | jq -r '.Credentials.SessionToken')
            
            # Export to environment for subsequent steps
            echo "AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID" >> $GITHUB_ENV
            echo "AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY" >> $GITHUB_ENV
            echo "AWS_SESSION_TOKEN=$AWS_SESSION_TOKEN" >> $GITHUB_ENV
            
            echo "Verifying new identity..."
            export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
            export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
            export AWS_SESSION_TOKEN=$AWS_SESSION_TOKEN
            
            echo "New AWS Identity:"
            aws sts get-caller-identity
            
            echo "Testing ECR repository access:"
            aws ecr describe-repositories --repository-names ${{ env.REPO_NAME }} --region ${{ env.AWS_REGION }}
            
            echo "Testing ECR list images:"
            aws ecr list-images --repository-name ${{ env.REPO_NAME }} --region ${{ env.AWS_REGION }} --max-items 5
            
          else
            echo "❌ Role assumption failed"
            echo "Checking assume-role error..."
            aws sts assume-role \
              --role-arn $ROLE_ARN \
              --role-session-name $SESSION_NAME \
              --external-id $EXTERNAL_ID \
              --duration-seconds 3600 \
              --output json || echo "Assume role command failed"
            echo "Possible issues:"
            echo "1. Role ARN might be incorrect"
            echo "2. External ID might not match"  
            echo "3. Trust relationship not set up properly"
            echo "4. Source account doesn't have AssumeRole permission"
            exit 1
          fi

      # ECR Login with cross-account credentials
      - name: Log in to cross-account ECR
        run: |
          echo "Logging into ECR with cross-account credentials..."
          echo "Current AWS identity:"
          aws sts get-caller-identity
          
          echo "Getting ECR login password..."
          aws ecr get-login-password --region ${{ env.AWS_REGION }} | \
          docker login --username AWS --password-stdin ${{ env.ECR_REGISTRY }}
          
          echo " ECR login successful"

      # Main build with FULL caching
      - name: Build and push Docker image (Full Cache)
        uses: docker/build-push-action@v5
        id: build-and-push-codegenservice
        with:
          context: .
          file: ./Dockerfile.dev
          push: true
          platforms: linux/amd64
          tags: |
            ${{ env.ECR_REGISTRY }}/${{ env.REPO_NAME }}:${{ github.sha }}
            ${{ env.ECR_REGISTRY }}/${{ env.REPO_NAME }}:latest
            ${{ env.ECR_REGISTRY }}/${{ env.REPO_NAME }}:buildcache
          build-args: |
            AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}
            AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}
          cache-from: |
            type=gha
            type=registry,ref=${{ env.ECR_REGISTRY }}/${{ env.REPO_NAME }}:buildcache
          cache-to: |
            type=gha,mode=max
            type=registry,ref=${{ env.ECR_REGISTRY }}/${{ env.REPO_NAME }}:buildcache,mode=max

      # Report build success
      - name: Build completion summary
        run: |
          echo "🎉 Build completed successfully!"
          echo "Image: ${{ env.ECR_REGISTRY }}/${{ env.REPO_NAME }}:${{ github.sha }}"
          echo "Caching: Full Docker + GitHub Actions + Registry cache enabled"

  deploy:
    if: contains(github.event.head_commit.message, 'codegendeploy')
    runs-on: ubuntu-latest
    needs:
      - build
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      # CACHE: Deployment tools cache
      - name: Cache kubectl and deployment tools
        uses: actions/cache@v4
        with:
          path: |
            ~/.kube
            ~/.aws
          key: ${{ runner.os }}-deploy-tools-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-deploy-tools-

      # AWS: Configure AWS for deployment
      - name: Configure AWS credentials for deployment
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      # DUPLO: Setup Duplo for deployment
      - name: Duplo Setup
        uses: duplocloud/actions@main
        with:
          admin: true

      - name: Create Kubeconfig
        run: |
          aws eks update-kubeconfig \
            --name duploinfra-dev \
            --region us-west-2

      # Step 1: Update the Duplo config‑map before touching any pods
      - name: update image-id for codegen in duploctl
        id: new_tag
        env:
          IMAGE: "${{ needs.build.outputs.backend_image }}"
        run: |
          echo "Image being used: $IMAGE"
          GIT_SHA="${{ github.sha }}"
          echo $GIT_SHA
          NEW_IMAGE_TAG=${IMAGE##*:}
          echo "NEW_IMAGE_TAG=$NEW_IMAGE_TAG" >> $GITHUB_OUTPUT

      # CACHE: CodeGen Agent repo cache
      - name: Cache CodeGenerationAgent repo
        uses: actions/cache@v4
        with:
          path: dev-docker-id
          key: ${{ runner.os }}-codegen-agent-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-codegen-agent-

      - name: Checkout CodeGenerationAgent repo
        uses: actions/checkout@v4
        with:
          repository: Kavia-ai/CodeGenerationAgent
          token: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
          path: dev-docker-id

      - name: Extract Kavia-dev-container image tag from docker_version.py
        id: kavia
        run: |
          set -e
          echo "Reading version from docker_version.py"
          KAVIA_IMAGE_TAG=$(grep KAVIA_CONTAINER_VERSION dev-docker-id/src/code_generation_core_agent/docker_version.py | cut -d '"' -f2)
          echo "Kavia tag: $KAVIA_IMAGE_TAG"
          echo "kavia_image_tag=$KAVIA_IMAGE_TAG" >> $GITHUB_OUTPUT

      - name: Update image tags in Duplo configmap -Fix
        env:
          IMAGE: "${{ needs.build.outputs.backend_image }}"
          KAVIA_TAG: "${{ steps.kavia.outputs.kavia_image_tag }}"
          NEW_IMAGE_TAG: "${{ steps.new_tag.outputs.NEW_IMAGE_TAG}}"
        run: |
          set -e

          echo "Codegen tag: $NEW_IMAGE_TAG"
          echo "Kavia tag: $KAVIA_TAG"

          echo "Fetching current configmap"
          duploctl configmap find codegenservicedeploymentdevconfig -o yaml > /tmp/test7.yaml

          CODEGEN_PREFIX="************.dkr.ecr.us-west-2.amazonaws.com/codegenservice"

          echo "Updating codegen image..."
          sed -i -E "s|(${CODEGEN_PREFIX}):[a-zA-Z0-9._-]+|\1:${NEW_IMAGE_TAG}|g" /tmp/test7.yaml

          echo "Updating FLUTTER_IMAGE_TAG env var..."
          # Check current value for debugging
          echo "Current FLUTTER_IMAGE_TAG value:"
          grep -o 'FLUTTER_IMAGE_TAG\\n[ ]*value: \\"[^\\]*\\"' /tmp/test7.yaml || echo "Pattern not found"
          
          # Update FLUTTER_IMAGE_TAG with dynamic pattern matching
          sed -i -E 's/(- name: FLUTTER_IMAGE_TAG\\n[ ]+value: \\")[^\\"]+/\1'"${KAVIA_TAG}"'/g' /tmp/test7.yaml
          
          echo "Verifying update..."
          grep -o 'FLUTTER_IMAGE_TAG\\n[ ]*value: \\"[^\\]*\\"' /tmp/test7.yaml || echo "Updated pattern not found"

          echo "Final configmap after update:"
          cat /tmp/test7.yaml

          echo "Applying update to Duplo configmap..."
          duploctl configmap update codegenservicedeploymentdevconfig -f /tmp/test7.yaml

          # NEW: Extract the updated template for deployment use
          echo "Extracting updated deployment template for two-wave deployment..."
          kubectl get configmap codegenservicedeploymentdevconfig -n duploservices-k-dev01 \
            -o jsonpath='{.data.codegenservice-deployment\.yaml}' > /tmp/deployment_template.yaml
          
          echo "ConfigMap updated and template extracted"

      # Step 2: Two-Wave Grace-Deployment - Test
      - name: Deploy to DEV environment (Optimized Two-Wave)
        env:
          IMAGE: "${{ needs.build.outputs.backend_image }}"
        run: |
          echo "Starting Two-Wave deployment with ConfigMap template updates"
          echo "Image being used: $IMAGE"
          GIT_SHA="${{ github.sha }}"
          NAMESPACE=duploservices-k-dev01
          SERVICE=codegen
          FIRST_WAVE_PERCENTAGE=50  # Update 50% in first wave
          WAIT_TIME=300             # Wait for first wave to be ready
          
          # Optimization settings for dev environment
          EXEC_TIMEOUT=5            # Max 5 seconds for kubectl exec
          MAX_PODS=30               # Limit pod processing (dev has fewer pods)
          PROGRESS_INTERVAL=3       # Show progress every 3 pods

          # NEW: Function to apply complete ConfigMap template to deployment
          apply_configmap_template() {
            local deployment="$1"
            local wave_name="$2"
            
            # Extract PROJECT_ID and ENV_NAME from deployment name
            local PROJECT_ID=$(echo "$deployment" | cut -d'-' -f1)
            local ENV_NAME=$(echo "$deployment" | cut -d'-' -f2)
            
            echo "$wave_name Applying complete template to $deployment (PROJECT_ID=$PROJECT_ID, ENV_NAME=$ENV_NAME)"
            echo "    This updates: Container image + FLUTTER_IMAGE_TAG + all other configs"
            
            # Apply the ConfigMap template with variable substitution
            if sed "s/{{PROJECT_ID}}/$PROJECT_ID/g; s/{{ENV_NAME}}/$ENV_NAME/g" /tmp/deployment_template.yaml | kubectl apply -f - 2>/dev/null; then
              echo "$wave_name Complete template applied to $deployment"
              return 0
            else
              echo "$wave_name Failed to apply template to $deployment"
              return 1
            fi
          }

          # [All your existing deployment logic remains exactly the same]
          # Fast pod availability check function
          is_pod_ready_for_exec() {
            local pod_name="$1"
            local base_name="$2"
            
            # Quick readiness test - if this fails, skip expensive screen check
            if ! timeout 3s kubectl exec "$pod_name" -c "$base_name" -n $NAMESPACE -- echo "ready" >/dev/null 2>&1; then
              echo "Pod $pod_name not responding to exec - skipping"
              return 1
            fi
            return 0
          }

          # Function to check if pod is still available for update (with timeout)
          is_pod_available() {
            local pod_name="$1"
            local pod_full_name
            
            # Find the current pod with this deployment name
            pod_full_name=$(kubectl get pods -l service=$SERVICE -n $NAMESPACE --no-headers 2>/dev/null | grep "^${pod_name}-" | head -1 | awk '{print $1}')
            
            if [[ -z "$pod_full_name" ]]; then
              echo "Pod $pod_name not found"
              return 1
            fi
            
            # Check if pod is still running
            local status
            status=$(kubectl get pod "$pod_full_name" -n $NAMESPACE --no-headers 2>/dev/null | awk '{print $3}')
            if [[ "$status" != "Running" ]]; then
              echo "Pod $pod_name is not running (status: $status)"
              return 1
            fi
            
            # Quick exec test first
            if ! is_pod_ready_for_exec "$pod_full_name" "$pod_name"; then
              return 1
            fi
            
            # Check for screen sessions with timeout
            if timeout $EXEC_TIMEOUT kubectl exec "$pod_full_name" -c "$pod_name" -n $NAMESPACE -- screen -ls 2>/dev/null | grep -q 'Detached\|Attached'; then
              echo "Pod $pod_name has active screen session - skipping"
              CONFIGMAP_NAME="pod-status-${pod_name}"
              timeout 3s kubectl patch configmap "$CONFIGMAP_NAME" -n $NAMESPACE \
                --type merge \
                -p "{\"data\": {\"deployment-status\": \"not-up-to-date\", \"image-sha\": \"${GIT_SHA}\"}}" \
                2>/dev/null || echo "Warning: ConfigMap update timed out for $CONFIGMAP_NAME"
              return 1
            elif [ $? -eq 124 ]; then
              echo "Screen check timed out for $pod_name - assuming available"
              return 0
            fi
            
            return 0
          }

          echo "Starting Optimized Pod Discovery for Dev Environment..."
          echo "Configuration: max $MAX_PODS pods, $EXEC_TIMEOUT second timeouts"

          # Fast initial pod collection with timeouts and limits
          AVAILABLE_PODS=()
          POD_COUNT=0
          SKIPPED_COUNT=0
          
          while IFS= read -r line; do
            if [[ -z "$line" ]]; then
              continue
            fi
            
            POD_COUNT=$((POD_COUNT + 1))
            
            # Prevent processing too many pods
            if [ "$POD_COUNT" -gt "$MAX_PODS" ]; then
              echo "Reached maximum pod processing limit ($MAX_PODS). Proceeding with available pods."
              break
            fi
            
            read -r POD READY STATUS RESTARTS AGE <<< "$line"
            
            # Progress indicator
            if [ $((POD_COUNT % PROGRESS_INTERVAL)) -eq 0 ]; then
              echo "Progress: Processed $POD_COUNT pods, found ${#AVAILABLE_PODS[@]} available, skipped $SKIPPED_COUNT"
            fi
            
            if [[ "$STATUS" != "Running" ]]; then
              echo "Pod $POD_COUNT: $POD - not running ($STATUS)"
              SKIPPED_COUNT=$((SKIPPED_COUNT + 1))
              continue
            fi

            # Skip zoltan-dev pods
            if [[ "$POD" =~ ^zoltan-dev- ]]; then
              echo "Pod $POD_COUNT: Skipping zoltan-dev pod: $POD"
              SKIPPED_COUNT=$((SKIPPED_COUNT + 1))
              continue
            fi

            BASE=$(echo "$POD" | sed -E 's/-[a-z0-9]+-[a-z0-9]+$//')
            echo "Pod $POD_COUNT: Checking $POD ($BASE)"

            # Quick exec readiness test
            if ! is_pod_ready_for_exec "$POD" "$BASE"; then
              SKIPPED_COUNT=$((SKIPPED_COUNT + 1))
              continue
            fi

            # Screen session check with timeout
            HAS_SCREEN=false
            if timeout $EXEC_TIMEOUT kubectl exec "$POD" -c "$BASE" -n $NAMESPACE -- screen -ls 2>/dev/null | grep -q 'Detached\|Attached'; then
              HAS_SCREEN=true
            elif [ $? -eq 124 ]; then
              echo "Screen check timed out for $POD - assuming no session"
              HAS_SCREEN=false
            fi

            if [ "$HAS_SCREEN" = true ]; then
              echo "Screen session detected in $POD - skipping"
              CONFIGMAP_NAME="pod-status-${BASE}"
              timeout 3s kubectl patch configmap "$CONFIGMAP_NAME" -n $NAMESPACE \
                --type merge \
                -p "{\"data\": {\"deployment-status\": \"not-up-to-date\", \"image-sha\": \"${GIT_SHA}\"}}" \
                2>/dev/null || echo "ConfigMap update timed out for $CONFIGMAP_NAME"
              SKIPPED_COUNT=$((SKIPPED_COUNT + 1))
              continue
            fi

            AVAILABLE_PODS+=("$BASE")
            echo "Added $BASE to available pods (${#AVAILABLE_PODS[@]} total)"
            
          done < <(kubectl get pods -l service=$SERVICE -n $NAMESPACE --no-headers 2>/dev/null)

          TOTAL_INITIAL=${#AVAILABLE_PODS[@]}
          echo
          echo "Pod Discovery Completed Successfully!"
          echo "Results: $TOTAL_INITIAL available pods from $POD_COUNT total ($SKIPPED_COUNT skipped)"

          # Safety check - need at least 2 pods for two-wave strategy
          if [ "$TOTAL_INITIAL" -lt 2 ]; then
            echo "Not enough pods ($TOTAL_INITIAL) for two-wave deployment. Need at least 2 pods."
            exit 1
          fi

          echo
          echo "Starting Two-Wave Deployment Strategy for Dev Environment"
          echo "Strategy: Update 50% first (create buffer), then update remaining 50%"
          echo "Total pods to update: $TOTAL_INITIAL"

          # ====================================================================
          # WAVE 1: Update 50% of pods to create buffer
          # ====================================================================
          echo
          echo "=== WAVE 1: Creating Buffer ==="
          
          FIRST_WAVE_SIZE=$((TOTAL_INITIAL * FIRST_WAVE_PERCENTAGE / 100))
          # Ensure at least 1 pod in first wave
          if [ "$FIRST_WAVE_SIZE" -lt 1 ]; then
            FIRST_WAVE_SIZE=1
          fi
          
          echo "Wave 1: Updating $FIRST_WAVE_SIZE pods ($FIRST_WAVE_PERCENTAGE% of $TOTAL_INITIAL) to create buffer"
          
          WAVE1_UPDATED_PODS=()
          WAVE1_SKIPPED_PODS=()
          
          # Update first wave with real-time checks
          for i in $(seq 0 $((FIRST_WAVE_SIZE - 1))); do
            if [ $i -lt ${#AVAILABLE_PODS[@]} ]; then
              DEPLOYMENT=${AVAILABLE_PODS[$i]}
              echo "Real-time check for $DEPLOYMENT..."
              
              if is_pod_available "$DEPLOYMENT"; then
                echo "Updating deployment/$DEPLOYMENT (Wave 1: $((i + 1))/$FIRST_WAVE_SIZE)"
                
                # NEW: Apply complete ConfigMap template instead of just updating image
                if apply_configmap_template "$DEPLOYMENT" "Wave 1"; then
                  echo "  Complete template applied to $DEPLOYMENT"
                  WAVE1_UPDATED_PODS+=("$DEPLOYMENT")
                else
                  echo "  Failed to apply template to $DEPLOYMENT"
                  WAVE1_SKIPPED_PODS+=("$DEPLOYMENT")
                fi
              else
                echo "  Pod $DEPLOYMENT no longer available - skipped for user safety"
                WAVE1_SKIPPED_PODS+=("$DEPLOYMENT")
              fi
            fi
          done
          
          echo "Wave 1 initiated: ${#WAVE1_UPDATED_PODS[@]} pods updating, ${#WAVE1_SKIPPED_PODS[@]} pods skipped"
          

          # Wait for Wave 1 to complete
          if [ ${#WAVE1_UPDATED_PODS[@]} -gt 0 ]; then
            echo "Waiting $WAIT_TIME seconds for Wave 1 pods to become ready..."
            sleep $WAIT_TIME
            
            # Check readiness of Wave 1 pods
            WAVE1_READY_COUNT=0
            for deployment in "${WAVE1_UPDATED_PODS[@]}"; do
              echo "Checking readiness of $deployment..."
              if timeout 180s kubectl rollout status deployment/$deployment -n $NAMESPACE; then
                echo "  $deployment is ready"
                WAVE1_READY_COUNT=$((WAVE1_READY_COUNT + 1))
              else
                echo "  Warning: $deployment rollout may still be in progress"
                WAVE1_READY_COUNT=$((WAVE1_READY_COUNT + 1))  # Count as ready for buffer purposes
              fi
            done
            
            echo "Wave 1 completed: $WAVE1_READY_COUNT/${#WAVE1_UPDATED_PODS[@]} pods ready and serving as buffer"
          else
            echo "No pods were updated in Wave 1. Cannot proceed with Wave 2."
            exit 1
          fi

          # ====================================================================
          # WAVE 2: Update ALL remaining pods (they have buffer from Wave 1)
          # ====================================================================
          echo
          echo "=== WAVE 2: Update Remaining Pods ==="
          
          # Calculate remaining pods (excluding Wave 1 updated and skipped pods)
          REMAINING_PODS=()
          for pod in "${AVAILABLE_PODS[@]}"; do
            if [[ ! " ${WAVE1_UPDATED_PODS[@]} " =~ " ${pod} " ]] && [[ ! " ${WAVE1_SKIPPED_PODS[@]} " =~ " ${pod} " ]]; then
              REMAINING_PODS+=("$pod")
            fi
          done
          
          REMAINING_COUNT=${#REMAINING_PODS[@]}
          echo "Wave 2: Updating ALL remaining $REMAINING_COUNT pods"
          echo "Buffer: ${#WAVE1_UPDATED_PODS[@]} pods from Wave 1 are ready to handle requests"
          
          WAVE2_UPDATED_PODS=()
          WAVE2_SKIPPED_PODS=()
          
          # Update ALL remaining pods with real-time checks
          for pod in "${REMAINING_PODS[@]}"; do
            echo "Real-time check for $pod..."
            
            if is_pod_available "$pod"; then
              echo "Updating deployment/$pod (Wave 2)"
              
              # NEW: Apply complete ConfigMap template instead of just updating image
              if apply_configmap_template "$pod" "Wave 2"; then
                echo "  Complete template applied to $pod"
                WAVE2_UPDATED_PODS+=("$pod")
              else
                echo "  Failed to apply template to $pod"
                WAVE2_SKIPPED_PODS+=("$pod")
              fi
            else
              echo "  Pod $pod no longer available - skipped for user safety"
              WAVE2_SKIPPED_PODS+=("$pod")
            fi
          done
          
          echo "Wave 2 initiated: ${#WAVE2_UPDATED_PODS[@]} pods updating, ${#WAVE2_SKIPPED_PODS[@]} pods skipped"
          
          # Wait for Wave 2 to complete
          if [ ${#WAVE2_UPDATED_PODS[@]} -gt 0 ]; then
            echo "Waiting $WAIT_TIME seconds for Wave 2 pods to become ready..."
            sleep $WAIT_TIME
            
            # Check readiness of Wave 2 pods
            WAVE2_READY_COUNT=0
            for deployment in "${WAVE2_UPDATED_PODS[@]}"; do
              echo "Checking readiness of $deployment..."
              if timeout 180s kubectl rollout status deployment/$deployment -n $NAMESPACE; then
                echo "  $deployment is ready"
                WAVE2_READY_COUNT=$((WAVE2_READY_COUNT + 1))
              else
                echo "  Warning: $deployment rollout may still be in progress"
                WAVE2_READY_COUNT=$((WAVE2_READY_COUNT + 1))  # Count as ready
              fi
            done
            
            echo "Wave 2 completed: $WAVE2_READY_COUNT/${#WAVE2_UPDATED_PODS[@]} pods ready"
          fi

          # ====================================================================
          # DEPLOYMENT SUMMARY
          # ====================================================================
          echo
          echo "Docker Buildx Two-Wave Dev Deployment Completed!"
          echo "Final Summary:"
          echo "  - Initial available pods: $TOTAL_INITIAL"
          echo "  - Wave 1 updated: ${#WAVE1_UPDATED_PODS[@]} pods"
          echo "  - Wave 2 updated: ${#WAVE2_UPDATED_PODS[@]} pods" 
          echo "  - Total updated: $((${#WAVE1_UPDATED_PODS[@]} + ${#WAVE2_UPDATED_PODS[@]}))"
          echo "  - Total skipped (user protection): $((${#WAVE1_SKIPPED_PODS[@]} + ${#WAVE2_SKIPPED_PODS[@]}))"
          echo "  - Update percentage: $(( (${#WAVE1_UPDATED_PODS[@]} + ${#WAVE2_UPDATED_PODS[@]}) * 100 / TOTAL_INITIAL ))%"
          echo "  - Strategy: Docker Buildx with advanced caching"
          
          TOTAL_UPDATED=$((${#WAVE1_UPDATED_PODS[@]} + ${#WAVE2_UPDATED_PODS[@]}))
          if [ "$TOTAL_UPDATED" -eq "$TOTAL_INITIAL" ]; then
            echo "SUCCESS: ALL INITIALLY AVAILABLE DEV PODS UPDATED WITH BUILDX OPTIMIZATION!"
          else
            echo "Status: $TOTAL_UPDATED/$TOTAL_INITIAL pods updated"
            echo "Note: Some pods were protected from update due to active usage or timeouts"
          fi

          # Final verification
          echo
          echo "Final Verification:"
          timeout 30s kubectl get pods -l service=$SERVICE -n $NAMESPACE -o custom-columns="NAME:.metadata.name,IMAGE:.spec.containers[0].image,STATUS:.status.phase" || echo "Verification timed out"
          
          echo
          echo "Image summary:"
          timeout 15s kubectl get pods -l service=$SERVICE -n $NAMESPACE -o jsonpath='{.items[*].spec.containers[0].image}' 2>/dev/null | tr ' ' '\n' | cut -d: -f2 | sort | uniq -c || echo "Image summary timed out"

          # NEW: Additional verification for Flutter image tags
          echo
          echo "Flutter image tag summary:"
          timeout 15s kubectl get pods -l service=$SERVICE -n $NAMESPACE --field-selector=status.phase=Running \
            -o custom-columns="NAME:.metadata.name,FLUTTER:.spec.containers[0].env[?(@.name=='FLUTTER_IMAGE_TAG')].value" 2>/dev/null | \
            head -10 || echo "Flutter tag verification timed out"

  notify:
    name: Slack Notification
    runs-on: ubuntu-latest
    needs:
      - build
      - deploy
    if: always() && needs.build.result != 'skipped' && needs.deploy.result != 'skipped'
    steps:
      - name: Debug Job Results
        run: |
          echo "build.result  = '${{ needs.build.result }}'"
          echo "deploy.result = '${{ needs.deploy.result }}'"

      - name: Slack Deployment Succeeded
        if: ${{ needs.build.result == 'success' && needs.deploy.result == 'success' }}
        run: |
          curl -X POST \
            -H 'Content-type: application/json' \
            --data "{\"text\":\"✅ *Code Generation deployment complete* on branch \`${{ github.ref_name }}\` in *${{ github.repository }}* using Docker Buildx optimization.\"}" \
            ${{ secrets.SLACK_HOOK_DEV_DEPLOY }}

      - name: Slack Deployment Failed
        if: ${{ needs.build.result == 'failure' || needs.deploy.result == 'failure' }}
        run: |
          curl -X POST \
            -H 'Content-type: application/json' \
            --data "{\"text\":\"🚨 *Code Generation deployment failed* on branch \`${{ github.ref_name }}\` in *${{ github.repository }}*.\nView logs: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}\"}" \