# Simple Manifest Loop Prevention

## Overview
This is a simplified approach to prevent and monitor manifest generation loops without complex rate limiting that could break the application.

## What's Implemented

### 1. Simple Request Logging
- **Location**: `app/routes/manifest_route.py`
- **Behavior**: 
  - Logs all manifest generation requests with request count
  - Warns when more than 5 requests are made for the same project/user
  - No blocking or rate limiting - just monitoring

### 2. Monitoring Script
- **File**: `monitor_manifest.py`
- **Features**:
  - Monitors manifest route logs for repeated requests
  - Checks for high CPU processes
  - Auto-kills processes with >80% CPU usage
  - Real-time monitoring with 10-second intervals

### 3. Stop Script
- **File**: `stop_loop.sh`
- **Features**:
  - Kills all uvicorn processes
  - Kills high CPU Python processes
  - Kills processes on port 8000
  - Simple and reliable

## How to Use

### Stop Current Loops
```bash
# Use the stop script
wsl bash -c "./stop_loop.sh"

# Or use the Python script
python monitor_manifest.py kill
```

### Monitor for Loops
```bash
# Start monitoring
python monitor_manifest.py monitor
```

### Check Logs Manually
```bash
# Check manifest logs
wsl bash -c "tail -f logs/manifest_route.log"

# Check for repeated requests
wsl bash -c "grep 'Multiple requests detected' logs/manifest_route.log"
```

## What to Look For

### In Logs
- `Multiple requests detected` - Indicates potential loop
- High request counts for same project/user
- Repeated requests in short time periods

### In Process Monitoring
- High CPU usage (>80%)
- Multiple uvicorn processes
- Processes stuck in loops

## Emergency Procedures

### 1. Immediate Stop
```bash
wsl bash -c "./stop_loop.sh"
```

### 2. Restart Application
```bash
# Stop all processes
wsl bash -c "./stop_loop.sh"

# Restart application
wsl bash -c "cd /home/<USER>/graphnode-backend-api && source pyvenv/bin/activate && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
```

### 3. Monitor and Auto-Kill
```bash
# Start monitoring with auto-kill
python monitor_manifest.py monitor
```

## Advantages of This Approach

1. **Non-intrusive**: Doesn't break existing authentication
2. **Simple**: Easy to understand and maintain
3. **Reliable**: Uses proven process management techniques
4. **Informative**: Provides clear logging and monitoring
5. **Safe**: No complex rate limiting that could cause issues

## Troubleshooting

### If Loops Still Occur
1. Check the logs: `tail -f logs/manifest_route.log`
2. Use the stop script: `wsl bash -c "./stop_loop.sh"`
3. Monitor with the Python script: `python monitor_manifest.py monitor`
4. Check for frontend issues causing repeated requests

### If Application Won't Start
1. Make sure no processes are using port 8000
2. Check for zombie processes
3. Restart WSL if needed

## Best Practices

1. **Monitor regularly**: Use the monitoring script during development
2. **Check logs**: Look for patterns of repeated requests
3. **Stop early**: Use the stop script as soon as you notice issues
4. **Restart cleanly**: Always stop processes before restarting

This approach provides effective loop prevention without the complexity that could break your application.
