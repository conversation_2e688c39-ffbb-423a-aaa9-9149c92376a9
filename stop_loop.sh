#!/bin/bash

echo "Stopping manifest generation loops..."

# Kill uvicorn processes
echo "Killing uvicorn processes..."
pkill -f "uvicorn.*app.main:app" 2>/dev/null || echo "No uvicorn processes found"

# Kill any Python processes with high CPU usage (potential loops)
echo "Killing high CPU Python processes..."
ps aux | grep python | grep -v grep | awk '$3 > 50 {print $2}' | xargs -r kill -9 2>/dev/null || echo "No high CPU processes found"

# Kill any processes listening on port 8000
echo "Killing processes on port 8000..."
lsof -ti:8000 | xargs -r kill -9 2>/dev/null || echo "No processes on port 8000"

# Check if any processes are still running
echo "Checking remaining processes..."
ps aux | grep -E "(uvicorn|python.*app.main)" | grep -v grep

echo "Done!"
